<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">基础信息</span>
      <!-- <div class="header-right">
        <el-button
          type="primary"
          size="mini"
          @click="exportBasicWorkOrderInfo"
          v-loading.fullscreen.lock="exportLoading"
          >导出Excel表</el-button
        >
      </div> -->
    </div>
    <div class="content">
      <el-descriptions title="工单基本信息" class="descriptions">
        <el-descriptions-item label="所属专业">{{
          basicWorkOrderData.professionalType
        }}</el-descriptions-item>
        <el-descriptions-item label="网络类型">{{
          basicWorkOrderData.networkType
        }}</el-descriptions-item>
        <el-descriptions-item label="告警地市">{{
          basicWorkOrderData.regionName
        }}</el-descriptions-item>
        <el-descriptions-item label="告警区县">{{
          basicWorkOrderData.cityName
        }}</el-descriptions-item>
        <!-- <el-descriptions-item label="网格名称">
          {{ basicWorkOrderData.gridName }}
        </el-descriptions-item> -->
        <el-descriptions-item label="预判故障等级">
          {{ basicWorkOrderData.faultLevel }}
        </el-descriptions-item>
        <!-- 移除经度、纬度、物业联系人、物业联系电话、工单优先级字段 -->
        <el-descriptions-item label="业务中断">
          {{ basicWorkOrderData.businessDown }}
        </el-descriptions-item>
        <el-descriptions-item label="故障现象" :span="1">
          <text-collapse
            :text="basicWorkOrderData.faultPhenomenon"
            :max-lines="2"
          ></text-collapse>
        </el-descriptions-item>
        <el-descriptions-item label="覆盖场景">{{
          basicWorkOrderData.coverScene
        }}</el-descriptions-item>
        <!-- 新增字段：附件 -->
        <el-descriptions-item label="附件" v-if="attachmentArr && attachmentArr.length > 0">
          <el-button type="text" @click="queryAttachmentList">
            查看附件({{ attachmentArr.length }})
          </el-button>
        </el-descriptions-item>
        <!-- 新增字段：业务影响范围列表 -->
        <el-descriptions-item label="业务影响范围列表" v-if="impactServiceArr && impactServiceArr.length > 0">
          <el-button type="text" @click="queryImpactServiceList">
            查看影响范围({{ impactServiceArr.length }})
          </el-button>
        </el-descriptions-item>
        <!-- 新增字段：派单方工单编号（仅电信时显示） -->
        <el-descriptions-item label="派单方工单编号" v-if="isTelecom">{{
          basicWorkOrderData.dispatchOrderNo || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="3">{{
          basicWorkOrderData.faultComment
        }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="专业故障信息" class="descriptions">
        <el-descriptions-item label="基站名称" :span="1">{{
          basicWorkOrderData.siteName
        }}</el-descriptions-item>
        <el-descriptions-item label="基站地址" :span="1">{{
          basicWorkOrderData.siteAddress
        }}</el-descriptions-item>
        <el-descriptions-item label="基站级别" :span="1">{{
          basicWorkOrderData.siteLevel
        }}</el-descriptions-item>
        <el-descriptions-item label="基站编号" :span="1">{{
          basicWorkOrderData.siteCode
        }}</el-descriptions-item>
        <el-descriptions-item label="规模退服" :span="1">{{
          basicWorkOrderData.scaleWithdrawal
        }}</el-descriptions-item>
        <!-- 新增字段 -->
        <el-descriptions-item label="基站类型" :span="1">{{
          baseStationTypeText
        }}</el-descriptions-item>
        <el-descriptions-item label="站址名称" :span="1">{{
          basicWorkOrderData[FIELD_NAMES.siteNameNew] || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="站址经纬度" :span="1">{{
          basicWorkOrderData[FIELD_NAMES.siteCoordinates] || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="站址类别" :span="1">{{
          siteCategoryText
        }}</el-descriptions-item>
        <el-descriptions-item label="所属区域" :span="1">{{
          basicWorkOrderData[FIELD_NAMES.belongingArea] || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="所属场景" :span="1">{{
          belongingSceneText
        }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="工单派送信息" class="descriptions">
        <el-descriptions-item label="主送">
          {{
            stitchingAlgorithm(
              basicWorkOrderData.agentDeptName,
              basicWorkOrderData.agentMan
            )
          }}
        </el-descriptions-item>
        <!-- 次送人字段：仅在建单方为联通时显示 -->
        <el-descriptions-item label="次送" v-if="isUnicom">
          {{
            stitchingAlgorithm(
              basicWorkOrderData.acceptDeptName,
              basicWorkOrderData.acceptMan
            )
          }}
        </el-descriptions-item>
        <el-descriptions-item label="抄送">
          {{
            stitchingAlgorithm(
              basicWorkOrderData.copyDeptName,
              basicWorkOrderData.copyMan
            )
          }}
        </el-descriptions-item>
        <!-- 短信通知字段：仅在建单方为联通时显示 -->
        <el-descriptions-item label="短信通知" v-if="isUnicom">{{
          basicWorkOrderData.isSendSms == 1 ? "是" : "否"
        }}</el-descriptions-item>
        <!-- 短信通知对象字段：仅在建单方为联通时显示 -->
        <el-descriptions-item
          v-if="isUnicom && basicWorkOrderData.isSendSms == 1"
          label="短信通知对象"
          >{{
            stitchingAlgorithm(
              basicWorkOrderData.smsToDeptName,
              basicWorkOrderData.smsToUsername
            )
          }}</el-descriptions-item
        >
        <!-- 接收人字段：在所有情况下都隐藏（暂时隐藏） -->
        <!-- <el-descriptions-item
          v-if="basicWorkOrderData.isSendSms == 1"
          label="接收人"
          >{{
            stitchingAlgorithm(
              basicWorkOrderData.smsToDeptName,
              basicWorkOrderData.smsToUsername
            )
          }}</el-descriptions-item> -->
        <el-descriptions-item
          v-if="isUnicom && basicWorkOrderData.isSendSms == 1"
          label="发送内容"
          :span="3"
          >{{ basicWorkOrderData.sendContent }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="impactServiceVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="impactServiceVisible = false"
        :attachmentArr="impactServiceArr"
      ></file-download>
    </el-dialog>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="attachmentVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="attachmentVisible = false"
        :attachmentArr="attachmentArr"
      ></file-download>
    </el-dialog>
  </el-card>
</template>

<script>
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";
import {
  apiExportWorkOrder,
  apiDownloadManualFile,
  apifluenceExcel,
} from "../api/CommonApi";
import { apiFileDownload } from "../api/CommonApi";
import {
  FIELD_NAMES,
  BASE_STATION_TYPE_OPTIONS,
  SITE_CATEGORY_OPTIONS,
  BELONGING_SCENE_OPTIONS
} from "../../config/additionalFields";
// import FileDownload from "@plugin/backbone/components/common/FileDownload.vue";
export default {
  components: {
    TextCollapse,
    // FileDownload,
  },
  props: {
    basicWorkOrderData: Object,
    woId: String,
    workItemId: [String, Number],
  },
  name: "BaseInfo",
  data() {
    return {
      builderZs: "",
      builderCs: "",
      notifier: "",
      recipient: "",
      exportLoading: false,
      impactServiceArr: [],
      impactServiceVisible: false,
      attachmentArr: [],
      attachmentVisible: false,
      manualFileName: "电路信息",
      OpticCableName: "影响系统信息",
      OpticCableLoading: false,
      manualFileFullscreenLoading: false,
      appendixFileLoading: false,
      professionalType: "",
      // 添加FIELD_NAMES到data中，使模板可以访问
      FIELD_NAMES,
    };
  },
  computed: {
    // 获取建单方类型
    faultHandler() {
      return this.basicWorkOrderData && this.basicWorkOrderData[this.FIELD_NAMES.faultHandler] || 'unicom';
    },
    // 是否为电信建单方
    isTelecom() {
      return this.faultHandler === 'telecom';
    },
    // 是否为联通建单方
    isUnicom() {
      return this.faultHandler === 'unicom';
    },
    // 获取基站类型显示文本
    baseStationTypeText() {
      if (!this.basicWorkOrderData) return '';
      const option = BASE_STATION_TYPE_OPTIONS.find(opt =>
        opt.value === this.basicWorkOrderData[this.FIELD_NAMES.baseStationType]
      );
      return option ? option.label : '';
    },
    // 获取站址类别显示文本
    siteCategoryText() {
      if (!this.basicWorkOrderData) return '';
      const option = SITE_CATEGORY_OPTIONS.find(opt =>
        opt.value === this.basicWorkOrderData[this.FIELD_NAMES.siteCategory]
      );
      return option ? option.label : '';
    },
    // 获取所属场景显示文本
    belongingSceneText() {
      if (!this.basicWorkOrderData) return '';
      const option = BELONGING_SCENE_OPTIONS.find(opt =>
        opt.value === this.basicWorkOrderData[this.FIELD_NAMES.belongingScene]
      );
      return option ? option.label : '';
    },
  },
  mounted() {
    if (this.basicWorkOrderData.appendixFileUrl) {
      this.impactServiceArr = JSON.parse(
        this.basicWorkOrderData.appendixFileUrl
      );
    }
    if (this.basicWorkOrderData.appendix) {
      this.attachmentArr = JSON.parse(this.basicWorkOrderData.appendix);
    }
  },
  methods: {
    exportBasicWorkOrderInfo() {
      this.exportLoading = true;
      let param = {
        woId: this.woId,
        workItemId: this.workItemId,
      };
      apiExportWorkOrder(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("导出成功");
          } else {
            this.$message.error("导出失败");
          }
          this.exportLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("导出失败");
          this.exportLoading = false;
        });
    },
    queryImpactServiceList() {
      this.impactServiceVisible = true;
    },
    queryAttachmentList() {
      this.attachmentVisible = true;
    },
    //电缆附件下载
    downloadOpticCable() {
      this.OpticCableLoading = true;
      let param = {
        param1: JSON.stringify({
          woId: this.woId,
        }),
      };
      apifluenceExcel(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.success("文件下载失败");
          }
          this.OpticCableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.OpticCableLoading = false;
        });
    },
    // downloadAppendixFile(data) {
    //   this.appendixFileLoading = true;
    //   let param = {
    //     attId: data.id,
    //   };
    //   apiDownloadAppendixFile(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.$message.success("文件下载成功");
    //       } else {
    //         this.$message.error("文件下载失败");
    //       }
    //       this.appendixFileLoading = false;
    //     })
    //     .catch(error => {
    //       console.log(error);
    //       this.$message.error("文件下载失败");
    //       this.appendixFileLoading = false;
    //     });
    // },
    downloadManualFile() {
      this.manualFileFullscreenLoading = true;
      let param = {
        param1: JSON.stringify({
          woId: this.woId,
        }),
      };
      apiDownloadManualFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.success("文件下载失败");
          }
          this.manualFileFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.manualFileFullscreenLoading = false;
        });
    },
    stitchingAlgorithm(orgName, userName) {
      if (
        null != orgName &&
        orgName.length !== 0 &&
        null != userName &&
        userName.length !== 0
      ) {
        return orgName + "," + userName;
      } else {
        if (null != orgName && orgName.length !== 0) {
          return orgName;
        } else if (null != userName && userName.length !== 0) {
          return userName;
        }
      }
    },
    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
::v-deep .el-descriptions-item__content {
  position: relative;
  word-break: break-all;
  padding-right: 8px;
}
::v-deep .el-descriptions-item__cell {
  vertical-align: top;
}
</style>
