<template>
  <head-content-layout class="page-wrap">
    <template #header>
      <el-form
        label-width="100px"
        :inline="false"
        class="demo-form-inline"
        label-position="right"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="5">
            <el-form-item label-width="0px">
              <el-input
                v-model.trim="form.keyword"
                placeholder="请输入工单主题或工单编号关键字"
                style="width: 100%"
                clearable
              >
              </el-input>
            </el-form-item>
          </el-col>
          <template v-if="form.type != 'gt'">
            <el-col :span="5">
              <el-form-item label="工单状态：">
                <dict-select
                  :value.sync="form.status"
                  :dictId="dictId"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <template v-if="form.type == 'sfty'">
              <el-col :span="5">
                <el-form-item label="紧急程度：">
                  <dict-select
                    :value.sync="form.urgencyDegree"
                    :dictId="10300"
                    placeholder="请选择内容"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </template>
            <template v-else-if="form.type == 'wxw'">
              <el-col :span="4">
                <el-form-item label="紧急程度：">
                  <dict-select
                    :value.sync="form.urgencyDegree"
                    :dictId="10001"
                    placeholder="请选择内容"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="4">
                <el-form-item label="覆盖场景：">
                  <dict-select
                    :value.sync="form.coverScene"
                    :dictId="81002"
                    placeholder="请选择内容"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col> -->
            </template>
            <template v-else>
              <el-col :span="5">
                <el-form-item label="紧急程度：">
                  <dict-select
                    :value.sync="form.urgencyDegree"
                    :dictId="10001"
                    placeholder="请选择内容"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="5">
              <el-form-item label="建单时间：">
                <el-date-picker
                  v-model="form.createTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'gt'">
            <el-col :span="5">
              <el-form-item label="紧急程度：">
                <dict-select
                  :value.sync="form.urgencyDegree"
                  :dictId="10200"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="工单状态：">
                <dict-select
                  :value.sync="form.status"
                  :dictId="dictId"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="操作时间：">
                <el-date-picker
                  v-model="form.operateTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </template>
          <template
            v-if="
              form.type != 'commonFlow' &&
              form.type != 'sfty' &&
              form.type != 'core' &&
              form.type != 'ip' &&
              form.type != 'pt'
            "
          >
            <el-col :span="1">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
            </el-col>
          </template>
          <template
            v-if="
              form.type == 'commonFlow' ||
              form.type == 'sfty' ||
              form.type == 'core' ||
              form.type == 'ip' ||
              form.type == 'pt'
            "
          >
            <el-col v-if="!showMoreSearch" :span="3">
              <el-form-item label-width="0px">
                <el-button type="primary" @click="seniorQuery">筛选</el-button>
                <el-button
                  type="text"
                  style="color: #606266"
                  v-if="!showMoreSearch"
                  @click="onSwitchMoreSearch"
                  >更多
                  <i class="el-icon-caret-bottom" style="color: #b50b14"></i
                ></el-button>
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'sfty' && showMoreSearch">
            <el-col :span="5">
              <el-form-item label="故障专业：">
                <dict-select
                  :value.sync="form.professionalType"
                  :dictId="810002"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="工单来源：">
                <dict-select
                  :value.sync="form.source"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="10003"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5"> </el-col>
            <el-col :span="5"> </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </template>
          <template v-if="form.type == 'commonFlow' && showMoreSearch">
            <el-col :span="5">
              <el-form-item label="故障专业：">
                <dict-select
                  :value.sync="form.professionalType"
                  :dictId="60012"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="工单来源：">
                <dict-select
                  :value.sync="form.source"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="10003"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="建单人：">
                <el-input
                  v-model="form.createUser"
                  style="width: 100%"
                  readonly
                >
                  <el-button
                    type="info"
                    slot="append"
                    icon="el-icon-user"
                    @click="onOpenPeopleDialog('builderDetermine')"
                  ></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="预估处理时限:">
                <dict-select
                  :value.sync="form.processTimeLimit"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="60005"
                />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </template>
          <template v-if="form.type == 'core' && showMoreSearch">
            <el-col :span="5">
              <el-form-item label="故障专业：">
                <dict-select
                  :value.sync="form.professionalType"
                  :dictId="60009"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="工单来源：">
                <dict-select
                  :value.sync="form.source"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="10003"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="建单人：">
                <el-input
                  v-model="form.createUser"
                  style="width: 100%"
                  readonly
                >
                  <el-button
                    type="info"
                    slot="append"
                    icon="el-icon-user"
                    @click="onOpenPeopleDialog('builderDetermine')"
                  ></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="预估处理时限:">
                <dict-select
                  :value.sync="form.processTimeLimit"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="60005"
                />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </template>
          <template v-if="form.type == 'ip' && showMoreSearch">
            <el-col :span="5">
              <el-form-item label="故障专业：">
                <dict-select
                  :value.sync="form.professionalType"
                  :dictId="60010"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="工单来源：">
                <dict-select
                  :value.sync="form.source"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="10003"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="建单人：">
                <el-input
                  v-model="form.createUser"
                  style="width: 100%"
                  readonly
                >
                  <el-button
                    type="info"
                    slot="append"
                    icon="el-icon-user"
                    @click="onOpenPeopleDialog('builderDetermine')"
                  ></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="预估处理时限:">
                <dict-select
                  :value.sync="form.processTimeLimit"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="60005"
                />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </template>
          <template v-if="form.type == 'pt' && showMoreSearch">
            <el-col :span="5">
              <el-form-item label="故障专业：">
                <dict-select
                  :value.sync="form.professionalType"
                  :dictId="60011"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="工单来源：">
                <dict-select
                  :value.sync="form.source"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="10003"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="建单人：">
                <el-input
                  v-model="form.createUser"
                  style="width: 100%"
                  readonly
                >
                  <el-button
                    type="info"
                    slot="append"
                    icon="el-icon-user"
                    @click="onOpenPeopleDialog('builderDetermine')"
                  ></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="预估处理时限:">
                <dict-select
                  :value.sync="form.processTimeLimit"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="60005"
                />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </template>

    <template #contentHeader>
      <el-row
        ref="contentHeader"
        :gutter="20"
        class="content-header"
        v-loading="tableLoading"
        element-loading-spinner=" "
      >
        <el-col :xs="16" :sm="16" :md="16" :lg="18" style="padding-right: 0px;width: 100%;margin-bottom: 6px;">
          <filter-total
            v-loading="tabMenuLoading"
            element-loading-text="数据加载中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            :value.sync="form.type"
            :filters="filterList"
            :totals="filterTotals"
            @onConHeadFilterChange="onConHeadFilterChange"
          ></filter-total>
        </el-col>
        <el-col
          :xs="8"
          :sm="8"
          :md="8"
          :lg="6"
          style="text-align: right; padding-left: 0px"
          v-if="false"
        >
          <div>
            <el-button type="primary" @click="getTableData('senior')"
              >刷新</el-button
            >
          </div>
        </el-col>
      </el-row>
      <el-row
        v-if="true"
        :gutter="20"
        class="content-header"
        v-loading="tableLoading"
      >
        <el-col
          :xs="8"
          :sm="8"
          :md="8"
          :lg="6"
          style="text-align: right; padding-left: 0px;display: contents;"
        >
          <div>
            <el-button type="primary" @click="getTableData('senior')"
              >刷新</el-button
            >
          </div>
        </el-col>
      </el-row>
    </template>

    <template #table>
      <el-table
        ref="table"
        :data="tableData"
        :border="false"
        stripe
        height="100%"
        v-loading="tableLoading"
      >
        <el-table-column key="1" label="工单类别" width="120px">
          <template slot-scope="scope">
            <el-tag :class="getSheetTypeClass(scope.row)">
              <span v-if="scope.row.networkTypeTop == 1">省分</span>
              <span v-if="scope.row.networkTypeTop == 2">省分</span>
              <span v-if="scope.row.networkTypeTop == 4">高铁</span>
              <span v-if="scope.row.networkTypeTop == 5">集团</span>
              <span v-if="scope.row.networkTypeTop == 7">核心网</span>
              <span v-if="scope.row.networkTypeTop == 8">IP专业</span>
              <span v-if="scope.row.networkTypeTop == 9">平台</span>
              <span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == '传输网'
                "
                >骨干传输</span
              ><span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == 'IT云设备'
                "
                >IT云</span
              ><span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == '通信云'
                "
                >通信云</span
              >
              <span v-else-if="scope.row.networkTypeTop == 6">无线网</span>
              <span v-else-if="scope.row.networkTypeTop == 10">GNOC督办</span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column key="2" prop="sheetNo" width="320px" 、>
          <template slot="header" slot-scope="scope">
            <span>工单编号</span>
            <!-- v-if="
                form.type == 'province' ||
                form.type == 'sfty' ||
                form.type == 'gt'
              " -->
            <el-popover
              title="标签筛选"
              v-model="popoverVisible"
              placement="right"
              width="300"
              trigger="click"
              :ref="'sheetNo_' + scope.$index"
            >
              <el-tabs type="border-card" v-model="activeName">
                <el-tab-pane label="标签" name="tags">
                  <el-tree
                    ref="tagTree"
                    :data="treeData"
                    show-checkbox
                    node-key="label"
                    :props="defaultProps"
                  >
                  </el-tree>
                </el-tab-pane>
              </el-tabs>
              <base-icon
                slot="reference"
                icon-class="icon-filter"
                style="
                  width: 20px;
                  height: 30px;
                  vertical-align: middle;
                  cursor: pointer;
                "
              ></base-icon>
              <div style="float: right; margin-top: 10px">
                <el-button type="primary" @click="filterTree()"
                  >确 定</el-button
                >
                <el-button @click="closePopOver()">重 置</el-button>
              </div>
            </el-popover>
          </template>
          <template slot-scope="scope">
            <div :style="getSheetNoStyle(scope.row)">
              <el-button
                type="text"
                class="sheetNo_style"
                @click="toDoDetail(scope.row)"
                ><span style="font-size: 14px">{{
                  scope.row.sheetNo
                }}</span></el-button
              >
              <span v-if="scope.row.suspendStatus == 1" class="hang__order"
                >挂</span
              >
            </div>
            <div
              v-if="scope.row.workOrderTag.length > 0"
              style="position: absolute; bottom: 10px"
            >
              <div class="tag-container">
                <el-link
                  v-show="scope.row.workOrderTag.length > 3"
                  :key="'linkLeft_' + scope.$index"
                  @click="scrollLeft('scrollWrapper_' + scope.$index)"
                  style="padding: 2px 3px"
                  plain
                  icon="el-icon-caret-left"
                  :underline="false"
                ></el-link>

                <div
                  :ref="'scrollWrapper_' + scope.$index"
                  class="scroll-wrapper"
                >
                  <div class="tags">
                    <el-tag
                      v-for="item in scope.row.workOrderTag"
                      :color="item.color"
                      style="color: #fff"
                    >
                      {{ item.label }}
                    </el-tag>
                  </div>
                </div>
                <el-link
                  v-show="scope.row.workOrderTag.length > 3"
                  :key="'linkRight_' + scope.$index"
                  @click="scrollRight('scrollWrapper_' + scope.$index)"
                  style="padding: 2px 3px"
                  plain
                  icon="el-icon-caret-right"
                  :underline="false"
                ></el-link>
              </div>
              <!-- <div class="tags-list" style="width: 192px" ref="totalLists">

                <el-link
                  @click="arrowBack"
                  v-if="showButton"
                  style="padding: 2px 3px"
                  plain
                  icon="el-icon-caret-left"
                  :underline="false"
                ></el-link>
                <div class="tag-style" ref="tagBox">
                  <div
                    class="scrollWrapper"
                    ref="scrollWrapper"
                    id="nav"
                    :style="{ marginLeft: tabScroll }"
                  >
                    <el-tag
                      v-for="item in scope.row.workOrderTag"
                      :color="item.color"
                      style="color: #fff"
                    >
                      {{ item.label }}
                    </el-tag>
                  </div>
                </div>

                <el-link
                  @click="arrowForward"
                  v-if="showButton"
                  style="padding: 2px 3px"
                  plain
                  icon="el-icon-caret-left"
                  :underline="false"
                ></el-link>
              </div> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column
          key="3"
          prop="sheetTitle"
          label="工单主题"
          min-width="320px"
        >
          <template #default="{ row }">
            <el-tooltip :content="row.sheetTitle" placement="top">
              <p class="descStyle">{{ row.sheetTitle }}</p>
            </el-tooltip>
          </template>
        </el-table-column>
        <template v-if="form.type == 'gt'">
          <el-table-column
            key="4"
            prop="emergencyLevel"
            label="紧急程度"
            width="120px"
          ></el-table-column>
        </template>
        <template v-if="form.type == 'commonFlow'">
          <el-table-column key="18" prop="emergencyLevel" label="紧急程度">
          </el-table-column>
          <el-table-column
            key="19"
            prop="createType"
            label="工单来源"
            min-width="120px"
          >
          </el-table-column>
          <el-table-column
            key="20"
            prop="senderName"
            label="建单人"
            min-width="120px"
          ></el-table-column>
        </template>
        <template v-if="form.type != 'gt'">
          <el-table-column
            key="5"
            prop="sheetCreateTime"
            label="建单时间"
            width="160px"
          ></el-table-column>
        </template>
        <template v-if="form.type == 'supervise'">
          <el-table-column
            key="supervise_emergencyLevel"
            prop="emergencyLevel"
            label="紧急程度"
            width="120px"
          ></el-table-column>
          <el-table-column
            key="supervise_emergencyLevel"
            prop="createType"
            label="工单来源"
            min-width="120px"
          ></el-table-column>
          <el-table-column
            key="supervise_senderName"
            prop="senderName"
            label="建单人"
            min-width="120px"
          ></el-table-column>
          <el-table-column
            key="supervise_professional"
            prop="professionalType"
            label="所属专业"
            min-width="120px"
          ></el-table-column>
        </template>
        <template v-if="form.type == 'province' || form.type == 'wxw'">
          <el-table-column
            key="6"
            prop="city"
            label="归属地市"
          ></el-table-column>
        </template>
        <template v-if="form.type == 'sfty'">
          <el-table-column
            key="7"
            prop="professionalType"
            label="故障专业"
            min-width="120px"
          ></el-table-column>
          <el-table-column
            key="8"
            prop="agentMan"
            label="主送人"
            min-width="180px"
          >
            <template #default="{ row }">
              <el-tooltip :content="row.agentMan" placement="top">
                <p class="descStyle">{{ row.agentMan }}</p>
              </el-tooltip>
            </template></el-table-column
          >
        </template>
        <template
          v-if="
            form.type == 'province' || form.type == 'sfty' || form.type == 'wxw'
          "
        >
          <el-table-column key="9" prop="emergencyLevel" label="紧急程度">
          </el-table-column>
          <el-table-column
            key="10"
            prop="createType"
            :label="labelName"
            min-width="120px"
          >
          </el-table-column>
          <el-table-column
            key="11"
            prop="senderName"
            label="建单人"
            min-width="120px"
          ></el-table-column>
        </template>
        <template
          v-if="
            form.type != 'gt' && form.type != 'sfty' && form.type != 'supervise'
          "
        >
          <el-table-column
            key="12"
            prop="professionalType"
            label="故障专业"
            min-width="120px"
          ></el-table-column>
          <el-table-column
            key="13"
            prop="agentMan"
            label="主送人"
            min-width="180px"
          >
            <template #default="{ row }">
              <el-tooltip :content="row.agentMan" placement="top">
                <p class="descStyle">{{ row.agentMan }}</p>
              </el-tooltip>
            </template></el-table-column
          >
        </template>
        <template v-if="form.type == 'gt'">
          <el-table-column
            key="14"
            prop="senderName"
            label="客户名称"
            width="180px"
          ></el-table-column>
          <el-table-column
            key="15"
            prop="provinceName"
            label="发起省"
            width="120px"
          ></el-table-column>
        </template>
        <template v-if="form.type == 'supervise'">
          <el-table-column
            key="supervise_agentMan"
            prop="agentMan"
            label="主送人"
            min-width="180px"
          >
            <template #default="{ row }">
              <el-tooltip :content="row.agentMan" placement="top">
                <p class="descStyle">{{ row.agentMan }}</p>
              </el-tooltip>
            </template></el-table-column
          >
        </template>

        <template
          v-if="
            form.type == 'sfty' || form.type == 'province' || form.type == 'wxw'
          "
        >
          <el-table-column
            key="coverScene"
            prop="coverScene"
            min-width="120px"
            label="覆盖场景"
          >
            <template #default="{ row }">
              <el-tooltip :content="row.coverScene" placement="top">
                <p class="descStyle">{{ row.coverScene }}</p>
              </el-tooltip>
            </template>
          </el-table-column>
        </template>
        <el-table-column
          key="16"
          prop="sheetStatus"
          label="工单状态"
          width="100px"
        ></el-table-column>

        <template v-if="form.type == 'gt'">
          <el-table-column
            key="17"
            prop="loadTime"
            label="操作时间"
            width="200px"
          ></el-table-column>
        </template>
      </el-table>
    </template>
    <template #pagination>
      <pagination
        ref="pagination"
        :total="form.total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        @change="getTableData('senior')"
        v-loading="tableLoading"
        element-loading-spinner=" "
      />
    </template>
    <template #dialog>
      <dia-orgs-user-tree
        :title="diaPeople.title"
        :visible.sync="diaPeople.visible"
        :showOrgsTree="diaPeople.showOrgsTree"
        @on-save="onSavePeople"
      />
    </template>
  </head-content-layout>
</template>

<script>
import HeadContentLayout from "./components/HeadContentLayout.vue";
import Pagination from "./components/Pagination.vue";
import DictSelect from "./components/DictSelect.vue";
import FilterTotal from "./components/FilterTotal.vue";
import DiaOrgsUserTree from "./components/DiaOrgsUserTree.vue";
import { apiGetOrgInfo, apiListTreeTag } from "./api/CommonApi";
import { apiGetFaultArea } from "./workOrderWaitDetail/api/CommonApi";
import {
  apiGetInOfficeList,
  apiCountByAgentManGrp,
} from "./api/WorkOrderTodo.js";
import { mapGetters } from 'vuex';
import moment from "moment";
export default {
  name: "WorkOrderUnread",
  components: {
    HeadContentLayout,
    Pagination,
    DictSelect,
    FilterTotal,
    DiaOrgsUserTree,
  },
  data() {
    return {
      filterTreeId: "85023",
      activeName: "tags",
      tabScroll: "0px", // 移动的距离
      showButton: false, // 标签左右两侧箭头是否显示
      // swiperScrollWidth: 0, // 盒子的宽度
      // swiperScrollContentWidth: 0,
      popoverVisible: false,
      // showPopover: false,
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      scrollLeftDisabled: false,
      scrollRightDisabled: false,
      // 查询参数
      form: {
        woPriority: [],
        mkAlarmClear: [],
        keyword: "",
        sheetId: "",
        title: "",
        source: "",
        createTimeRange: [],
        operateTimeRange: [],
        createUser: "",
        specialty: "",
        urgencyDegree: "",
        processing: "",
        status: "",
        level: "",
        type: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
        networkTypeTop: 0,
        processTimeLimit: null,
        professionalType: "",
      },
      labelName: "",
      dictId: 30002,
      faultRegionOptions: [],
      provinceRangeTime: [], //省份建单时间
      startTime: moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
      endTime: moment().format("YYYY-MM-DD 23:59:59"),
      showMoreSearch: false,
      moreSearchColAttrs: Object.freeze({
        xs: 24,
        sm: 12,
        md: 12,
        lg: 8,
        offset: 0,
      }),
      filterList: [],
      tabMenuLoading: false,
      filterTotals: {
        ggw: 0,
        fiveGc: 0,
        ywpt: 0,
        gt: 0,
        yzy: 0,
        ITy: 0,
        txy: 0,
        province: 0,
        wxw: 0,
        supervise: 0,
      },
      tableLoading: false,
      tableData: [],
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          builderDetermine: "建单人选择",
        },
        showOrgsTree: false,
        showOrgsTreeMap: {
          builderDetermine: false,
        },
      },
      createUserIdArr: [],
    };
  },
  computed: {
    ...mapGetters([
      "showAiAgent"
    ]),
  },
  watch: {
    // 监听智能体状态变化，触发FilterTotal重新计算宽度
    showAiAgent: {
      handler(newVal, oldVal) {
        console.log('🤖 [在办工单] 智能体状态变化:', { oldVal, newVal });
        this.$nextTick(() => {
          this.triggerFilterTotalResize();
        });
      },
      immediate: false,
    },
  },
  activated() {
    this.form.pageNum = 1;
    this.onSearch();
    this.getOrgInfo();
  },
  mounted() {
    this.getFilterTreeData();
    // 监听窗口大小变化
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    // 处理窗口大小变化
    handleResize() {
      this.triggerFilterTotalResize();
    },

    // 触发FilterTotal组件重新计算宽度
    triggerFilterTotalResize() {
      const filterTotalComponent = this.$children.find(child =>
        child.$options.name === 'FilterTotal'
      );
      if (filterTotalComponent && typeof filterTotalComponent.updateScrollState === 'function') {
        filterTotalComponent.updateScrollState();
        console.log('🤖 [在办工单] 已触发FilterTotal重新计算宽度');
      }
    },
    //查询标签筛选树
    getFilterTreeData() {
      let param = {
        dictTypeCode: this.filterTreeId,
      };
      apiListTreeTag(param)
        .then(res => {
          if (res.code == 200) {
            this.treeData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.$emit("update:dict-list", this.dictData);
        });
    },
    // 标签向左切换
    arrowBack() {
      let tabBoxWidth = this.$refs.tagBox.clientWidth; //盒子宽度
      let offsetLeft = Math.abs(this.$refs.scrollWrapper.offsetLeft); //移动距离
      if (offsetLeft > tabBoxWidth) {
        //移动距离大于父盒子宽度，向前移动一整个父盒子宽度
        this.tabScroll = offsetLeft + tabBoxWidth + "px";
      } else {
        this.tabScroll = "0px"; // 否则移动到开始位置
      }
    },
    // 标签向右切换
    arrowForward() {
      let tabBoxWidth = this.$refs.tagBox.clientWidth; //盒子宽度
      let scrollWidth = this.$refs.scrollWrapper.scrollWidth; //内容宽度
      // 必须要在循环的父级添加 定位样式, offsetLeft 获取元素相对带有定位父元素左边框的偏移
      let offsetLeft = Math.abs(this.$refs.scrollWrapper.offsetLeft); //移动距离
      let diffWidth = scrollWidth - tabBoxWidth; //计算内容宽度与盒子宽度的差值
      if (diffWidth - offsetLeft > tabBoxWidth) {
        //判断差值减去移动距离是否大于盒子宽度 大于则滚动已移动距离+盒子宽度
        this.tabScroll = -(offsetLeft + tabBoxWidth) + "px";
      } else {
        this.tabScroll = -diffWidth + "px"; //小于则移动差值距离
      }
    },
    checkButtonStatus() {
      if (!this.$refs.scrollWrapper) return;
      // 盒子的宽度
      let containerSize = this.$refs.tagBox.clientWidth;
      // 内容的宽度
      let navSize = this.$refs.scrollWrapper.scrollWidth;
      if (containerSize > navSize || containerSize == navSize) {
        this.showButton = false;
      } else {
        this.showButton = true;
      }
    },
    onSelectionChange() {},
    getSheetNoStyle(row) {
      if (row.workOrderTag.length > 0) {
        return "display: inline-block;margin-bottom:25px";
      }
    },
    filterTree() {
      let self = this;
      let checked = self.$refs?.tagTree?.getCheckedNodes(true) ?? [];
      // let checkedNew = checked.map((obj, index) => {
      //   return obj.id;
      // });

      console.log("checked----", checked);

      this.form.woPriority = [];
      this.form.mkAlarmClear = [];
      checked.forEach(item => {
        if (this.form[item.name] == null) {
          this.form[item.name] = [];
        }
        this.form[item.name].push(item.id);
      });

      // console.log("form", this.form);
      this.popoverVisible = false;
      this.getTableData("senior");
    },
    closePopOver() {
      // 将选中设置为空
      this.$nextTick(function () {
        this.$refs.tagTree.setCheckedKeys([]);
      });
    },
    scrollLeft(wrapper) {
      this.$refs[wrapper].scrollBy({ left: -100, behavior: "smooth" });
      // this.getScrollLeftDisabled(wrapper);
      // this.getScrollRightDisabled(wrapper);
    },
    scrollRight(wrapper) {
      this.$refs[wrapper].scrollBy({ left: 100, behavior: "smooth" });
      // this.getScrollLeftDisabled(wrapper);
      // this.getScrollRightDisabled(wrapper);
    },
    checkScrollButtons() {
      const scrollWrapper = this.$refs.scrollWrapper;
      this.scrollLeftDisabled = scrollWrapper.scrollLeft <= 0;
      this.scrollRightDisabled =
        scrollWrapper.scrollWidth -
          scrollWrapper.clientWidth -
          scrollWrapper.scrollLeft <=
        0;
    },
    getScrollLeftDisabled(wrapper) {
      console.log("wrapper", wrapper);
      this.$nextTick(() => {
        const scrollWrapper = this.$refs[wrapper];
        console.log("scrollLeft", scrollWrapper.scrollLeft <= 0);
        return scrollWrapper.scrollLeft <= 0;
      });
    },
    getScrollRightDisabled(wrapper) {
      this.$nextTick(() => {
        const scrollWrapper = this.$refs[wrapper];
        console.log(
          "scrollright",
          scrollWrapper.scrollWidth -
            scrollWrapper.clientWidth -
            scrollWrapper.scrollLeft <=
            0
        );
        return (
          scrollWrapper.scrollWidth -
            scrollWrapper.clientWidth -
            scrollWrapper.scrollLeft <=
          0
        );
      });
    },

    professionalSum(param) {
      if (this.filterList.length == 0) this.tabMenuLoading = true;
      apiCountByAgentManGrp(param)
        .then(res => {
          if (res.status == "0") {
            let _this = this;
            let v = res?.data?.rows ?? [];
            let filList = [];
            for (let i = 0; i < v.length; i++) {
              filList.push({
                label: v[i].professionalType,
                value: this.getLabelValue(v[i].professionalType),
                professionalType: v[i].professionalTypeCode,
                networkTypeTop: v[i].networkTypeTop,
              });
            }
            this.filterList = filList;
            this.tabMenuLoading = false;
            for (let i = 0; i < v.length; i++) {
              filList.map(item => {
                if (
                  item.professionalType == v[i].professionalTypeCode &&
                  item.networkTypeTop == v[i].networkTypeTop
                ) {
                  _this.filterTotals[item.value] = v[i].sum;
                }
              });
            }
          } else {
            this.tabMenuLoading = false;
          }
        })
        .catch(err => {
          console.log(err);
          this.this.tabMenuLoading = false;
        });
    },
    getLabelValue(val) {
      switch (val) {
        case "骨干传输":
          return "ggw";
        case "IT云设备":
          return "ITy";
        case "通信云":
          return "txy";
        case "省分":
          return "province";
        case "高铁督办":
          return "gt";
        case "省分通用":
          return "sfty";
        case "集团通用":
          return "commonFlow";
        case "核心网":
          return "core";
        case "IP专业":
          return "ip";
        case "平台":
          return "pt";
        case "无线网":
          return "wxw";
        case "GNOC督办":
          return "supervise";
      }
    },
    onSwitchMoreSearch() {
      this.showMoreSearch = !this.showMoreSearch;
      this.form.source = "";
      this.form.professionalType = "";
      this.createUserIdArr = [];
      this.form.createUser = "";
    },
    onSearch() {
      this.form.pageNum = 1;
      this.form.type = "";
      this.getTableData("simple");
    },
    // 获取地区
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.form.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.form.category = res?.data?.category ?? "";
            this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getFaultAreaOptions() {
      let param = {
        areaCode: this.form.areaCode,
        category: this.form.category,
      };
      apiGetFaultArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //查询
    seniorQuery() {
      this.form.pageNum = 1;
      // if (this.form.type != "province" && this.form.type != "gt" && this.form.type != "sfty") {
      if (
        this.form.type == "ggw" ||
        this.form.type == "ITy" ||
        this.form.type == "txy"
      ) {
        this.setType(this.form.specialty);
      }
      this.getTableData("senior");
    },
    onResetForm() {
      this.form = {
        ...this.$options.data,
        pageNum: this.form.pageNum,
        total: this.form.total,
      };
    },
    onConHeadFilterChange(val = {}) {
      let { value, professionalType: type, networkTypeTop } = val;

      if (type == "3" && networkTypeTop == "0") {
        this.form.specialty = "传输网";
        this.dictId = 30000;
        this.filterTreeId = "85013";
      } else if (type == "23" && networkTypeTop == "0") {
        this.form.specialty = "IT云设备";
        this.dictId = 30014;
        this.filterTreeId = "85013";
      } else if (type == "22" && networkTypeTop == "0") {
        this.form.specialty = "通信云";
        this.dictId = 30014;
        this.filterTreeId = "85013";
      } else if (networkTypeTop == "6") {
        this.form.specialty = "无线网";
        this.dictId = 30001;
        this.labelName = "工单来源";
        this.filterTreeId = "85003";
      } else if (networkTypeTop == "1") {
        this.form.specialty = "省分";
        this.dictId = 30001;
        this.form.professionalType = "";
        this.labelName = "工单来源";
        this.filterTreeId = "85003";
      } else if (networkTypeTop == "4") {
        this.form.specialty = "高铁督办";
        this.form.professionalType = "";
        this.dictId = 30015;
        this.filterTreeId = "85003";
      } else if (networkTypeTop == "2") {
        this.form.specialty = "省分通用";
        this.form.professionalType = "";
        this.dictId = 30019;
        this.labelName = "工单来源";
        this.filterTreeId = "85003";
      } else if (networkTypeTop == "5") {
        this.form.specialty = "集团通用";
        this.form.professionalType = "";
        this.dictId = 60007;
        this.filterTreeId = "85013";
      } else if (networkTypeTop == "7") {
        this.form.specialty = "核心网";
        this.form.professionalType = "";
        this.dictId = 60007;
        this.filterTreeId = "85013";
      } else if (networkTypeTop == "8") {
        this.form.specialty = "IP专业";
        this.form.professionalType = "";
        this.dictId = 60007;
        this.filterTreeId = "85013";
      } else if (networkTypeTop == "9") {
        this.form.specialty = "平台";
        this.form.professionalType = "";
        this.dictId = 60007;
        this.filterTreeId = "85013";
      } else if (networkTypeTop == "10") {
        this.form.specialty = "集团督办";
        this.dictId = 811044;
        this.filterTreeId = "85013";
      } else {
        this.form.specialty = "";
        this.dictId = 30002;
        this.filterTreeId = "85023";
      }
      this.form.type = value; //控制tab
      this.form.pageNum = 1;
      this.form.woPriority = [];
      this.form.mkAlarmClear = [];
      this.getFilterTreeData();
      this.getTableData("senior");
    },

    getTableData(type) {
      this.tableLoading = true;
      let seniorParam = {
        flag: 1,
        sheetNo: this.form.sheetId,
        sheetTitle: this.form.title,
        //createType: this.form.source, //工单来源
        startTime: this?.form?.createTimeRange?.[0] ?? this.startTime,
        endTime: this?.form?.createTimeRange?.[1] ?? this.endTime,
        //senders: this.createUserIdArr,
        professionalType: this.getType(),
        emergencyLevel: this.form.urgencyDegree,
        processNodeId: this.form.processing, //处理环节
        sheetStatus: this.form.status, //工单状态
        sheetLevel: this.form.level,
        sheetTitleOrNoKeyword: this.form.keyword,
      };
      let simpleParam = {
        flag: 1,
        sheetTitleOrNoKeyword: this.form.keyword,
        startTime: this.startTime,
        endTime: this.endTime,
      };
      if (this.form.type === "province") {
        this.$set(seniorParam, "networkTypeTop", 1);
        seniorParam.faultRegion = this.form.faultRegion;
      } else if (this.form.type === "gt") {
        delete seniorParam.startTime;
        delete seniorParam.endTime;
        this.$set(
          seniorParam,
          "loadStartTime",
          this?.form?.operateTimeRange?.[0] ?? ""
        );
        this.$set(
          seniorParam,
          "loadEndTime",
          this?.form?.operateTimeRange?.[1] ?? ""
        );

        this.$set(seniorParam, "networkTypeTop", 4);
        seniorParam.faultRegion = this.form.faultRegion;
      } else if (this.form.type === "sfty") {
        this.$set(seniorParam, "networkTypeTop", 2);

        // this.$set(seniorParam, "emergencyLevel", this.form.urgencyDegree);
        this.$set(seniorParam, "createType", this.form.source); //工单来源
        this.$set(seniorParam, "professionalType", this.form.professionalType); //故障专业
        seniorParam.faultRegion = this.form.faultRegion;
      } else if (
        this.form.type == "ggw" ||
        this.form.type == "ITy" ||
        this.form.type == "txy"
      ) {
        this.$set(seniorParam, "networkTypeTop", 0);
      } else if (this.form.type == "commonFlow") {
        this.$set(seniorParam, "networkTypeTop", 5);
        this.$set(seniorParam, "createType", this.form.source); //工单来源
        this.$set(seniorParam, "processTimeLimit", this.form.processTimeLimit);
        this.$set(seniorParam, "professionalType", this.form.professionalType); //故障专业
        this.$set(seniorParam, "senders", this.createUserIdArr); //故障专业
      } else if (this.form.type == "wxw") {
        this.$set(seniorParam, "networkTypeTop", 6);
        this.$set(seniorParam, "professionalType", 7);
      } else if (this.form.type == "core") {
        this.$set(seniorParam, "networkTypeTop", 7);
        this.$set(seniorParam, "createType", this.form.source); //工单来源
        this.$set(seniorParam, "processTimeLimit", this.form.processTimeLimit);
        this.$set(seniorParam, "professionalType", this.form.professionalType); //故障专业
        this.$set(seniorParam, "senders", this.createUserIdArr); //故障专业
      } else if (this.form.type == "ip") {
        this.$set(seniorParam, "networkTypeTop", 8);
        this.$set(seniorParam, "createType", this.form.source); //工单来源
        this.$set(seniorParam, "processTimeLimit", this.form.processTimeLimit);
        this.$set(seniorParam, "professionalType", this.form.professionalType); //故障专业
        this.$set(seniorParam, "senders", this.createUserIdArr); //故障专业
      } else if (this.form.type == "pt") {
        this.$set(seniorParam, "networkTypeTop", 9);
        this.$set(seniorParam, "createType", this.form.source); //工单来源
        this.$set(seniorParam, "processTimeLimit", this.form.processTimeLimit);
        this.$set(seniorParam, "professionalType", this.form.professionalType); //故障专业
        this.$set(seniorParam, "senders", this.createUserIdArr); //故障专业
      } else if (this.form.type == "supervise") {
        this.$set(seniorParam, "networkTypeTop", 10);
        this.$set(seniorParam, "professionalType", "");
      }
      this.$set(seniorParam, "woPriority", this.form.woPriority?.join(","));
      this.$set(seniorParam, "mkAlarmClear", this.form.mkAlarmClear?.join(","));
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1:
          type == "senior"
            ? JSON.stringify(seniorParam)
            : JSON.stringify(simpleParam),
      };
      apiGetInOfficeList(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.rows ?? [];
            this.form.total = res?.data?.totalElements ?? 0;
            this.filterTotals[this.form.type] = res?.data?.totalElements ?? 0;

            this.professionalSum(param);
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    getType() {
      let type = "";
      if (this.form.specialty == "传输网") {
        type = "3";
      } else if (this.form.specialty == "IT云设备") {
        type = "23";
      } else if (this.form.specialty == "通信云") {
        type = "22";
      } else if (this.form.specialty == "高铁督办") {
        type = "";
      } else if (this.form.specialty == "省分通用") {
        type = "";
      } else if (this.form.specialty == "省分") {
        type = "";
      } else if (this.form.specialty == "集团通用") {
        type = "";
      } else if (this.form.specialty == "核心网") {
        type = "";
      } else if (this.form.specialty == "IP专业") {
        type = "";
      } else if (this.form.specialty == "平台") {
        type = "";
      } else if (this.form.specialty == "无线网") {
        type = "7";
      } else {
        type = this.form.specialty;
      }
      return type;
    },
    setType(newVal) {
      if (newVal == "传输网") {
        this.form.type = "ggw";
      } else if (newVal == "IT云设备") {
        this.form.type = "ITy";
      } else if (newVal == "通信云") {
        this.form.type = "txy";
      } else if (newVal == "省分") {
        this.form.type = "province";
      } else if (newVal == "集团通用") {
        this.form.type = "commonFlow";
      } else if (newVal == "核心网") {
        this.form.type = "core";
      } else if (newVal == "IP专业") {
        this.form.type = "ip";
      } else if (newVal == "平台") {
        this.form.type = "pt";
      } else if (newVal == "无线网") {
        this.form.type = "wxw";
      } else if (newVal == "集团督办") {
        this.form.type = "supervise";
      } else {
        this.form.type = "";
      }
    },
    toDoDetail(data) {
      if (data.networkTypeTop == 0) {
        switch (data.professionalType) {
          case "传输网":
            this.JumpDetails_GGW(data);
            break;
          case "IT云设备":
            this.JumpDetails_ITY(data);
            break;
          case "通信云":
            this.JumpDetails_TXY(data);
            break;
        }
      } else if (
        data.networkTypeTop == 5 ||
        data.networkTypeTop == 7 ||
        data.networkTypeTop == 9
      ) {
        this.JumpDetails_Common(data);
      } else if (data.networkTypeTop == 8) {
        this.JumpDetails_IpProfessionnal(data);
      } else if (data.networkTypeTop == 1) {
        this.JumpDetails_Province(data);
      } else if (data.networkTypeTop == 4) {
        this.JumpDetails_GT(data);
      } else if (data.networkTypeTop == 2) {
        this.JumpDetails_SFTY(data);
      } else if (data.networkTypeTop == 6) {
        this.JumpDetails_Wireless(data);
      } else if (data.networkTypeTop == 10) {
        this.JumpDetails_Supervise(data);
      }
    },
    JumpDetails_TXY(data) {
      this.$router.push({
        name: "commCloud_woDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "在办工单",
          networkType: data.networkType,
        },
      });
    },
    JumpDetails_ITY(data) {
      this.$router.push({
        name: "itCloud_woDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "在办工单",
          networkType: data.networkType,
        },
      });
    },
    JumpDetails_Wireless(data) {
      this.$router.push({
        name: "wireless_orderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "在办工单",
          processNode: data.processNode,
          networkType: data.networkType,
          professionalType: data.professionalType,
        },
      });
    },
    JumpDetails_GGW(data) {
      this.$router.push({
        name: "backbone_toDoRepairOrderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "在办工单",
          networkType: data.networkType,
          networkTypeTop: data.networkTypeTop,
          professionalType: data.professionalType,
        },
      });
    },
    JumpDetails_Province(data) {
      this.$router.push({
        name: "provinceOrder_detail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "在办工单",
          networkType: data.networkType,
        },
      });
    },
    JumpDetails_GT(data) {
      this.$router.push({
        name: "gtOrder_detail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "在办工单",
          networkType: data.networkType,
        },
      });
    },

    JumpDetails_Common(data) {
      this.$router.push({
        name: "common_orderDetail",
        query: {
          frameTabTitle:
            data.networkTypeTop == 5
              ? "集团通用故障工单"
              : data.networkTypeTop == 7
              ? "核心网故障工单"
              : data.networkTypeTop == 8
              ? "IP专业故障工单"
              : "平台故障工单",
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "在办工单",
          networkType: data.networkType,
        },
      });
    },

    JumpDetails_Supervise(data) {
      this.$router.push({
        name: "supervise_orderDetail",
        query: {
          frameTabTitle: "GNOC督办单",
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "在办工单",
          networkType: data.networkType,
        },
      });
    },
    JumpDetails_IpProfessionnal(data) {
      this.$router.push({
        name: "ipProfessionOrder_Detail",
        query: {
          frameTabTitle: "IP专业故障工单",
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "在办工单",
          networkType: data.networkType,
        },
      });
    },
    JumpDetails_SFTY(data) {
      this.$router.push({
        name: "commonProvinceOrder_detail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "在办工单",
          networkType: data.networkType,
        },
      });
    },

    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
    onOpenPeopleDialog(diaSaveName) {
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.visible = true;
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    builderDetermine({ usersChecked, orgsChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        let createUserArr = selectionUser.map(item => {
          return item.trueName;
        });
        if (this.form.type == "province") {
          this.formP.createUser = createUserArr.join(",");
        } else {
          this.form.createUser = createUserArr.join(",");
        }
        this.createUserIdArr = selectionUser.map(item => {
          return item.userName;
        });
      } else {
        let createUserArr = usersChecked.map(item => {
          return item.name;
        });
        if (this.form.type == "province") {
          this.formP.createUser = createUserArr.join(",");
        } else {
          this.form.createUser = createUserArr.join(",");
        }
        this.createUserIdArr = usersChecked.map(item => {
          return item.id;
        });
      }
    },
    getSheetTypeClass(data) {
      if (data.networkTypeTop == 0 && data.professionalType == "传输网") {
        return "ggw_sheetType";
      } else if (
        data.networkTypeTop == 0 &&
        data.professionalType == "IT云设备"
      ) {
        return "itCloud_sheetType";
      } else if (
        data.networkTypeTop == 0 &&
        data.professionalType == "通信云"
      ) {
        return "commCloud_sheetType";
      } else if (data.networkTypeTop == 6) {
        return "wxw_sheetType";
      } else if (data.networkTypeTop == 1) {
        return "sf_sheetType";
      } else if (data.networkTypeTop == 5) {
        return "commonFlow_sheetType";
      } else if (data.networkTypeTop == 4) {
        return "gt_sheetType";
      } else if (data.networkTypeTop == 2) {
        return "sfty_sheetType";
      } else if (data.networkTypeTop == 7) {
        return "core_sheetType";
      }
      if (data.networkTypeTop == 8) {
        return "ip_sheetType";
      }
      if (data.networkTypeTop == 9) {
        return "pt_sheetType";
      }
      if (data.networkTypeTop == 10) {
        return "supervise_sheetType";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./workOrderWaitDetail/assets/common.scss";
.filter-total {
  ::v-deep .el-loading-mask .el-loading-spinner {
    text-align: left;
    line-height: 32px;
    margin-left: 150px;
  }
  ::v-deep .el-loading-text {
    display: inline-block;
    margin-left: 10px;
  }
}
.descStyle {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 新样式
.tag-container {
  display: flex;
  align-items: center;
}
</style>
