/**
 * 字段名 1:1 映射真实接口
 * 仅保留文档中出现的字段，其余废弃
 */
export const FIELD_NAMES = {
  /* ---------- 主告警 / 子告警 ---------- */
  alarmId         : 'alarmId',
  alarmOccurTime  : 'alarmCreateTime',    // 发生时间 → 接口字段名
  alarmName       : 'alarmTitle',         // 告警名称 → 接口字段名
  alarmType       : 'orgType',            // 告警类别 → 接口字段名
  alarmCityCode   : 'alarmCity',          // 告警地市 → 接口字段名
  alarmRegionCode : 'alarmCounty',        // 告警区县 → 接口字段名
  alarmDetail     : 'alarmDetail',
  alarmLocation   : 'alarmLocation',
  faultCategory   : 'faultReason',        // 故障类别 → 接口字段名
  deviceManufacturer : 'alarmVendor',     // 设备厂家 → 接口字段名
  deviceType      : 'neType',             // 设备类型 → 接口字段名

  /* ---------- 子告警追加专有 ---------- */
  alarmLevel      : 'alarmLevel',         // 仅追加接口

  /* ---------- 工单信息 sheetInfo ---------- */
  faultHandler    : 'processingParty',    // 故障处理方 → 接口字段名

  /* ---------- 基站 / 站址 ---------- */
  siteNameNew     : 'stName',             // 站址名称 → 接口字段名
  siteCoordinates : 'siteCoordinates',    // 站址经纬度（内部使用，需拆分为latitude/longitude）
  siteCategory    : 'stCategory',         // 站址类别 → 接口字段名
  belongingArea   : 'areaName',           // 所属区域 → 接口字段名
  belongingScene  : 'sceneName',          // 所属场景 → 接口字段名
  baseStationType : 'bsType',             // 基站类型 → 接口字段名

  /* ---------- 业务逻辑字段（保持原有） ---------- */
  isSharedConstruction: 'isSharedConstruction', // 是否共建共享（业务逻辑字段）
}

// 故障处理方选项
export const FAULT_HANDLER_OPTIONS = [
  { label: '联通', value: 'unicom' },
  { label: '电信', value: 'telecom' }
]

// 是否共建共享选项
export const SHARED_CONSTRUCTION_OPTIONS = [
  { label: '是', value: 'yes' },
  { label: '否', value: 'no' }
]

// 基站类型选项
export const BASE_STATION_TYPE_OPTIONS = [
  { label: '室分', value: 'indoor' },
  { label: '宏站', value: 'macro' }
]

// 站址类别选项
export const SITE_CATEGORY_OPTIONS = [
  { label: '铁塔', value: 'tower' },
  { label: '电信自有', value: 'telecom_owned' },
  { label: '其他', value: 'other' },
  { label: '联通自有', value: 'unicom_owned' }
]

// 所属场景选项
export const BELONGING_SCENE_OPTIONS = [
  { label: '口碑场景', value: 'reputation_scene' },
  { label: '高校', value: 'university' },
  { label: '高铁', value: 'high_speed_rail' },
  { label: '高速', value: 'highway' },
  { label: '高密度住宅区', value: 'high_density_residential' },
  { label: '高流量商务区及地铁', value: 'high_traffic_business_metro' },
  { label: '非五高一地', value: 'non_five_high_one_ground' }
]

// 告警级别选项
export const ALARM_LEVEL_OPTIONS = [
  { label: '紧急告警', value: 'urgent' },
  { label: '重要告警', value: 'important' },
  { label: '次要告警', value: 'minor' },
  { label: '提示告警', value: 'info' }
]

// 故障类别选项
export const FAULT_CATEGORY_OPTIONS = [
  { label: '基站设备', value: 'base_station_equipment' },
  { label: '传输系统', value: 'transmission_system' },
  { label: '动环系统（铁塔、自有）', value: 'power_environment_system' }
]
