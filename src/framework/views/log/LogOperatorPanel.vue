<template>
  <div>
    <el-header style="height: 50px; width: 100%">
      <el-card class="box-card">
        <el-form :model="queryParams" :inline="true">
          <el-form-item label="用户名">
            <el-input v-model="queryParams.userName"></el-input>
          </el-form-item>
          <el-form-item label="操作内容">
            <el-input
              style="width: 360px"
              v-model="queryParams.logInfo"
            ></el-input>
          </el-form-item>
          <el-form-item label="开始时间">
            <el-date-picker
              v-model="queryParams.startLoadTime"
              type="datetime"
              placeholder="选择日期时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间">
            <el-date-picker
              v-model="queryParams.endLoadTime"
              type="datetime"
              placeholder="选择日期时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="doCheck" class="el-icon-search"
              >查询
            </el-button>
            <el-button
              type="primary"
              @click="exportLogOperatorFn"
              class="export el-icon-download"
              >导出
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </el-header>
    <el-main style="width: 100%">
      <el-card
        :style="{
          height: rightBodyHeight,
          width: '100%',
        }"
      >
        <el-table
          :style="{ width: '100%' }"
          :height="tableHeight"
          :data="tableData"
          border
          stripe
        >
          <el-table-column align="center" prop="" label="序号" width="60">
            <template slot-scope="scope">
              <span>{{
                scope.$index +
                1 +
                (pageConfig.currentPage - 1) * pageConfig.pageSize
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="userName"
            label="用户名"
            width="120"
          ></el-table-column>
          <!-- <el-table-column
            align="left"
            width="160px"
            prop="realName"
            label="姓名"
          ></el-table-column> -->
          <el-table-column
            align="left"
            width="160px"
            prop="zoneName"
            label="所属区域"
          ></el-table-column>
          <!-- <el-table-column
            align="left"
            width="160px"
            prop="deptName"
            label="所属组织"
          ></el-table-column> -->
          <el-table-column
            align="left"
            width="160px"
            prop="logAction"
            label="操作动作"
          ></el-table-column>
          <el-table-column
            align="left"
            prop="logInfo"
            label="操作内容"
          ></el-table-column>
          <el-table-column
            align="center"
            width="170"
            prop="loadTime"
            :formatter="dateFormater"
            label="操作时间"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="clientIp"
            label="IP地址"
            width="140"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="clientOs"
            label="操作系统"
            width="150"
          ></el-table-column>
          <el-table-column width="200" align="center" label="详细">
            <template slot-scope="{ row }">
              <div>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="'查看详细'"
                  placement="left-start"
                >
                  <el-button
                    plain
                    size="mini"
                    type="primary"
                    icon="el-icon-view"
                    @click="handleShowRequestParam(row)"
                  ></el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div align="right">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageConfig.currentPage"
            :page-sizes="[10, 15, 20, 30, 50]"
            :page-size="pageConfig.pageSize"
            layout="->, sizes, prev, pager, next, jumper, slot, total"
            :total="pageConfig.total"
            class="pagination"
          ></el-pagination>
        </div>
      </el-card>
    </el-main>
    <el-dialog
      title="查看详细"
      :visible.sync="requestParamDialogVisible"
      :before-close="handleRequestParamDialogClose"
      width="30%"
    >
      <el-input rows="15" type="textarea" v-model="requestParam"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="requestParamDialogVisible = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";
import { getLogOperator, exportLogOperator } from "./api/log";

export default {
  name: "LogOperatorPanel",
  props: ["pid", "tableHeight", "rightBodyHeight"],
  watch: {
    pid: function () {
      this.queryLogOperatorFn();
    },
  },
  data() {
    return {
      requestParam: "",
      requestParamDialogVisible: false,
      tableData: [],
      queryParams: {
        userName: "",
        logInfo: "",
        startLoadTime: moment().add(-1, "d").toDate(),
        endLoadTime: moment().toDate(),
      },
      pageConfig: {
        pageSizes: [10, 20, 30, 40, 50, 60, 70, 80, 100],
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
    };
  },
  methods: {
    dateFormater: function (row) {
      if (row.loadTime) {
        return moment(row.loadTime).format("YYYY-MM-DD HH:mm:ss");
      }
      return "";
    },
    handleRequestParamDialogClose(done) {
      this.requestParam = "";
      done();
    },
    handleShowRequestParam(row) {
      this.requestParam = row.requestParam;
      this.requestParamDialogVisible = true;
    },
    exportLogOperatorFn() {
      let that = this;
      that.$message.info("正在导出文件");
      exportLogOperator({
        logInfo: this.queryParams.logInfo,
        userName: this.queryParams.userName,
        startLoadTime: this.queryParams.startLoadTime,
        endLoadTime: this.queryParams.endLoadTime,
      }).catch(err => {
        if (err.response.data && err.response.data.type == "application/json") {
          let reader = new FileReader();
          reader.readAsText(err.response.data, "utf-8");
          reader.onload = function (result) {
            let target = result.target;
            let res = JSON.parse(target.result);
            that.$message.warning(res.msg);
          };
        } else if (err.status) {
          this.$message.error(err.message);
        }
      });
    },

    //执行查询
    doCheck() {
      this.resetData();
      this.queryLogOperatorFn();
    },

    queryLogOperatorFn: function () {
      getLogOperator({
        logInfo: this.queryParams.logInfo,
        userName: this.queryParams.userName,
        startLoadTime: this.queryParams.startLoadTime,
        endLoadTime: this.queryParams.endLoadTime,
        pageNum: this.pageConfig.currentPage,
        pageSize: this.pageConfig.pageSize,
      })
        .then(res => {
          this.tableData = res.data.list;
          this.pageConfig.total = res.data.total;
        })
        .catch(err => {
          if (err.status) {
            this.$message.error(err.message);
          }
        });
    },

    //重置数据
    resetData() {
      this.pageConfig.currentPage = 1;
      this.tableData = [];
    },

    handleSizeChange: function (pageSize) {
      this.pageConfig.pageSize = pageSize;
      this.resetData();
      this.queryLogOperatorFn();
    },

    handleCurrentChange: function (pageNum) {
      this.pageConfig.currentPage = pageNum;
      this.tableData = [];
      this.queryLogOperatorFn();
    },
  },
  mounted() {
    this.queryLogOperatorFn();
  },
};
</script>
