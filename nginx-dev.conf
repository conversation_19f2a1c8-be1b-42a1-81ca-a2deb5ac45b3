#user  nobody;
worker_processes 2;
#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;
#pid        logs/nginx.pid;
events {
  worker_connections 2048;
}
http {
  include mime.types;
  default_type application/octet-stream;
  #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
  #                  '$status $body_bytes_sent "$http_referer" '
  #                  '"$http_user_agent" "$http_x_forwarded_for"';
  #access_log  logs/access.log  main;
  sendfile on;
  #tcp_nopush     on;
  #keepalive_timeout  0;
  keepalive_timeout 65;
  client_max_body_size 50m;
  #代理websocket map $http_upgrade $connection_upgrade {
  #default upgrade;
  #'' close;
  #}
  #gzip  on;
  gzip on;
  #开启或关闭gzip on off
  gzip_disable "msie6";
  #不使用gzip IE6
  gzip_min_length 100k;
  #gzip压缩最小文件大小，超出进行压缩（自行调节）
  gzip_buffers 4 16k;
  #buffer 不用修改
  gzip_comp_level 4;
  #压缩级别:1-10，数字越大压缩的越好，时间也越长，建议4
  gzip_types text/plain application/x-javascript application/javascript text/css application/xml text/javascript application/x-httpd-php application/json image/jpeg image/gif image/png;
  #  压缩文件类型
  gzip_vary on;
  #给CDN和代理服务器使用，针对相同url，可以根据头信息返回压缩和非压缩副本

  server_tokens off;
  # 限制跨域请求
  map $http_origin $allow_cors {
    default 1;
    "~^(http?://(************:8088)?)$" 1;
    "~^(http?://(**************:9080)?)$" 1;
    "~^(https?://(dzyw.ossapp.chinaunicom.cn:9443)?)$" 1;
    "~^(https?://(127.0.0.1:9443)?)$" 1;
    "~^(https?://(127.0.0.1:8080)?)$" 1;
    "~*" 0;
  }

  upstream netfm3_api {
    server netfm3-api-svc:9900 weight=1;
  }
  upstream netfm3_common_flow {
    server netfm3-common-flow-svc:9907 weight=1;
  }
  upstream netfm3_backbone_flow {
    server netfm3-backbone-flow-svc:9903 weight=1;
  }
  upstream netfm3_commcloud_flow {
    server netfm3-commcloud-flow-svc:9803 weight=1;
  }
  upstream netfm3_common {
    server netfm3-common-svc:8785 weight=1;
  }
  upstream netfm3_province_flow {
    server netfm3-province-flow-svc:9904 weight=1;
  }
  upstream netfm3-gt-dispatch {
    server netfm3-gt-dispatch-svc:8187 weight=1;
  }
  upstream netfm3-dispatch {
    server netfm3-dispatch-svc:8186 weight=1;
  }
  upstream netfm3-gt-flow {
    server netfm3-gt-flow-svc:9905 weight=1;
  }
  upstream netfm3-province-common-flow {
    server netfm3-province-common-flow-svc:9906 weight=1;
  }
  upstream netfm3-province-common-interface {
    server netfm3-province-common-interface-svc:9908 weight=1;
  }
  upstream netfm3_wireless_flow {
    server netfm3-wireless-flow-svc:9909 weight=1;
  }
  upstream backend {
    keepalive 100;
    server *************:9664 max_fails=4 weight=1 fail_timeout=5s;
  }
  server {
    listen 9080;
    server_name localhost;
    #指定允许其他域名访问
    add_header Access-Control-Allow-Origin $http_origin;
    #允许的请求类型
    # add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
    #允许的请求头字段
    # add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";

    #charset koi8-r;
    #access_log  logs/host.access.log  main;
    # 新增测试
    location /nginx_status {
      stub_status on;
      access_log off;
      allow 127.0.0.1;
      deny all;
    }
    # 对接监控平台
    location ^~ /tianyan {
      proxy_pass http://backend;
      proxy_connect_timeout 1s;
      proxy_read_timeout 1s;
      proxy_send_timeout 1s;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location ^~ /nfm3/tianyan {
      proxy_pass http://backend/tianyan/;
      proxy_connect_timeout 1s;
      proxy_read_timeout 1s;
      proxy_send_timeout 1s;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /login {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_backbone_flow/login;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /commApi/v1/login {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_backbone_flow/login;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /framework {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_backbone_flow/framework;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /backbone {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_backbone_flow/backbone;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /commonFlow {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_common_flow/commonFlow;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /commApi/v1 {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_api/commApi/v1;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /commCloud {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_commcloud_flow/commCloud;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /province {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_province_flow/province;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /wireless {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_wireless_flow/wireless;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /nfm3/wireless {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_wireless_flow/wireless;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /gt {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3-gt-flow/gt;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /commonDict/attach/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_common/attach/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /commonDict/enum/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_common/enum/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /commonDict/info/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_common/info/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /commonDict/analyze/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_common/analyze/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }


    location /commApi/v1/info/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_common/info/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /resweb_jituan {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************;
    }

    location /resweb {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************/resweb_jituan/;
    }

    location /uflow-api {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************:8081;
    }

    location /netfm3topo/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:1500/;
    }

    location /topoapi {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:1500;
    }

    location /nfm3/login {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_backbone_flow/login;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /nfm3/commApi/v1/login {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_backbone_flow/login;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /nfm3/framework {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_backbone_flow/framework;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /nfm3/backbone {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_backbone_flow/backbone;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /nfm3/commonFlow {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_common_flow/commonFlow;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /nfm3/commonprovince {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3-province-common-flow/commonprovince;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /nfm3/interface {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3-province-common-interface/interface;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /nfm3/commApi/v1 {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_api/commApi/v1;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /nfm3/commCloud {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_commcloud_flow/commCloud;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /nfm3/province {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_province_flow/province;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /nfm3/gt {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3-gt-flow/gt;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /nfm3/netfm3-gt-dispatch {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3-gt-dispatch/netfm3-gt-dispatch;
      proxy_set_header Host $host:8088;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header Via "nginx";
    }
    location /nfm3/netfm3-dispatch {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3-dispatch/netfm3-dispatch;
      proxy_set_header Host $host:8088;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header Via "nginx";
    }
    location /nfm3/commonDict/enum/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_common/enum/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /nfm3/commonDict/todoSorting/ {
      if ($allow_cors = 0) {
        return 403;
      }
      proxy_pass http://netfm3_gateway/netfm3-common/todoSorting/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /nfm3/commonDict/info/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_common/info/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /nfm3/commonDict/attach/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3_common/attach/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }


    location /nfm3/resweb_jituan {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************;
    }

    location /nfm3/resweb {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************/resweb_jituan/;
    }

    location /nfm3/uflow-api/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************:8081/uflow-api/;
    }

    location /nfm3/netfm3topo/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:1500/;
    }

    location /nfm3/topoapi/ {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:1500/topoapi/;
    }

    location /nfm3/ser {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3-province-common-interface/ser;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /nfm3/ws {
      if ($allow_cors = 0) {
        return 403;
      }

      proxy_pass http://netfm3-province-common-interface/ws;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    # location /{
    #   root /etc/nginx/html;
    #   index index.html index.htm;
    #   if    (!-e    $request_filename){
    #     rewrite ^(.*)$ /index.html?s=$1 last;
    #   }
    # }
    location /nfm3 {
      if ($allow_cors = 0) {
        return 403;
      }

      alias /etc/nginx/html/nfm3/;
      index index.html index.htm;
      try_files $uri $uri/ /index.html =404;
    }

    #error_page404              /404.html;
    # redirect server error pages to the static page /50x.html
    # error_page 500 502 503 504 /50x.html;
    location = /50x.html {
      root html;
    }
  }
}
