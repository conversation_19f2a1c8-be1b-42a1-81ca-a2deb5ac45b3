<template>
  <div style="height: 100%" :class="[type != 'pc' ? 'full-main' : '']">
    <!-- 流程图显示区域 -->
    <div v-if="isShowFlowView" :style="vmStyle">
      <flow-view
        :flow-json="flowJson"
        :complete-connects="completeConnects"
        @click-element="handleClickElement"
        @mouseover-element="handleMouseoverElement"
        :type="type"
        :isPortraitScreen="isPortraitScreen"
        :key="reciveNum"
      ></flow-view>
    </div>

    <!-- 无流程图提示区域 -->
    <div
      v-else-if="showNoFlowMessage"
      :style="vmStyle"
      class="no-flow-container"
    >
      <div class="no-flow-message">当前工单无法找到诊断流程图</div>
    </div>
    <!-- 构建一个看不见的元素并能触发气泡 -->
    <template>
      <el-popover
        v-model="popoverVisible"
        ref="popover"
        :visible-arrow="false"
        popper-class="node-detail-propper"
        append-to-body
        class="popover-class"
        :width="popoverWidth"
      >
        <div :style="getFontSize()" class="flow-table-class" v-if="hoverTask">
          <div :style="getPopoverTitle()">
            <div style="margin-left: 5px; font-weight: bold">
              {{ hoverTask.taskName }}
            </div>
            <div style="cursor: pointer" @click="hidePopover">
              <em class="el-icon-close"></em>
            </div>
          </div>
          <div
            v-if="processId == hwProcessIdFixed"
            style="background: #fff; padding: 10px; display: flex; width: 100%"
          >
            <div style="margin-right: 10px; margin-left: 5px; width: 52%">
              <span>操作时间:</span>
              <span style="margin-left: 2px" v-if="hoverTask.startTime">{{
                hoverTask.startTime
              }}</span>
              <span v-else>--</span>
            </div>
            <div>
              <span>执行时长:</span>
              <span style="margin-left: 2px" v-if="hoverTask.duration"
                >{{ hoverTask.duration }}(ms)</span
              ><span v-else>--</span>
            </div>
          </div>
          <div :class="getFontClass()" :style="getTableStyle()">
            <!-- background: #fff; padding: 0 10px -->
            <el-table
              stripe
              :data="hoverTask.result || []"
              :header-cell-style="{ background: '#F2F2F2' }"
              class="max-height-table"
            >
              <!-- <template v-if="processId != hwProcessIdFixed"> -->
              <el-table-column
                label="类型"
                align="left"
                prop="accessType"
                width="50px"
              >
                <template slot-scope="{ row }">
                  <span v-if="row.accessType && removeSpaces(row.accessType)">{{
                    row.accessType
                  }}</span>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <el-table-column
                label="名称"
                align="left"
                prop="entityName"
                width="120px"
                ><template slot-scope="{ row }">
                  <span v-if="row.entityName && removeSpaces(row.entityName)">
                    {{ row.entityName }}</span
                  >
                  <span v-else>--</span>
                </template></el-table-column
              >
              <el-table-column label="值" align="left" prop="entityValue"
                ><template slot-scope="{ row }">
                  <span v-if="row.entityValue && removeSpaces(row.entityValue)">
                    {{ row.entityValue }}</span
                  >
                  <span v-else>--</span>
                </template></el-table-column
              >
              <!-- </template> -->
              <!-- <template v-else>
                <el-table-column label="序号" type="index" width="50">
                </el-table-column>
                <el-table-column
                  label="描述"
                  align="left"
                  prop="describe"
                ></el-table-column>
              </template> -->
            </el-table>
          </div>
        </div>
        <div class="trigger-div" ref="reference" slot="reference"></div>
      </el-popover>
    </template>
  </div>
</template>
<script>
import { getFlowPage, getFlowConnects } from "../flow-api";
import { mapGetters } from "vuex";

import FlowView from "../flow-view.vue";
import { hwProcessId } from "../api/HwProcessId";
export default {
  components: {
    FlowView,
  },
  props: {
    workOrderId: String,
    type: String,
    reciveNum: String,
  },
  name: "FlowViewPage",
  computed: {
    ...mapGetters(["frameStyle"]),
    vmStyle() {
      if (this.type != "pc") {
        return {
          height: `calc(100vh - ${this.frameStyle.headerHeight}px)`,
        };
      } else {
        return { height: "600px" };
      }
    },
    popoverWidth() {
      if (this.type != "pc") {
        return 330;
      } else {
        return 480;
      }
    },
    popoverTableHeight() {
      if (this.type != "pc") {
        return "230px";
      } else {
        return "223px";
      }
    },
  },
  data() {
    return {
      woId: null,
      // 点亮数据
      completeConnects: null,
      // 节点map
      taskMap: {},
      //流程图JSON
      flowJson: null,

      // 气泡显示控制
      popoverVisible: false,
      popoverStyle: {
        // height: "300px",
        // background: "#F8E9E8",
        transition: "all .2s",
        padding: "0px",
        paddingBottom: "10px",
      },
      hoverTask: null,
      hoverElement: null,
      isShowFlowView: false,
      // 显示无流程图提示
      showNoFlowMessage: false,
      //app判断是否横竖屏 横屏为false,竖屏为true
      isPortraitScreen: null,
      processId: null,
      hwProcessIdFixed: hwProcessId,
    };
  },
  mounted() {
    console.log(this.hwProcessIdFixed);
    this.isPortraitScreen = this.$route.query.isPortraitScreen;
    this.woId = this.$route.query.woId || this.workOrderId;
    //"6d6c65a741eb4b678c3e1ff8bcf97424"; "20240425100012_vwldDxqBAe0pTMJcmyz";//this.$route.query.woId || this.workOrderId; //||"3ca13494304c4d04a90960a80ea837c6"; //this.$route.query.woId;
    // this.getAdxInfo();

    try {
      let dom = (this.parentScrollDom = document.querySelector(
        ".content-wrap"
      ));
      dom.addEventListener("scroll", this.hidePopover);
    } catch (error) {}
  },
  beforeDestroy() {
    if (this.parentScrollDom) {
      this.parentScrollDom.removeEventListener("scroll", this.hidePopover);
    }
  },
  watch: {
    reciveNum: {
      handler(_val) {
        this.getAdxInfo();
      },
      immediate: true,
    },
  },
  methods: {
    //获取诊断流程图信息
    getAdxInfo() {
      // 重置状态
      this.isShowFlowView = false;
      this.showNoFlowMessage = false;
      if (
        !this.reciveNum &&
        this.$route.query.wo_diagnose_round != "undefined" &&
        this.$route.query.wo_diagnose_round
      ) {
        this.reciveNum = this.$route.query.wo_diagnose_round;
      }
      getFlowConnects(this.woId || this.$route.query.woId, this.reciveNum)
        .then(res => {
          let adxStatus = res?.data?.adxStatus ?? "NO";
          if (adxStatus == "YES") {
            //展示诊断流程图
            this.isShowFlowView = true;
            this.showNoFlowMessage = false;
            this.completeConnects = res?.data?.chartData?.linkList ?? [];
            let taskList = res?.data?.chartData?.taskList ?? [];
            this.taskMap = {};
            for (let task of taskList) {
              this.taskMap[task.taskId] = task;
            }
            let processId = res?.data?.chartData?.processId ?? "";
            this.processId = processId;
            let version = res?.data?.chartData?.processVersion ?? "";
            this.getFlowData(processId, version);
          } else {
            // adxStatus为"NO"时显示提示信息
            this.isShowFlowView = false;
            this.showNoFlowMessage = true;
            console.log("诊断流程图状态为NO，显示提示信息");
          }
        })
        .catch(error => {
          console.log(error);
          // 请求失败时也显示提示信息
          this.isShowFlowView = false;
          this.showNoFlowMessage = true;
        });
    },
    //获取流程图数据
    getFlowData(processId, version) {
      let param = {
        version: version,
        key: processId,
        pageNum: 1,
        pageSize: 1,
      };
      getFlowPage(param)
        .then(res => {
          this.flowJson = res?.data?.records[0]?.json ?? "";
        })
        .catch(error => {
          console.log(error);
        });
    },
    hidePopover() {
      this.popoverVisible = false;
    },
    handleClickElement(element, _evt) {
      if (!this.type) {
        this.showPopover(element);
      }
    },
    /**
     * PC端节点悬浮事件处理 - 显示气泡并更新位置
     *
     * @param element
     * @param _evt
     */
    handleMouseoverElement(element, _evt) {
      if (this.type) {
        this.showPopover(element);
      }
    },
    showPopover(element) {
      this.hoverTask = null;
      let { id } = element;
      let hoverTask = this.taskMap[id];

      if (!hoverTask || hoverTask.result.length <= 0) {
        this.hidePopover();
        return;
      }

      this.hoverTask = hoverTask;
      this.popoverVisible = true;
      this.hoverElement = element;

      this.$nextTick(() => {
        const position = this.calculateOptimalPosition(element);
        setTimeout(() => {
          this.nodeDetailPropperAssign(position.left, position.top);
        }, 50);
      });
    },
    getHwProcessPopover(element) {
      if (element?.meta?.describe) {
        let hoverTask = {};
        this.$set(hoverTask, "result", []);
        this.$set(hoverTask, "taskName", element.name);
        if (element.meta.describe.indexOf("|$|") != -1) {
          let describeArr = element.meta.describe.split("|$|");
          console.log(describeArr);
          describeArr.forEach(item => {
            hoverTask.result.push({ describe: item });
          });
        } else {
          hoverTask.result.push({ describe: element.meta.describe });
        }
        console.log(hoverTask);
        this.hoverTask = hoverTask;
      } else {
        this.hidePopover();
      }
    },
    /**
     * 计算弹窗的最佳显示位置
     * @param {Object} element - 节点元素
     * @returns {Object} 包含left和top的位置对象
     */
    calculateOptimalPosition(element) {
      const nodeRect = element.node.getBoundingClientRect();
      const viewport = this.getViewportInfo();
      const popoverSize = this.getPopoverSize();
      const spacing = this.getSpacing();

      // 移动端竖屏特殊处理
      if (this.isMobilePortrait()) {
        return this.calculateMobilePosition(viewport, popoverSize);
      }

      // 计算各个方向的可用空间
      const availableSpace = this.calculateAvailableSpace(
        nodeRect,
        viewport,
        spacing
      );

      // 根据可用空间选择最佳位置
      const preferredDirection = this.selectOptimalDirection(availableSpace);

      // 计算具体位置坐标
      return this.calculatePositionByDirection(
        nodeRect,
        popoverSize,
        spacing,
        preferredDirection,
        viewport
      );
    },

    /**
     * 获取视窗信息
     */
    getViewportInfo() {
      return {
        width: window.innerWidth,
        height: window.innerHeight,
        scrollX: window.pageXOffset || document.documentElement.scrollLeft,
        scrollY: window.pageYOffset || document.documentElement.scrollTop,
      };
    },

    /**
     * 获取弹窗尺寸信息
     */
    getPopoverSize() {
      const baseHeight = 300; // 弹窗基础高度
      const dynamicHeight = this.hoverTask?.result?.length
        ? Math.min(400, Math.max(200, this.hoverTask.result.length * 40 + 100))
        : baseHeight;

      return {
        width: this.popoverWidth,
        height: dynamicHeight,
      };
    },

    /**
     * 获取间距配置
     */
    getSpacing() {
      const isMobile = this.type !== "pc";
      return {
        node: isMobile ? 8 : 12, // 与节点的距离
        edge: isMobile ? 10 : 16, // 与屏幕边缘的距离
        safe: isMobile ? 5 : 8, // 安全边距
      };
    },

    /**
     * 判断是否为移动端竖屏
     */
    isMobilePortrait() {
      return this.type !== "pc" && this.isPortraitScreen === "true";
    },

    /**
     * 计算移动端位置
     */
    calculateMobilePosition(viewport, popoverSize) {
      return {
        left: viewport.width - popoverSize.width - 10,
        top: Math.max(10, (viewport.height - popoverSize.height) / 2),
      };
    },

    /**
     * 计算各个方向的可用空间
     */
    calculateAvailableSpace(nodeRect, viewport, spacing) {
      return {
        right: viewport.width - nodeRect.right - spacing.edge,
        left: nodeRect.left - spacing.edge,
        bottom: viewport.height - nodeRect.bottom - spacing.edge,
        top: nodeRect.top - spacing.edge,
      };
    },

    /**
     * 选择最佳显示方向
     */
    selectOptimalDirection(availableSpace) {
      const {
        width: popoverWidth,
        height: popoverHeight,
      } = this.getPopoverSize();

      // 优先级：右 > 左 > 下 > 上
      const directions = [
        { name: "right", space: availableSpace.right, required: popoverWidth },
        { name: "left", space: availableSpace.left, required: popoverWidth },
        {
          name: "bottom",
          space: availableSpace.bottom,
          required: popoverHeight,
        },
        { name: "top", space: availableSpace.top, required: popoverHeight },
      ];

      // 找到第一个空间足够的方向
      const suitable = directions.find(dir => dir.space >= dir.required);

      // 如果没有完全合适的空间，选择空间最大的方向
      return suitable
        ? suitable.name
        : directions.reduce((max, current) =>
            current.space > max.space ? current : max
          ).name;
    },

    /**
     * 根据方向计算具体位置
     */
    calculatePositionByDirection(
      nodeRect,
      popoverSize,
      spacing,
      direction,
      viewport
    ) {
      let left, top;

      switch (direction) {
        case "right":
          left = nodeRect.right + spacing.node;
          top = this.calculateVerticalCenter(
            nodeRect,
            popoverSize.height,
            viewport
          );
          break;

        case "left":
          left = nodeRect.left - popoverSize.width - spacing.node;
          top = this.calculateVerticalCenter(
            nodeRect,
            popoverSize.height,
            viewport
          );
          break;

        case "bottom":
          left = this.calculateHorizontalCenter(
            nodeRect,
            popoverSize.width,
            viewport
          );
          top = nodeRect.bottom + spacing.node;
          break;

        case "top":
          left = this.calculateHorizontalCenter(
            nodeRect,
            popoverSize.width,
            viewport
          );
          top = nodeRect.top - popoverSize.height - spacing.node;
          break;
      }

      // 应用边界约束
      return this.applyBoundaryConstraints(
        { left, top },
        popoverSize,
        viewport,
        spacing
      );
    },

    /**
     * 计算垂直居中位置
     */
    calculateVerticalCenter(nodeRect, popoverHeight, viewport) {
      const nodeCenter = nodeRect.top + nodeRect.height / 2;
      const idealTop = nodeCenter - popoverHeight / 2;

      // 确保不超出视窗边界
      const minTop = this.getSpacing().edge;
      const maxTop = viewport.height - popoverHeight - this.getSpacing().edge;

      return Math.max(minTop, Math.min(maxTop, idealTop));
    },

    /**
     * 计算水平居中位置
     */
    calculateHorizontalCenter(nodeRect, popoverWidth, viewport) {
      const nodeCenter = nodeRect.left + nodeRect.width / 2;
      const idealLeft = nodeCenter - popoverWidth / 2;

      // 确保不超出视窗边界
      const minLeft = this.getSpacing().edge;
      const maxLeft = viewport.width - popoverWidth - this.getSpacing().edge;

      return Math.max(minLeft, Math.min(maxLeft, idealLeft));
    },

    /**
     * 应用边界约束
     */
    applyBoundaryConstraints(position, popoverSize, viewport, spacing) {
      const { left, top } = position;
      const { width, height } = popoverSize;
      const { edge } = spacing;

      // 水平边界约束
      const constrainedLeft = Math.max(
        edge,
        Math.min(viewport.width - width - edge, left)
      );

      // 垂直边界约束
      const constrainedTop = Math.max(
        edge,
        Math.min(viewport.height - height - edge, top)
      );

      return {
        left: constrainedLeft,
        top: constrainedTop,
      };
    },

    /**
     * 设置弹窗位置和样式
     */
    nodeDetailPropperAssign(left, top) {
      const popperElement = document.body.querySelector(".node-detail-propper");
      if (!popperElement) return;

      Object.assign(popperElement.style, {
        left: `${left}px`,
        top: `${top}px`,
        position: "fixed",
        zIndex: "2001",
        maxWidth: `${this.popoverWidth}px`,
        maxHeight: "400px",
        overflow: "auto",
        ...this.popoverStyle,
      });

      // 添加平滑过渡效果
      if (!popperElement.style.transition) {
        popperElement.style.transition = "all 0.2s ease-out";
      }
    },
    getPopoverTitle() {
      return {
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        padding: "10px",
        backgroundColor: "#F8E9E8",
      };
    },
    getFontSize() {
      return {
        fontSize: this.type != "pc" ? "11px" : "13px",
      };
    },
    getTableStyle() {
      if (this.processId != this.hwProcessIdFixed) {
        return {
          background: "#fff",
          padding: "0 10px",
          marginTop: "10px",
        };
      } else {
        return {
          background: "#fff",
          padding: "0 10px",
        };
      }
    },
    getFontClass() {
      if (this.type != "pc") {
        return "app-font-size-class";
      }
    },
    removeSpaces(str) {
      return str.replace(/\s+/g, "");
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-table .el-table__body-wrapper {
  scrollbar-width: thin !important;
}
::v-deep .custom-theme-default .el-popover .el-table .cell {
  line-height: unset !important;
}
.max-height-table {
  max-height: 223px; /* 你想要的最大高度 */
  overflow-y: auto;
}

.flow-table-class {
  ::v-deep .el-table--small .el-table__cell {
    padding: 4px 0 !important;
  }
  .app-font-size-class {
    ::v-deep .el-table--small {
      font-size: 11px !important;
    }
  }
}

/* 无流程图提示样式 */
.no-flow-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.no-flow-message {
  font-size: 16px;
  color: #606266;
  text-align: center;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 90%;
  word-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .no-flow-message {
    font-size: 14px;
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .no-flow-message {
    font-size: 12px;
    padding: 10px;
  }
}
</style>
