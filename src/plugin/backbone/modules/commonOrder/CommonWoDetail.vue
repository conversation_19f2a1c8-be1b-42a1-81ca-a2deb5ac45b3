<template>
  <div
    class="common-wo-detail-wrapper"
    :class="{
      'mobile': isMobile
    }"
    :style="wrapperStyle"
  >
    <head-fixed-layout class="draft-eidt-page" v-loading="pageLoading">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="14" class="head-title">
          <text-collapse :style="showAiAgent ? { fontSize: '18px' } : {}" :text="`【${basicWorkOrderData.professionalType || ''}故障工单】${headInfo.title
            }`" :max-lines="3"></text-collapse>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="10" class="head-handle-wrap">
          <el-button-group>
            <el-button type="button" @click="onHeadHandleClick('jcxx')">基础信息</el-button>
            <el-button type="button" @click="onHeadHandleClick('gjxq')">告警详情</el-button>
            <el-button type="button" v-if="ppResult" @click="onHeadHandleClick('glzd')">关联诊断</el-button>
            <el-button type="button" @click="onHeadHandleClick('fkdxq')">反馈单详情</el-button>
            <el-dropdown @command="onHeadHandleClick" class="el-button more-dropdown" size="medium">
              <el-button type="button">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(item, i) in headMoreDrops" :key="i" :command="item.command">
                  {{ item.title }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-button-group>
          <br />
          <!-- 按钮动态展示 -->
          <el-button v-if="!showAiAgent" size="mini" type="primary" v-for="(item, i) in processButtonArr" class="btnleft__group" :key="i"
            @click="buttonClick(item.value, item.desc)" v-loading.fullscreen.lock="processFullscreenLoading">{{
              item.value }}</el-button>
        </el-col>
      </el-row>
      <template v-if="showAiAgent">
        <el-row style="display: flex;">
          <el-col :span="12">
            <div></div>
          </el-col>
          <el-col :span="12" class="head-handle-wrap">
            <!-- 按钮动态展示 -->
            <el-button size="mini" type="primary" v-for="(item, i) in processButtonArr" class="btnleft__group" :key="i"
              @click="buttonClick(item.value, item.desc)" v-loading.fullscreen.lock="processFullscreenLoading">{{
                item.value }}</el-button>
          </el-col>
        </el-row>
      </template>
      <el-divider direction="horizontal" content-position="left" class="divider"></el-divider>
      <div class="head-info2">
        <el-row :gutter="20" tag="p">
          <el-col :style="showAiAgent ? { fontSize: '18px' } : {}" :xs="24" :sm="24" :md="12" :lg="12" tag="p" class="head-sheetId">
            工单编号: {{ headInfo.sheetId }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" tag="p">
            <el-row :gutter="20" type="flex">
              <el-col :xs="24" :sm="12" :md="12" :lg="7" tag="p" class="head-up-down">
                <div>当前处理人</div>
                <div class="text-truncate" @click="currentProcessor" style="
                    cursor: pointer;
                    text-decoration: underline;
                    color: #b50b14;
                    user-select: unset;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-size: 18px;
                    line-height: 28px;
                  " :title="headInfo.currentHandler" :style="showAiAgent ? { fontSize: '16px' } : {}">
                  {{ headInfo.currentHandler }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="4" tag="p" class="head-up-down">
                <div>工单状态</div>
                <div :style="showAiAgent ? { fontSize: '16px' } : {}">
                  {{ headInfo.sheetStatus }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="7" tag="p" class="head-up-down"
                v-if="headInfo.sheetStatus == '待受理'">
                <div>剩余受理时间</div>
                <div :style="showAiAgent ? { fontSize: '16px' } : {}" class="text-primary" :title="setAcceptTimeLimit">
                  {{ setAcceptTimeLimit }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="7" tag="p" class="head-up-down" v-if="
                [
                  '处理中',
                  '待定性',
                  '待确认',
                  '待定性审核',
                  '挂起',
                  '上传故障报告',
                  '故障报告审核',
                ].includes(headInfo.sheetStatus)
              ">
                <div>剩余处理时间</div>
                <div :style="showAiAgent ? { fontSize: '16px' } : {}" class="text-primary" :title="setEstimatedProTimeLimit">
                  {{ setEstimatedProTimeLimit }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="6" tag="p" class="head-up-down">
                <div>工单总耗时</div>
                <div :style="showAiAgent ? { fontSize: '16px' } : {}" class="text-primary" :title="headInfo.totalWorkingTime">
                  {{ headInfo.totalWorkingTime }}
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        
        <template v-if="!showAiAgent">
          <el-row :gutter="20" tag="p">
            <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
              建单人: {{ headInfo.buildSingleMan }}
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
              建单部门: {{ headInfo.buildSingleDept }}
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
              建单时间: {{ headInfo.buildSingleTime }}
            </el-col>
          </el-row>
          <el-row :gutter="20" tag="p">
            <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
              工单来源: {{ headInfo.sourceInfo }}
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
              发生时间: {{ headInfo.occurrenceTime }}
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
              紧急程度: {{ headInfo.emergencyDegree }}
            </el-col>
          </el-row>
        </template>
        <template v-if="showAiAgent">
            <el-row 
              type="flex" 
              justify="space-between" 
              :gutter="20" 
              tag="p"
            >
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p" style="margin-left: 0;">
                建单人: {{ headInfo.buildSingleMan }}
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p" style="margin-left: 0;">
                建单部门: {{ headInfo.buildSingleDept }}
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p" style="margin-left: 0;">
                建单时间: {{ headInfo.buildSingleTime }}
              </el-col>
            </el-row>
            <el-row 
              type="flex" 
              justify="space-between" 
              :gutter="20" 
              tag="p"
            >
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p" style="margin-left: 0;">
                工单来源: {{ headInfo.sourceInfo }}
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p" style="margin-left: 0;">
                发生时间: {{ headInfo.occurrenceTime }}
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p" style="margin-left: 0;">
                紧急程度: {{ headInfo.emergencyDegree }}
              </el-col>
            </el-row>
        </template>
        
      </div>
    </template>

    <base-info ref="jcxx" v-if="basicWorkOrderData" :basicWorkOrderData="basicWorkOrderData" :woId="common.woId"
      :workItemId="common.workItemId" />
    <alarm-detail :class="showAiAgent ?'alarmDetailTable': ''" ref="gjxq" v-if="common.woId && headInfo.occurrenceTime" :common="common"
      :occurrenceTime="headInfo.occurrenceTime" :isShowClearAlarmApply="isShowClearAlarmApply"
      :isShowManualConfirm="isShowManualConfirm" @alarmClearCallBack="alarmClearCallBack" />
    <relation-diagnosis ref="glzd" v-if="common.woId && ppResult" :woId="common.woId" :ppResult="ppResult"
      :analysisStatus="analysisStatus" />
    <feedback-sheet ref="fkdxq" v-if="showFkdxq && common.woId" :isShowAudit="isShowQualitativeReviewButton"
      :isShowQualitative="isShowQualitative" :common="common" :woId="common.woId" :qualitativeType="qualitativeType"
      @qualitativeReviewSubmit="qualitativeReviewSubmit" @qualitativeSubmit="qualitativeSubmit" />
    <report-records ref="gzbgscjl" v-if="reportRecordsShow" :isShowReportReview="isShowReportReview"
      :reportRecordsData="reportRecordsData" :woId="common.woId" @closeDialogReportAudit="closeDialogReportAudit" />
    <deal-details ref="clxq" v-if="showClxq" :woId="common.woId" :common="common" />
    <process-log ref="lcrz" v-if="showLcrz" :woId="common.woId" :aiAgentActive="showAiAgent" />
    <flow-chart :key="showAiAgent + Math.random() * Math.random()" ref="lct" v-if="showLct" :common="common" :aiAgentActive="showAiAgent" />
    <el-dialog title="返单" :visible.sync="dialogBackSingleVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogBackSingleClose" :fullscreen="false" width="83%" top="5vh">
      <span slot="title">
        <span style="line-height: 24px; font-size: 18px; color: #333">返单</span>
        <el-button v-if="ruleButtonVisible" type="primary" style="float: right; margin-right: 35px"
          @click="changeRuleVisible">返单审核规则</el-button>
      </span>
      <back-single ref="backSingleForm" :basicWorkOrderData="basicWorkOrderData" :common="common" :timing="timing"
        @closeBackSingleDialog="dialogBackSingleSubmitClose"
        @professionalTypeChange="professionalTypeChange"></back-single>
    </el-dialog>
    <el-dialog width="620px" title="返单审核规则" :visible.sync="ruleVisible" :close-on-click-modal="false" append-to-body>
      <p style="
          font-size: 16px;
          line-height: 130%;
          margin-top: 0;
          margin-bottom: 0;
        ">
        返单界面标记“*”符号字段必须按实际情况填写，审核时重点关注内容如下，填写信息包含即可，返单页面无对应字段的请自行选择字段填写：<br />
        (1)故障排查对象（具体系统、网元、设备）<br />
        (2)排查结果<br />
        (3)故障影响范围（据实反馈故障影响业务情况）<br />
        (4)故障处理情况<br />
        (5)故障原因分析<br />
        (6)反馈人信息(联系方式)。<br />
      </p>
    </el-dialog>
    <el-dialog title="追单" :visible.sync="dialogAfterSingleVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogAfterSingleClose" width="600px">
      <after-single :common="common" @closeAfterSingleDialog="dialogAfterSingleSubmitClose"></after-single>
    </el-dialog>
    <el-dialog title="阶段反馈" :visible.sync="dialogStageFeedbackVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogStageFeedbackClose" width="450px">
      <stage-feedback :common="common" @stageBackDialogClose="stageBackDialogCommitClose"></stage-feedback>
    </el-dialog>
    <el-dialog title="撤单" :visible.sync="dialogRevokeVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogRevokeClose" width="480px">
      <el-form ref="revokeForm" :model="revokeForm" :rules="revokeFormRules" label-width="90px">
        <el-form-item label="审核意见:" prop="auditOpinion">
          <el-input type="textarea" :rows="2" placeholder="请填写审核意见" v-model="revokeForm.auditOpinion"
            style="width: 300px" show-word-limit maxlength="1000">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleRevokeSubmit('revokeForm')"
          v-loading.fullscreen.lock="revokeSubmitLoading">提 交</el-button>
        <el-button @click="onResetRevoke">重 置</el-button>
      </div>
    </el-dialog>
    <el-dialog title="挂起" :visible.sync="dialogHangUpVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogHangUpClose" width="540px">
      <hang-up :common="common" :opType="1" @closeDialogHangUp="dialogHangUpSubmitClose"></hang-up>
    </el-dialog>
    <el-dialog title="解挂" :visible.sync="dialogSolutionToHangVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogSolutionToHangClose" width="540px">
      <hang-up :common="common" :opType="2" @closeDialogHangUp="dialogSolutionToHangSubmitClose"></hang-up>
    </el-dialog>
    <el-dialog title="挂起审核" :visible.sync="dialogPendingReviewVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogPendingReviewClose" width="450px">
      <audit :common="common" :opContent="opContent" @closeDialogPendingReview="dialogPendingReviewSubmitClose"></audit>
    </el-dialog>
    <el-dialog title="解挂审核" :visible.sync="dialogSolutionToHangAuditVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogSolutionToHangAuditClose" width="450px">
      <audit :common="common" :opContent="opContent"
        @closeDialogSolutionToHangAudit="dialogSolutionToHangAuditSubmitClose">
      </audit>
    </el-dialog>
    <el-dialog title="异常终止" :visible.sync="dialogAbendVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogAbendClose" width="450px">
      <abend :common="common" @closeDialogAbend="dialogAbendSubmitClose"></abend>
    </el-dialog>
    <el-dialog title="异常终止审核" :visible.sync="dialogAbendAuditVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogAbendAuditClose" width="450px">
      <abend-audit :common="common" @closeDialogAbendAudit="dialogAbendAuditSubmitClose"></abend-audit>
    </el-dialog>
    <el-dialog title="转派" :visible.sync="dialogTurnToSendVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogTurnToSendClose" width="480px">
      <turn-to-send @closeDialogTurnToSend="dialogTurnToSendSubmitClose" :common="common" :type="type"
        actionName="转派"></turn-to-send>
    </el-dialog>
    <el-dialog title="转办" :visible.sync="dialogTurnToVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogTurnToClose" width="480px">
      <turn-to @closeDialogTurnToSend="dialogTurnToSubmitClose" :common="common" :type="type" actionName="转办"></turn-to>
    </el-dialog>
    <el-dialog title="故障报告转派" :visible.sync="dialogReportTransferVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogTurnToSendClose" width="480px">
      <report-transfer @closeDialogTurnToSend="dialogReportTransferClose" :common="common" :type="type"
        actionName="故障报告转派"></report-transfer>
    </el-dialog>
    <el-dialog title="重新转派" :visible.sync="dialogAgainTurnToSendVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogAgainTurnToSendClose" width="480px">
      <turn-to-send @closeDialogTurnToSend="dialogAgainTurnToSendSubmitClose" :common="common" :type="type"
        actionName="重新转派"></turn-to-send>
    </el-dialog>
    <one-key-ivr :dialogOneKeyIvrNotice.sync="dialogOneKeyIvrNotice" :common="common"
      :sheetId="headInfo.sheetId && headInfo.sheetId"
      :sheetCreateTime="headInfo.buildSingleTime && headInfo.buildSingleTime" />
    <el-dialog title="定性审核" :visible.sync="dialogQualitativeReviewVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="qualitativeReviewClose" width="83%" top="5vh">
      <qualitative-review ref="qualitativeReview" :common="common" :workItemId="common.workItemId" v-if="showDxsh"
        @qualitativeReviewSubmit="qualitativeReviewSubmit"></qualitative-review>
    </el-dialog>
    <el-dialog title="上传故障报告" :visible.sync="dialogReportUpVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogSolutionToHangClose" width="600px">
      <report-up :common="common" :sheetCreateTime="headInfo.buildSingleTime"
        @closeDialogHangUp="dialogSolutionToHangSubmitClose"></report-up>
    </el-dialog>
    <el-dialog title="定性" :visible.sync="dialogQualitativeVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" :common="common" @close="qualitativeClose" width="83%" top="5vh">
      <qualitative ref="qualitative" :common="common" :workItemId="common.workItemId"
        @qualitativeSubmit="qualitativeSubmit"></qualitative>
    </el-dialog>
    <el-dialog title="处理人" :visible.sync="dialogCurrentProcessorVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="currentProcessorClose" :fullscreen="false" width="60%" top="5vh">
      <current-processor :common="common" :persons="basicWorkOrderData.operatePersonId">
      </current-processor>
    </el-dialog>
    </head-fixed-layout>


  </div>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "../workOrder/components/HeadFixedLayout.vue";
import BaseInfo from "./components/BaseInfo.vue";
import AlarmDetail from "./components/AlarmDetail.vue";
import FeedbackSheet from "./components/FeedbackSheet.vue";
import DealDetails from "./components/DealDetails.vue";
import RelationDiagnosis from "./components/RelationDiagnosis.vue";
import ProcessLog from "../workOrder/workOrderWaitDetail/components/ProcessLog.vue";
import FlowChart from "../workOrder/workOrderWaitDetail/components/FlowChart.vue";
import BackSingle from "./components/BackSingle.vue";
import StageFeedback from "../workOrder/workOrderWaitDetail/components/StageFeedback.vue";
import HangUp from "./components/HangUp.vue";
import Audit from "./components/Audit.vue";
import Abend from "./components/Abend.vue";
import AfterSingle from "./components/AfterSingle.vue";
import AbendAudit from "./components/AbendAudit.vue";
import TurnToSend from "./components/TurnToSend.vue";
import TurnTo from "./components/TurnTo.vue";
import oneKeyIvr from "../commCloud/components/CommCloudoneKeyIVR.vue";
import QualitativeReview from "./components/QualitativeReview.vue";
import ReportUp from "./components/ReportUp.vue";
import Qualitative from "./components/Qualitative.vue";
import ReportRecords from "./components/ReportRecords.vue";
import TextCollapse from "@plugin/backbone/components/TextCollapseTitle.vue";
import ReportTransfer from "./components/ReportTransfer.vue";
import CurrentProcessor from "../workOrder/components/CurrentProcessor.vue";

import {
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiAccept,
  // apiItCloudAccept,
  apiRevoke,
  apiHaveRead,
  apiGetReportUpLog,
  apiRotationSubmit,
  apiQueryAlarmDetail
} from "./api/CommonApi";

import { apiIsHaveAuth } from "../workOrder/api/CommonApi";

import { mixin } from "../../../../mixins";
export default {
  name: "WorkOrderWaitDetail",
  components: {
    HeadFixedLayout,
    BaseInfo,
    AlarmDetail,
    FeedbackSheet,
    DealDetails,
    ProcessLog,
    FlowChart,
    BackSingle,
    RelationDiagnosis,
    StageFeedback,
    HangUp,
    Audit,
    Abend,
    AfterSingle,
    AbendAudit,
    TurnToSend,
    oneKeyIvr,
    Qualitative,
    QualitativeReview,
    ReportUp,
    ReportRecords,
    TextCollapse,
    ReportTransfer,
    CurrentProcessor,
    TurnTo,

  },
  mixins: [mixin],
  data() {
    return {
      headInfo: {
        title: "",
        sheetId: "",
        currentHandler: "",
        currentLink: "",
        sheetStatus: null,
        acceptTimeLimit: "",
        totalWorkingTime: "",
        buildSingleMan: "",
        buildSingleDept: "",
        buildSingleTime: "",
        sourceInfo: "",
        occurrenceTime: "",
        emergencyDegree: "",
        estimatedProTimeLimit: "", //剩余处理时间隔
      },
      headMoreDrops: [
        { command: "gzbgscjl", title: "故障报告上传记录" },
        { command: "clxq", title: "处理详情" },
        { command: "lcrz", title: "流程日志" },
        { command: "lct", title: "流程图" },
      ],
      processButtonArr: [],
      basicWorkOrderData: "",
      common: {
        woId: "", //工单ID
        workItemId: "", //工作项ID
        processInstId: null, //流程实例ID
        processDefId: null, //流程定义ID
        failureTime: "", //故障发生时间
        failureInformTime: "", //故障通知时间
        actionName: "", //操作按钮名称
        processNode: "", //环节名称
        sheetNo: "", //工单编号
        isSender: null, //是否为建单人
        agentManId: "", //主送人Id
        agentMan: "", //主送人
        copyManId: "", //抄送人Id
        copyMan: "", //抄送人
        sheetStatus: "", //工单状态
        agentDeptCode: "",
        agentDeptName: "",
        copyDeptCode: "",
        copyDeptName: "",
        professionalType: null,
        professionalTypeName: null, //专业类型名称
        hangOver: null, //挂起历时
        faultCauseDescription: null, //故障原因描述
        busName: null, //业务名称
        auditResult: null,
        isUploadReport: null, //是否上传故障报告
        remainAcceptTime: null, //剩余处理时间
        alarmClearTime: null,
        isObject: null, //追单 0：追信息 1：追人
        mecNodeName: "",
        emergencyLevel: "",
      },
      timing: {
        hangTime: "", //挂起时间
      },
      showFkdxq: false,
      showClxq: false,
      showLcrz: false,
      showLct: false,
      ppResult: null,
      analysisStatus: 0,
      processFullscreenLoading: false,
      isShowClearAlarmApply: false,
      //阶段反馈
      dialogStageFeedbackVisible: false,
      //返单
      dialogBackSingleVisible: false, //一干
      //撤单
      dialogRevokeVisible: false,
      revokeSubmitLoading: false,
      revokeForm: {
        auditOpinion: null,
      },
      revokeFormRules: {
        auditOpinion: [
          {
            required: true,
            message: "请填写审核意见",
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "revokeForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      //当前处理人
      dialogCurrentProcessorVisible: false,
      //挂起
      dialogHangUpVisible: false,
      //解挂
      dialogSolutionToHangVisible: false,
      //挂起审核
      auditTitle: null,
      opContent: null,
      dialogPendingReviewVisible: false,
      //解挂审核
      dialogSolutionToHangAuditVisible: false,
      //异常终止
      dialogAbendVisible: false,
      //异常终止审核
      dialogAbendAuditVisible: false,
      //追单
      dialogAfterSingleVisible: false,
      //转派
      dialogTurnToSendVisible: false,
      //转办
      dialogTurnToVisible: false,
      //故障报告转派
      dialogReportTransferVisible: false,
      //类型
      type: "single",
      //重新转派
      dialogAgainTurnToSendVisible: false,
      //一键IVR
      dialogOneKeyIvrNotice: false,
      //故障报告上传
      dialogReportUpVisible: false,
      route: null,
      //从哪个页面进入的，不同页面进入，显示的按钮会不同，如：待阅工单只展示已阅、传阅按钮
      fromPage: null,
      pageLoading: true,
      isShowManualConfirm: false,
      dialogQualitativeReviewVisible: false, //定性审核弹出框
      isShowQualitativeReviewButton: false,
      isShowQualitative: false,
      isShowReportReview: false, //故障报告审核按钮
      dialogQualitativeVisible: false, //定性弹出框
      qualitativeType: "",
      showDxsh: false,
      isBackSingle: false,
      ruleVisible: false,
      ruleButtonVisible: false,
      reportRecordsData: [],
      reportRecordsShow: false,

      // 智能体相关数据
      isMobile: false,
      workOrderDataLoaded: false, // 工单数据是否已加载完成

      // 智能体消息监听器引用
      aiAgentMessageHandler: null,

      // ========== 新增：页面活跃状态管理 ==========
      // 当前页面是否为活跃tab
      isCurrentPageActive: false,
      // 页面实例ID（用于区分同一工单的多个页面实例）
      pageInstanceId: null,

    };
  },
  computed: {
    ...mapGetters([
      "userInfo",
      "token",
      "sidebar",
      // 全局智能体状态
      "showAiAgent",
      "isAiAgentAvailable",
      "aiAgentLoading",
      "aiAgentUrl",
      "aiAgentConfig"
    ]),

    // 智能体面板的最小宽度（用于CSS变量）
    aiAgentMinWidth() {
      return this.isMobile ? 0 : 300;
    },

    // 智能体面板的最大宽度（用于CSS变量）
    aiAgentMaxWidth() {
      return this.isMobile ? 0 : 600;
    },
    setAcceptTimeLimit() {
      let time = this.headInfo.acceptTimeLimit;
      return this.setTime(time);
    },
    setEstimatedProTimeLimit() {
      let time = this.headInfo.estimatedProTimeLimit;
      // if (this.headInfo.sheetStatus == "待定性审核") {
      //   time = "-";
      // }

      return this.setTime(time);
    },
    // 容器样式（使用CSS变量传递动态值）
    wrapperStyle() {
      return {
        '--ai-agent-min-width': `${this.aiAgentMinWidth}px`,
        '--ai-agent-max-width': `${this.aiAgentMaxWidth}px`,
      };
    },

    // 获取智能体iframe引用
    aiAgentIframe() {
      // 通过AiAgentContainer组件获取内部iframe
      const aiAgentContainer = this.$refs.aiAgentContainer;
      if (aiAgentContainer && aiAgentContainer.$el) {
        return aiAgentContainer.$el.querySelector('.ai-agent-iframe');
      }
      return null;
    },
  },
  watch: {
    // 监听全局智能体显示状态变化
    showAiAgent: {
      handler(newVal, oldVal) {
        console.log('🤖 全局智能体状态变化:', { oldVal, newVal });

        // 当智能体展开时，检查是否为核心网工单并发送初始化数据
        if (newVal && this.workOrderDataLoaded) {
          // 检查是否为核心网工单（网络类型=7）
          const isNetworkType7 = parseInt(this.common.networkType) === 7;

          console.log('🤖 智能体展开检查发送条件:', {
            networkType: this.common.networkType,
            isNetworkType7,
            workOrderDataLoaded: this.workOrderDataLoaded,
            isAiAgentAvailable: this.isAiAgentAvailable
          });

          if (isNetworkType7) {
            // 延迟发送，确保iframe已加载
            this.$nextTick(() => {
              setTimeout(() => {
                // 确认当前页面是激活状态（防止多页面冲突）
                const isCurrentPageActive = this.checkIfCurrentPageIsActive();
                if (isCurrentPageActive) {
                  this.sendInitDataToGlobalAgent();
                  this.sendWoMessageToAiAgent();
                  console.log('🤖 智能体展开：核心网工单且页面激活，发送智能体数据');
                } else {
                  console.log('🤖 智能体展开：当前页面不是激活状态，跳过发送智能体数据');
                }
              }, 1000);
            });
          } else {
            console.log('🤖 智能体展开：非核心网工单，跳过发送智能体数据');
          }
        }
      },
      immediate: false,
    },
  },
  created() {
    this.route = this.$route;
  },
  mounted() {
    this.common.workItemId = this.$route.query.workItemId;
    this.common.processInstId = this.$route.query.processInstId;
    this.common.woId = this.$route.query.woId;
    this.common.processDefId = this.$route.query.processDefId;
    this.fromPage = this.$route.query.fromPage;

    // 生成页面实例ID（用于区分同一工单的多个页面实例）
    this.pageInstanceId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 设置页面为活跃状态
    this.isCurrentPageActive = true;

    // 设置当前活跃工单信息（用于消息过滤）
    this.setCurrentActiveWorkOrder();

    // 初始化全局智能体（但不检查可用性，避免覆盖我们的网络类型检查）
    this.initGlobalAiAgentWithoutAvailabilityCheck();

    // 初始检查智能体可用性（此时网络类型可能还未设置，会隐藏按钮）
    this.checkAiAgentAvailabilityByNetworkType();
    this.checkMobile();
    window.addEventListener("resize", this.handleResize);

    // 添加智能体 postMessage 监听器
    this.setupAiAgentMessageListener();

    // 加载工单数据，完成后再初始化智能体
    this.getShowButton().then(() => {
      this.getWorkOrderInfo();
    });
    this.getReportRecords();
  },
  activated() {
    // 当tab切换回来时，设置页面为活跃状态并发送初始化数据给智能体
    console.log('🤖 页面激活，设置为活跃状态并发送智能体数据');

    // 设置页面为活跃状态
    this.isCurrentPageActive = true;

    // 重新设置当前活跃工单
    this.setCurrentActiveWorkOrder();

    // ========== 页面激活智能体状态管理 ==========
    // 恢复智能体按钮显示（根据网络类型检查可用性）
    this.checkAiAgentAvailabilityByNetworkType();

    // ========== 智能体数据更新 ==========
    // 页面激活时，如果智能体已展开且是核心网工单，则发送最新的工单数据
    // 这样用户在多个核心网工单间切换时，智能体能获取到正确的工单信息
    this.$nextTick(() => {
      setTimeout(() => {
        // 检查是否为核心网工单且智能体已展开
        const isNetworkType7 = parseInt(this.common.networkType) === 7;
        const isAiAgentShown = this.showAiAgent;

        console.log('🤖 页面激活检查智能体数据发送条件:', {
          networkType: this.common.networkType,
          isNetworkType7,
          isAiAgentAvailable: this.isAiAgentAvailable,
          workOrderDataLoaded: this.workOrderDataLoaded,
          showAiAgent: isAiAgentShown
        });

        // 只有当智能体已展开且是核心网工单且当前页面激活时才发送
        if (isNetworkType7 && isAiAgentShown && this.workOrderDataLoaded) {
          // 再次确认当前页面是激活状态（防止多页面冲突）
          const isCurrentPageActive = this.checkIfCurrentPageIsActive();
          if (isCurrentPageActive) {
            this.sendInitDataToGlobalAgent();
            console.log('🤖 页面激活：核心网工单且智能体已展开且页面激活，发送智能体数据');
          } else {
            console.log('🤖 页面激活：当前页面不是激活状态，跳过发送智能体数据');
          }
        } else {
          console.log('🤖 页面激活：智能体未展开或非核心网工单，跳过发送智能体数据');
        }
      }, 500);
    });
  },
  deactivated() {
    // 当tab切换离开时，设置页面为非活跃状态
    console.log('🤖 页面失活，设置为非活跃状态');
    this.isCurrentPageActive = false;

    // ========== 页面失活智能体状态管理 ==========
    // 处理页面失活时的智能体状态（根据用户偏好自动隐藏）
    this.$store.dispatch('aiAgent/handlePageDeactivated');

    // 隐藏智能体按钮（设置为不可用状态）
    this.$store.commit('aiAgent/SET_AI_AGENT_AVAILABLE', false);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    // 移除智能体消息监听器
    if (this.aiAgentMessageHandler) {
      window.removeEventListener("message", this.aiAgentMessageHandler);
    }
    // 清除当前活跃工单信息
    this.$store.dispatch('aiAgent/clearCurrentActiveWorkOrder');
  },
  methods: {
    // 设置智能体 postMessage 监听器
    setupAiAgentMessageListener() {
      // 直接使用箭头函数避免this绑定问题
      this.aiAgentMessageHandler = (event) => {
        this.handleAiAgentMessage(event);
      };
      window.addEventListener("message", this.aiAgentMessageHandler);
      console.log("🤖 智能体消息监听器已启动");
    },

    // 处理智能体发送的消息
    async handleAiAgentMessage(event) {

      // 过滤掉开发工具消息
      if (event.data && event.data.source === "react-devtools-content-script") return;
      if (event.data && event.data.source === "react-devtools-bridge") return;
      if (event.data && event.data.source === "react-devtools-detector") return;

      // 检查是否是智能体消息格式
      if (event.data && typeof event.data === 'object') {
        // 检查是否包含智能体消息的特征字段
        if (event.data.action_name || event.data.messageId !== undefined || event.data.timestamp) {
          console.group("🤖 智能体消息");
          console.log("消息来源:", event.origin);
          console.log("消息数据:", event.data);
          console.log("动作名称:", event.data.action_name || "未知");
          console.log("消息ID:", event.data.messageId || "无");
          console.log("消息载荷:", event.data.action_data);
          console.log("时间戳:", event.data.timestamp ? new Date(event.data.timestamp).toLocaleString() : "无");
          console.log("完整事件:", event);
          console.groupEnd();

          // ========== 新增：消息目标工单检查 ==========
          // 检查消息是否针对当前工单（除了initComplete消息，它总是被处理）
          if (event.data.action_name !== 'initComplete') {
            const isTargetMessage = this.checkAiAgentMessageTarget(event.data);
            if (!isTargetMessage) {
              console.log('🤖 消息不针对当前工单，忽略处理');
              return;
            }
          }

          // 处理智能体动作
          switch (event.data.action_name) {
            case 'CloseIframe':
              // 关闭全局智能体
              this.$store.dispatch('aiAgent/hideAiAgent');
              break;
            case '受理':
            case '工单受理':
              // 工单受理 - 使用智能体返回的参数直接更新
              this.$message.success('工单受理成功');
              this.handleAiAgentAcceptSuccess(event.data.action_data);
              break;

            case '阶段反馈':
              // 阶段反馈 - 刷新页面
              
              // this.refreshPage();
              this.getWorkOrderInfo();
              this.$message.success('阶段反馈成功');
              break;

            case '阶段反馈-核心网':
              // 阶段反馈 - 刷新页面
              
              this.getWorkOrderInfo();
              // this.refreshPage();
              this.$message.success('阶段反馈成功');
              break;

            case '转派':
              // 转派 - 使用现有的关闭方法
              
              this.closeAndTurnAround();
              this.$message.success('转派成功');
              // 关闭全局智能体
              this.$store.dispatch('aiAgent/hideAiAgent');
              break;

            case '异常终止':
              // 异常终止 - 使用现有的关闭方法
              
              this.closeAndTurnAround();
              this.$message.success('异常终止成功');
              // 关闭全局智能体
              this.$store.dispatch('aiAgent/hideAiAgent');
              break;

            case '挂起申请':
              // 挂起申请 - 使用现有的关闭方法
              
              this.closeAndTurnAround();
              this.$message.success('挂起申请成功');
              // 关闭全局智能体
              this.$store.dispatch('aiAgent/hideAiAgent');
              break;

            case '挂起':
              // 挂起申请 - 使用现有的关闭方法
              
              this.closeAndTurnAround();
              this.$message.success('挂起申请成功');
              // 关闭全局智能体
              this.$store.dispatch('aiAgent/hideAiAgent');
              break;

            case '解挂申请':
              // 解挂申请 - 使用现有的关闭方法
              
              this.closeAndTurnAround();
              this.$message.success('解挂申请成功');
              // 关闭全局智能体
              this.$store.dispatch('aiAgent/hideAiAgent');
              break;

            case '解挂':
              // 解挂申请 - 使用现有的关闭方法
              
              this.closeAndTurnAround();
              this.$message.success('解挂申请成功');
              // 关闭全局智能体
              this.$store.dispatch('aiAgent/hideAiAgent');
              break;

            case '返单':
              // 返单 - 先检查权限，再决定是否关闭
              this.$message.success('返单成功');
              // 调用权限检查方法，根据智能体返回的数据决定后续操作
              this.getIsHaveAuth({
                processInstId: event.data.action_data.processInstId,
                workItemId: event.data.action_data.workItemId,
                processDefId: event.data.action_data.processDefId,
                // "processDefId": 23361
                processDefId: event.data.action_data.processDefId
              });
              break;
            
            case '流程诊断图':
              console.log('流程诊断图', this.$refs.lct)
              this.$refs.lct.$el.scrollIntoView({ behavior: 'smooth' })
              break;

            case '返单-核心网':
              // 返单 - 先检查权限，再决定是否关闭
              this.$message.success('返单成功');
              // 调用权限检查方法，根据智能体返回的数据决定后续操作
              this.getIsHaveAuth({
                processInstId: event.data.action_data.processInstId,
                workItemId: event.data.action_data.workItemId,
                processDefId: event.data.action_data.processDefId,
                // networkTypeTop: event.data.action_data.networkType
              });
              break;

            case 'woNewTab':
              // 打开新的工单Tab页
              if (event.data.action_data && event.data.action_data.sheet_no) {
                this.openNewWorkOrderTab(event.data.action_data.sheet_no);
              } else {
                // this.$message.error('缺少工单编号参数');
              }
              break;

            case 'fetchLoaction':
              // 获取位置 - 发送当前位置信息给智能体
              this.sendLocationToAgent();
              break;

            case 'initComplete':
              // 智能体初始化完成 - 发送工单参数数据给全局智能体
              this.sendInitDataToGlobalAgent();
              break;

            default:
              console.log('未处理的智能体动作:', event.data.action_name);
              break;
          }
        }
      }
    },

    setTime(time) {
      if (time == "-") {
        return "-";
      } else if (time <= 0) {
        const absTime = Math.abs(time);
        let format = "已超时";
        if (absTime < 60) {
          format += `${absTime}分`;
        } else {
          const minFormat = absTime % 60 == 0 ? "" : `${absTime % 60}分`;
          format += `${Math.floor(absTime / 60)}小时${minFormat}`;
        }
        return format;
      } else if (time > 0) {
        if (time < 60) {
          return `${time}分`;
        } else {
          const minFormat = time % 60 == 0 ? "" : `${time % 60}分`;
          return `${Math.floor(time / 60)}小时${minFormat}`;
        }
      }
    },
    setDialogOneKeyIvrNotice(val) {
      this.dialogOneKeyIvrNotice = val;
    },
    changeRuleVisible() {
      this.ruleVisible = true;
    },
    professionalTypeChange(value) {
      if (
        value == "1" ||
        value == "5" ||
        value == "6" ||
        value == "12" ||
        value == "13" ||
        value == "19" ||
        value == "20" ||
        value == "21" ||
        value == "25" ||
        value == "28"
      ) {
        this.ruleButtonVisible = true;
      } else {
        this.ruleButtonVisible = false;
      }
    },
    getShowButton() {
      return new Promise(resolve => {
        let param = {
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          processInstId: this.common.processInstId,
          fromPage: this.fromPage,
        };
        apiGetShowButton(param)
          .then(res => {
            if (res.status == "0") {
              let self = this;
              self.processButtonArr = res?.data?.user ?? [];
              // 临时添加
              // self.processButtonArr.push({
              //   "key": "links",
              //   "value": "转办",
              //   "desc": "edit"
              // });
              let alarmButton = res?.data?.admin ?? [];
              let qualitativeReviewButton = res?.data?.admin ?? [];
              let reportReviewButton = res?.data?.admin ?? [];
              if (
                qualitativeReviewButton.length > 0 &&
                qualitativeReviewButton[0].value == "定性"
              ) {
                this.isShowQualitative = true;
                this.qualitativeType = "定性";
              } else {
                this.isShowQualitative = false;
              }
              if (
                qualitativeReviewButton.length > 0 &&
                qualitativeReviewButton[0].value == "定性审核"
              ) {
                this.isShowQualitativeReviewButton = true;
                this.qualitativeType = "定性审核";
              } else {
                this.isShowQualitativeReviewButton = false;
              }
              if (
                reportReviewButton.length > 0 &&
                reportReviewButton[0].value == "故障报告审核"
              ) {
                this.isShowReportReview = true;
              } else {
                this.isShowReportReview = false;
              }
              if (
                alarmButton.length > 0 &&
                alarmButton.some(item => item.value == "清除告警申请")
              ) {
                this.isShowClearAlarmApply = true;
              } else {
                this.isShowClearAlarmApply = false;
              }
              if (
                alarmButton.length > 0 &&
                alarmButton.some(item => item.value == "人工确认清除告警")
              ) {
                this.isShowManualConfirm = true;
              } else {
                this.isShowManualConfirm = false;
              }
            }
            resolve("success");
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    getWorkOrderInfo() {
      this.pageLoading = true;
      let formData = new FormData();
      formData.append("woId", this.common.woId);
      formData.append(
        "processInstId",
        this.common.processInstId ? this.common.processInstId : ""
      );
      formData.append(
        "workItemId",
        this.common.workItemId ? this.common.workItemId : ""
      );
      apiGetWorkOrderInfo(formData)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            self.basicWorkOrderData = res?.data?.rows?.[0] ?? {};
            self.headInfo.title = self.basicWorkOrderData.sheetTitle;

            self.headInfo.sheetId = self.basicWorkOrderData.sheetNo;
            self.headInfo.currentHandler =
              self.basicWorkOrderData.operatePerson; // 当前处理人
            self.headInfo.acceptTimeLimit =
              self.basicWorkOrderData.remainAcceptTime;
            self.headInfo.estimatedProTimeLimit =
              self.basicWorkOrderData.remainHandleTime;
            self.headInfo.sheetStatus = self.basicWorkOrderData.sheetStatus;
            self.headInfo.buildSingleMan = self.basicWorkOrderData.senderName;
            self.headInfo.buildSingleDept =
              self.basicWorkOrderData.senderDeptName;
            self.headInfo.buildSingleTime =
              self.basicWorkOrderData.sheetCreateTime;
            this.getWorkOrderSpentTime(self.basicWorkOrderData); //工单总耗时
            self.headInfo.sourceInfo = self.basicWorkOrderData.createType;
            self.headInfo.occurrenceTime =
              self.basicWorkOrderData.alarmCreateTime;
            self.headInfo.emergencyDegree =
              self.basicWorkOrderData.emergencyLevel;
            self.common.failureTime = self.basicWorkOrderData.alarmCreateTime;
            self.common.failureInformTime =
              self.basicWorkOrderData.sheetCreateTime;
            self.common.processNode = self.basicWorkOrderData.processNode;
            self.common.sheetNo = self.basicWorkOrderData.sheetNo;
            self.common.isSender =
              self.basicWorkOrderData.senderName == self.userInfo.realName
                ? 1
                : 0;
            self.common.agentManId = self.basicWorkOrderData.agentManId;
            self.common.agentMan = self.basicWorkOrderData.agentMan;
            self.common.agentDeptCode = self.basicWorkOrderData.agentDeptCode;
            self.common.agentDeptName = self.basicWorkOrderData.agentDeptName;
            self.common.copyDeptCode = self.basicWorkOrderData.copyDeptCode;
            self.common.copyDeptName = self.basicWorkOrderData.copyDeptName;
            // 添加受理时限
            self.common.acceptTimeLimit = self.basicWorkOrderData.acceptTimeLimit;
            self.common.copyManId = self.basicWorkOrderData.copyManId;
            self.common.copyMan = self.basicWorkOrderData.copyMan;
            self.common.sheetStatus = self.headInfo.sheetStatus;
            self.timing.hangTime = self.basicWorkOrderData.suspendDuration;
            self.common.professionalType =
              self.basicWorkOrderData.professionalTypeId;
            self.common.professionalTypeName =
              self.basicWorkOrderData.professionalType;
            // 设置网络类型（用于智能体可用性判断）
            self.common.networkType = self.basicWorkOrderData.networkTypeTop ||
                                     self.basicWorkOrderData.networkTypeId ||
                                     self.route.query.networkType;

            // 根据网络类型立即检查智能体可用性
            self.checkAiAgentAvailabilityByNetworkType();
            self.common.hangOver = self.basicWorkOrderData.suspendDuration;
            self.common.faultCauseDescription =
              self.basicWorkOrderData.ppResult;
            self.common.busName = self.basicWorkOrderData.busName;
            self.common.auditResult = self.basicWorkOrderData.auditResult;
            self.common.isUploadReport = self.basicWorkOrderData.isUploadReport;
            self.common.remainAcceptTime =
              self.basicWorkOrderData.remainAcceptTime;
            self.common.alarmClearTime = self.basicWorkOrderData.alarmClearTime;
            self.common.mecNodeName = self.basicWorkOrderData.mecNodeName;
            self.common.emergencyLevel = self.basicWorkOrderData.emergencyLevel;
            this.ppResult = self.basicWorkOrderData.ppResultOri;
            this.analysisStatus = self.basicWorkOrderData.analysisStatus;
            this.showClxq = false;
            this.showLcrz = false;
            this.showLct = false;
            this.$nextTick(() => {
              this.showClxq = true;
              this.showLcrz = true;
              this.showLct = true;
            });
            if (
              self.basicWorkOrderData.auditResult == "0" ||
              self.basicWorkOrderData.feedbackResult == "0" ||
              self.headInfo.sheetStatus == "待定性审核" ||
              self.headInfo.sheetStatus == "待定性" ||
              self.headInfo.sheetStatus == "待归档" ||
              self.headInfo.sheetStatus == "已归档" ||
              self.headInfo.sheetStatus == "作废" ||
              self.headInfo.sheetStatus == "故障报告审核" ||
              self.headInfo.sheetStatus == "上传故障报告"
            ) {
              this.showFkdxq = false;
              this.$nextTick(() => {
                this.showFkdxq = true;
              });
            } else {
              this.showFkdxq = false;
            }
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.pageLoading = false;
          // 标记工单数据已加载完成
          this.workOrderDataLoaded = true;
          // 工单数据加载完成后，初始化智能体功能
          this.initAiAgentAfterDataLoaded();

          // 页面刷新完成后，发送工单信息变更消息给智能体
          this.$nextTick(() => {
            setTimeout(() => {
              this.sendWoMessageToAiAgent();
            }, 500); // 延迟500ms确保智能体已准备好接收消息
          });
        });
    },
    getWorkOrderSpentTime(basicData) {
      if (
        basicData.sheetStatus == "异常归档" ||
        basicData.sheetStatus == "已归档" ||
        basicData.sheetStatus == "作废"
      ) {
        let days = moment(
          basicData.sheetFilingTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      } else {
        let days = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      }
    },
    buttonClick(name, desc) {
      this.common.actionName = name;
      switch (name) {
        case "受理":
          this.accept(name);
          break;
        case "追单":
          this.common.isObject = desc;
          this.dialogAfterSingleVisible = true;
          break;
        case "撤单":
          this.dialogRevokeVisible = true;
          break;
        case "已阅":
          this.haveRead();
          break;
        case "返单":
          // 集团通用流程返单约束检查
          console.log("集团通用流程返单约束检查", this.common);
          if (this.common.networkType == 5) {
            this.checkGroupGeneralBackSingleConstraints();
          } else {
            //2023-10-19 需求变更（邮件）：去除集团通用流程现有“主告警清除，才可进行返单操作”的校验逻辑。
            // if (this.isBackSingle) {
            this.openBackSingleDialog();
            // } else {
            //   this.$message.warning("告警未被清除，不可返单");
            // }
          }
          break;
        case "阶段反馈":
          this.dialogStageFeedbackVisible = true;
          break;
        case "挂起":
          this.dialogHangUpVisible = true;
          break;
        case "解挂":
          this.dialogSolutionToHangVisible = true;
          break;
        case "挂起审核":
          this.opContent = 1;
          this.dialogPendingReviewVisible = true;
          break;
        case "解挂审核":
          this.opContent = 2;
          this.dialogSolutionToHangAuditVisible = true;
          break;
        case "异常终止":
          this.dialogAbendVisible = true;
          break;
        case "异常终止审核":
          this.dialogAbendAuditVisible = true;
          break;
        case "转派":
          this.dialogTurnToSendVisible = true;
          break;
        case "转办":
          this.dialogTurnToVisible = true;
          break;
        case "回转":
          this.rotary();
          break;
        case "故障报告转派":
          this.dialogReportTransferVisible = true;
          break;
        case "重新转派":
          this.dialogAgainTurnToSendVisible = true;
          break;
        case "一键催办IVR":
          this.dialogOneKeyIvrNotice = true;
          break;
        case "定性":
          this.dialogQualitativeVisible = true;
          break;
        case "定性审核":
          this.showDxsh = false;
          this.$nextTick(() => {
            this.showDxsh = true;
          });
          this.dialogQualitativeReviewVisible = true;
          break;
        case "上传故障报告":
          this.dialogReportUpVisible = true;
          break;
      }
    },

    // 检查集团通用流程返单约束条件
    checkGroupGeneralBackSingleConstraints() {
      // 需要检查主告警清除时间的专业类型列表
      const specialProfessionalTypes = ['骨干云池', 'MEC'];
      const professionalType = this.basicWorkOrderData?.professionalType;
      console.log('professionalType', this.basicWorkOrderData?.professionalType);

      if (specialProfessionalTypes.includes(professionalType)) {
        // 检查主告警是否有告警清除时间/人工告警清除时间
        this.checkMainAlarmClearTime();
      } else {
        // 其他专业直接打开返单弹窗
        this.openBackSingleDialog();
      }
    },

    // 检查主告警清除时间
    async checkMainAlarmClearTime() {
      // 获取告警详情数据
      const param = {
        param1: JSON.stringify({
          woId: this.common.woId,
        }),
        pageIndex: 1,
        pageSize: 1000, // 获取所有告警数据
      };

      try {
        const res = await apiQueryAlarmDetail(param);
        if (res.status !== "0") {
          this.$message.error(res.msg || "获取告警信息失败");
          return;
        }

        const alarmData = res?.data?.rows ?? [];
        const mainAlarm = alarmData.find(alarm => alarm.isMajorAlarm === "是");

        // 如果没有主告警，或者主告警的清除时间存在，则可以返单
        const canBackSingle = !mainAlarm || mainAlarm.autoClearTime || mainAlarm.manualClearTime || mainAlarm.manualAckClearTime;
        console.log("canBackSingle:", mainAlarm , mainAlarm.autoClearTime , mainAlarm.manualClearTime , mainAlarm.manualAckClearTime);
        if (canBackSingle) {
          this.openBackSingleDialog();
        } else {
          this.$message.error("告警未被清除，不可返单！");
        }
      } catch (error) {
        console.error("获取告警信息失败:", error);
        this.$message.error("获取告警信息失败");
      }
    },

    // 打开返单弹窗
    openBackSingleDialog() {
      this.dialogBackSingleVisible = true;

      // 立即处理云资源流程的故障结束时间初始化
      this.$nextTick(() => {
        if (this.$refs.backSingleForm) {
          this.$refs.backSingleForm.handleDialogOpen();
        }
      });
    },

    // 回转
    rotary() {
      apiRotationSubmit({
        "processInstId": this.common.processInstId,
        "workItemId": this.common.workItemId,
        "woId": this.common.woId,
        "sheetNo": this.common.sheetNo
      }).then(res => {
        console.log(res);
        if(res.status == 0){
          this.$message.success('回转成功');
          this.closeAndTurnAround();
        }else{
          this.$message.error(res.msg);
        }
        // this.closeAndTurnAround();
      }).catch(error => {
        this.$message.error('回转失败');
        console.log(error);
      });

    },
    accept(buttonName) {
      this.processFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        actionName: buttonName,
        processInsId: this.common.processInstId,
      };
      apiAccept(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("您已受理成功");
            this.common.workItemId = res.data.workItemId;
            this.common.processDefId = res.data.processDefId;
            this.common.processInstId = res.data.processInstId;
            this.common.networkType = res.data.networkTypeTop;
            //根据新生成的workItemId再次调用显示按钮的接口
            this.getShowButton();
            this.getWorkOrderInfo();
            this.JumpDetails_Common(this.common);

            // 受理成功后，发送工单信息变更消息给智能体
            this.$nextTick(() => {
              setTimeout(() => {
                this.sendWoMessageToAiAgent();
              }, 2000); // 延迟1秒确保工单数据已更新且智能体已准备好接收消息
            });
          } else {
            this.$message.error("受理失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("受理失败");
          this.processFullscreenLoading = false;
        });
    },
    dialogBackSingleClose() {
      this.dialogBackSingleVisible = false;
      this.$refs.backSingleForm.onReset();
    },
    //返单提交
    dialogBackSingleSubmitClose(data) {
      console.log("dialogBackSingleSubmitClose(data)",data);
      this.common.workItemId = data.workItemId;
      this.common.processInstId = data.processInstId;
      this.common.processDefId = data.processDefId;
      this.common.networkType = data.networkTypeTop;
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogBackSingleVisible = false;
      if (data.currentPage) {
        this.JumpDetails_Common(this.common);
      } else {
        // 关闭全局智能体
        this.$store.dispatch('aiAgent/hideAiAgent');
        this.closeAndTurnAround();
      }
    },
    //定性提交
    qualitativeSubmit(data) {
      this.dialogQualitativeVisible = false;
      this.closeAndTurnAround();
    },
    qualitativeClose() {
      this.dialogQualitativeVisible = false;
      this.$refs.qualitative.onReset();
    },
    qualitativeReviewClose() {
      this.dialogQualitativeReviewVisible = false;
      this.$refs.qualitativeReview.onReset();
    },
    //定性审核提交
    qualitativeReviewSubmit(data) {
      this.dialogQualitativeReviewVisible = false;
      this.closeAndTurnAround();
    },
    //故障报告审核通过
    closeDialogReportAudit() {
      this.closeAndTurnAround();
    },
    dialogStageFeedbackClose() {
      this.dialogStageFeedbackVisible = false;
    },
    stageBackDialogCommitClose() {
      this.showClxq = false;
      this.showLcrz = false;
      this.$nextTick(() => {
        this.showLcrz = true;
        this.showClxq = true;
      });
      this.dialogStageFeedbackVisible = false;
      // 智能体刷新工单信息
      this.sendWoMessageToAiAgent();
    },
    handleRevokeSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.revokeSubmitLoading = true;
          let param = {
            processNode: this.common.processNode,
            sheetNo: this.common.sheetNo,
          };
          apiRevoke(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("撤单成功");
                this.dialogRevokeClose();
                this.closeAndTurnAround();
              } else {
                this.$message.error("撤单失败");
              }
              this.revokeSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("撤单失败");
              this.revokeSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    onResetRevoke() {
      this.revokeForm = {
        ...this.$options.data,
      };
    },
    //撤单
    dialogRevokeClose() {
      this.onResetRevoke();
    },
    //挂起关闭
    dialogHangUpClose() {
      this.dialogHangUpVisible = false;
    },
    //挂起提交
    dialogHangUpSubmitClose() {
      this.dialogHangUpVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //解挂关闭
    dialogSolutionToHangClose() {
      this.dialogSolutionToHangVisible = false;
    },
    //解挂提交
    dialogSolutionToHangSubmitClose() {
      this.dialogSolutionToHangVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //挂起审核关闭
    dialogPendingReviewClose() {
      this.dialogPendingReviewVisible = false;
    },
    //挂起审核提交
    dialogPendingReviewSubmitClose() {
      this.dialogPendingReviewVisible = false;
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.closeAndTurnAround();
    },
    //解挂审核关闭
    dialogSolutionToHangAuditClose() {
      this.dialogSolutionToHangAuditVisible = false;
    },
    //解挂审核提交
    dialogSolutionToHangAuditSubmitClose() {
      this.dialogSolutionToHangAuditVisible = false;
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.closeAndTurnAround();
    },
    dialogAbendClose() {
      this.dialogAbendVisible = false;
    },
    //异常终止提交
    dialogAbendSubmitClose() {
      this.dialogAbendVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    dialogAbendAuditClose() {
      this.dialogAbendAuditVisible = false;
    },
    //异常终止审核
    dialogAbendAuditSubmitClose() {
      this.processButtonArr = [];
      this.dialogAbendAuditVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //追单
    dialogAfterSingleClose() {
      this.dialogAfterSingleVisible = false;
    },
    //追单
    dialogAfterSingleSubmitClose(val) {
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogAfterSingleVisible = false;
      if (val == 1) {
        this.closeAndTurnAround();
      }
    },
    //已阅
    haveRead() {
      this.processFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiHaveRead(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("已阅");
            this.closeAndTurnAroundRead();
          } else {
            this.$message.error("已阅失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("已阅失败");
          this.processFullscreenLoading = false;
        });
    },
    //转派
    dialogTurnToSendClose() {
      this.dialogTurnToSendVisible = false;
    },
    //转办
    dialogTurnToClose() {
      this.dialogTurnToVisible = false;
    },
    //转派提交
    dialogTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogTurnToSendVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogTurnToSendVisible = false;
      }
    },
    //转派提交
    dialogTurnToSubmitClose(val) {
      if (val == "1") {
        this.dialogTurnToVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogTurnToVisible = false;
      }
    },
    //重新转派
    dialogAgainTurnToSendClose() {
      this.dialogAgainTurnToSendVisible = false;
    },
    //故障报报转派
    dialogReportTransferClose(val) {
      if (val == "1") {
        this.dialogReportTransferVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogReportTransferVisible = false;
      }
    },
    dialogAgainTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogAgainTurnToSendVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogAgainTurnToSendVisible = false;
      }
    },
    getStatusStyle() {
      if (
        this.common.sheetStatus == "待定性" ||
        this.common.sheetStatus == "待定性审核"
      ) {
        return "color:#b50b14;border-color:#e9b6b9;background-color:#f8e7e8";
      }
    },
    onHeadHandleClick(command) {
      this.$refs[command]?.$el?.scrollIntoView?.({ behavior: "smooth" });
    },
    alarmClearCallBack(data) {
      this.isBackSingle = data;
    },
    showTime(val) {
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分";
      }
      return time;
    },
    closeAndTurnAround() {
      // 其余关闭窗口后发送消息给智能体
      this.sendWoMessageToAiAgent();
      // 工单侧设计页面关闭后，关闭全局智能体
      this.$store.dispatch('aiAgent/hideAiAgent');

      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    closeAndTurnAroundRead() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toReadRepairOrder",
      });
    },
    currentProcessor() {
      this.dialogCurrentProcessorVisible = true;
    },
    currentProcessorClose() {
      this.dialogCurrentProcessorVisible = false;
    },

    // 智能体返单成功后的权限检查方法（复用BackSingle.vue的逻辑）
    getIsHaveAuth(data) {
      //是否刷新当前页面
      let params = {
        userName: this.userInfo.userName,
        processInstId: data.processInstId,
      };
      apiIsHaveAuth(params)
        .then(res => {
          if (res.status == "0") {
            // 智能体返回的数据决定是否有权限继续操作
            data.currentPage = res.data;
            this.dialogBackSingleSubmitClose(data)
          } else {
            this.$message.error("获取是否刷新当前页面数据失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("获取是否刷新当前页面数据失败");
        });
    },
    JumpDetails_Common(data) {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });

      // 根据networkTypeTop或专业类型确定frameTabTitle
      let frameTabTitle = "";
      const networkTypeTop = data.networkType || this.common.networkType;
      const professionalTypeName = this.basicWorkOrderData?.professionalType || this.common.professionalTypeName;

      if (networkTypeTop == 5) {
        frameTabTitle = "集团通用故障工单";
      } else if (networkTypeTop == 7) {
        frameTabTitle = "核心网故障工单";
      } else if (networkTypeTop == 8) {
        frameTabTitle = "IP专业故障工单";
      } else if (networkTypeTop == 9) {
        frameTabTitle = "平台故障工单";
      } else if (professionalTypeName) {
        // 如果有专业类型名称，使用专业类型名称
        frameTabTitle = professionalTypeName + "故障工单";
      } else {
        // 默认使用通用标题
        frameTabTitle = "故障工单";
      }

      this.$router.replace({
        name: "common_orderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "待办工单",
          networkType: data.networkType,
          frameTabTitle: frameTabTitle,
          professionalType: this.common.professionalType,
        },
      });
    },
    getReportRecords() {
      apiGetReportUpLog({
        woId: this.common.woId,
      })
        .then(res => {
          if (res.status == 0) {
            this.reportRecordsData = res?.data ?? [];
            // this.reportRecordsData = [{ auditStatus: "成功" }];
            if (this.reportRecordsData.length > 0) {
              this.reportRecordsData.forEach(el => {
                el.appendix = JSON.parse(el.appendix);
                if (el.data.length > 0) {
                  el.data.forEach(tl => {
                    tl.appendix = JSON.parse(tl.appendix);
                  });
                }
              });
              let isContains = this.headMoreDrops.some(
                item => item.command == "gzbgscjl"
              );
              if (!isContains) {
                this.headMoreDrops.unshift({
                  command: "gzbgscjl",
                  title: "故障报告上传记录",
                });
              }
              this.reportRecordsShow = true;
            } else {
              this.reportRecordsShow = false;
              this.headMoreDrops = this.headMoreDrops.filter(
                obj => obj.command != "gzbgscjl"
              );
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },

    // ==================== 全局智能体相关方法 ====================

    // 初始化全局智能体（不检查可用性，避免覆盖网络类型检查）
    initGlobalAiAgentWithoutAvailabilityCheck() {
      try {
        // 只设置路由信息，不检查可用性
        this.$store.commit('aiAgent/SET_CURRENT_ROUTE', this.$route);

        // 恢复用户偏好设置
        const savedPreferences = localStorage.getItem('aiAgent_preferences');
        if (savedPreferences) {
          try {
            const preferences = JSON.parse(savedPreferences);
            this.$store.commit('aiAgent/UPDATE_USER_PREFERENCES', preferences);
          } catch (error) {
            console.warn('Failed to parse AI agent preferences:', error);
          }
        }

        console.log('🤖 全局智能体已初始化（跳过可用性检查）');
      } catch (error) {
        console.error('Failed to initialize global AI agent:', error);
      }
    },

    // 工单数据加载完成后的智能体初始化
    async initAiAgentAfterDataLoaded() {
      try {
        // 更新全局智能体的工单数据
        await this.updateGlobalAiAgentData();

        console.log('🤖 全局智能体工单数据已更新:', {
          basicWorkOrderData: this.basicWorkOrderData,
          headInfo: this.headInfo,
          common: this.common
        });

        // 如果智能体已经显示，立即发送初始化数据
        if (this.showAiAgent) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.sendInitDataToGlobalAgent();
              console.log('🤖 页面刷新后发送智能体初始化数据');
            }, 1000); // 延迟1秒确保智能体iframe已加载
          });
        }
      } catch (error) {
        console.error('Failed to update global AI agent data:', error);
      }
    },

    // 根据网络类型检查智能体可用性
    checkAiAgentAvailabilityByNetworkType() {
      console.log('🤖 根据网络类型检查智能体可用性:', {
        networkType: this.common.networkType,
        networkTypeType: typeof this.common.networkType,
        isEmpty: !this.common.networkType
      });

      // 如果网络类型还没有设置，隐藏智能体按钮
      if (!this.common.networkType) {
        console.log('🤖 网络类型未设置，隐藏智能体按钮');
        this.$store.commit('aiAgent/SET_AI_AGENT_AVAILABLE', false);
        return;
      }

      // 只有当网络类型为 7 时才显示智能体按钮
      const networkTypeNum = parseInt(this.common.networkType);
      const isAvailable = networkTypeNum === 7;

      // 直接设置智能体可用性状态
      this.$store.commit('aiAgent/SET_AI_AGENT_AVAILABLE', isAvailable);

      console.log('🤖 智能体可用性检查结果:', {
        networkType: this.common.networkType,
        networkTypeNum,
        isAvailable,
        shouldShow: isAvailable ? '显示按钮' : '隐藏按钮'
      });
    },

    // 获取智能体URL参数（通过URL query传递的基础参数）
    getAiAgentUrlParams() {
      // 获取主告警数据
      const majorAlarm = this.getMajorAlarmData();

      // 构建工单标题
      const workOrderTitle = `工单编号：${this.headInfo.sheetId || ''}`;

      // 构建工单描述
      const workOrderDescription = `[${this.basicWorkOrderData?.professionalType || '核心网'}故障工单]${this.headInfo.title || ''}`;

      // 获取真实的网元名称（优先从告警数据获取）
      const networkElementName = this.getRealNetworkElementName();

      // 获取真实的厂商信息（优先从告警数据获取）
      const vendorName = this.getRealVendorName();

      // 获取真实的省份信息（优先从告警数据获取）
      const provinceName = this.getRealProvinceName();

      // 获取真实的经纬度信息（优先从告警数据获取）
      const coordinates = this.getRealCoordinates();
      console.log('获取到的用户信息：', JSON.parse(this.userInfo.attr2));
      const userInfo = JSON.parse(this.userInfo.attr2);
      return {
        // 智能体场景标识（核心网专业）
        scene: 'CNFaultAgent',
        // 用户名称
        // username: this.userInfo.realName || '',
        // 工单标题
        // title: workOrderTitle,
        // 工单描述
        describe: workOrderDescription,
        // 组织ID
        // orgId: this.userInfo.orgId || '',
        // 工单接口token
        ai_wo_token: this.token || '',
        // 工单号
        ai_wo_id: this.common.woId || '',
        // 工作项ID（步骤ID）
        ai_wo_step_id: this.common.workItemId || '',
        // 流程实例ID
        ai_wo_process_id: this.common.processInstId || '',
        // 流程模版ID
        ai_wo_prodef_id: this.common.processDefId || '',
        // 工单状态
        ai_wo_status: this.headInfo.sheetStatus || '',
        // 工单领域（专业中文）
        ai_wo_domain: this.basicWorkOrderData?.professionalType || '核心网',
        // 客户端类型枚举：PC，MOBILE
        ai_client_type: 'PC',
        // 告警唯一标识
        ai_alarm_uniqueid: majorAlarm?.alarmStaId || '',
        // 告警发生时间：2024-09-18 00:57:56
        ai_alarm_event_time: majorAlarm?.alarmCreateTime || '',
        // 网元名称（核心网）
        ai_ne_name: networkElementName,
        // 厂商
        ai_vendor: vendorName,
        // 省份
        ai_province: provinceName,
        // 用户实时经度
        ai_user_longitude: coordinates.longitude,
        // 用户实时纬度
        ai_user_latitude: coordinates.latitude,

        // ========== 新增参数 ==========
        // 对话显示的中文名称
        ai_user_name: userInfo.userName || '',
        ai_true_name: userInfo.trueName || '',
        ai_user_id: this.userInfo.userId || '',
        // 工单大标题（工单标题）
        ai_wo_title: this.headInfo.title || '',
        // 工单小标题（工单编号）
        ai_wo_sheet_no: this.headInfo.sheetId || '',
        // 组织ID（与orgId保持一致）
        ai_org_id: userInfo.orgInfo.orgId || '',
        ai_wo_process_type: this.getWorkOrderProcessType(),
        ai_wo_process_node: this.common.processNode || '',
        ai_org_name:userInfo.orgInfo.fullOrgName || '',
        ai_user_phone:userInfo.mobilePhone || '',
      };
    },

    // 获取工单处理类型
    getWorkOrderProcessType() {
      // 优先使用fromPage参数判断
      if (this.fromPage === '待办工单') {
        return '待办';
      }
      // 如果fromPage为工单查询或其他非待办页面，则为非待办
      else if (this.fromPage === '工单查询' || this.fromPage === '已阅工单' || this.fromPage === '待阅工单') {
        return '非待办';
      }else {
        return '非待办';
      }
    },

    // 获取主告警数据
    getMajorAlarmData() {
      // 通过AlarmDetail组件获取告警数据
      const alarmDetailComponent = this.$refs.gjxq;
      if (alarmDetailComponent && alarmDetailComponent.tableData) {
        // 从告警列表中找到isMajorAlarm=是的主告警
        const majorAlarm = alarmDetailComponent.tableData.find(alarm =>
          alarm.isMajorAlarm === '是'
        );
        return majorAlarm;
      }

      // 如果AlarmDetail组件还没有加载数据，返回null
      console.log('AlarmDetail component not ready or no alarm data available');
      return null;
    },

    // 获取真实的网元名称（优先从告警数据获取）
    getRealNetworkElementName() {
      // 1. 优先从主告警数据获取
      const majorAlarm = this.getMajorAlarmData();
      if (majorAlarm) {
        // 核心网专业优先使用neName，其次locateNeName
        if (majorAlarm.neName) {
          return majorAlarm.neName;
        }
        if (majorAlarm.locateNeName) {
          return majorAlarm.locateNeName;
        }
      }

      // 2. 从工单基础数据获取
      if (this.basicWorkOrderData) {
        if (this.basicWorkOrderData.neName) {
          return this.basicWorkOrderData.neName;
        }
        if (this.basicWorkOrderData.locateNeName) {
          return this.basicWorkOrderData.locateNeName;
        }
        if (this.basicWorkOrderData.deviceName) {
          return this.basicWorkOrderData.deviceName;
        }
      }

      return '';
    },

    // 获取真实的厂商信息（优先从告警数据获取）
    getRealVendorName() {
      // 1. 优先从主告警数据获取
      const majorAlarm = this.getMajorAlarmData();
      if (majorAlarm && majorAlarm.alarmVendor) {
        return majorAlarm.alarmVendor;
      }

      // 2. 从工单基础数据获取
      if (this.basicWorkOrderData) {
        if (this.basicWorkOrderData.commandVendor) {
          return this.basicWorkOrderData.commandVendor;
        }
        if (this.basicWorkOrderData.vendor) {
          return this.basicWorkOrderData.vendor;
        }
        if (this.basicWorkOrderData.deviceVendor) {
          return this.basicWorkOrderData.deviceVendor;
        }
      }

      return '';
    },

    // 获取真实的省份信息（优先从告警数据获取）
    getRealProvinceName() {
      // 1. 优先从主告警数据获取
      const majorAlarm = this.getMajorAlarmData();
      if (majorAlarm && majorAlarm.alarmProvince) {
        return majorAlarm.alarmProvince;
      }

      // 2. 从工单基础数据获取
      if (this.basicWorkOrderData) {
        if (this.basicWorkOrderData.province) {
          return this.basicWorkOrderData.province;
        }
        if (this.basicWorkOrderData.provinceName) {
          return this.basicWorkOrderData.provinceName;
        }
      }

      // 3. 从用户信息获取（如果有组织省份信息）
      if (this.userInfo && this.userInfo.provinceName) {
        return this.userInfo.provinceName;
      }

      return '';
    },

    // 获取真实的经纬度信息（优先从告警数据获取）
    getRealCoordinates() {
      // 1. 优先从主告警数据获取
      const majorAlarm = this.getMajorAlarmData();
      if (majorAlarm) {
        const longitude = majorAlarm.longitude || '';
        const latitude = majorAlarm.latitude || '';
        if (longitude && latitude) {
          return { longitude, latitude };
        }
      }

      // 2. 从工单基础数据获取
      if (this.basicWorkOrderData) {
        const longitude = this.basicWorkOrderData.longitude || '';
        const latitude = this.basicWorkOrderData.latitude || '';
        if (longitude && latitude) {
          return { longitude, latitude };
        }
      }

      return { longitude: '', latitude: '' };
    },

    // 调试方法：打印所有可用的数据源
    debugDataSources() {
      console.log('=== AI智能体参数数据源调试 ===');

      console.log('1. 工单基础数据 (basicWorkOrderData):', this.basicWorkOrderData);
      console.log('2. 工单通用数据 (common):', this.common);
      console.log('3. 头部信息 (headInfo):', this.headInfo);
      console.log('4. 用户信息 (userInfo):', this.userInfo);

      // 告警数据
      const alarmDetailComponent = this.$refs.gjxq;
      if (alarmDetailComponent) {
        console.log('5. 告警组件数据 (AlarmDetail):', {
          tableData: alarmDetailComponent.tableData,
          alarmStaIdArr: alarmDetailComponent.alarmStaIdArr,
          alarmCreateTime: alarmDetailComponent.alarmCreateTime
        });

        const majorAlarm = this.getMajorAlarmData();
        console.log('6. 主告警数据:', majorAlarm);
      } else {
        console.log('5. 告警组件未找到');
      }

      // 最终参数
      const finalParams = this.getAiAgentUrlParams();
      console.log('7. 最终AI智能体参数:', finalParams);

      console.log('=== 调试结束 ===');

      return finalParams;
    },

    // 检查是否为移动端
    checkMobile() {
      this.isMobile = window.innerWidth <= 768;
    },

    // 处理窗口大小变化
    handleResize() {
      this.checkMobile();
    },

    // 智能体动作处理方法 - 刷新页面数据
    refreshPage() {
      // 重新加载工单详情数据和按钮权限
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
        // 页面刷新完成后，发送工单信息变更消息给智能体
        this.$nextTick(() => {
          setTimeout(() => {
            this.sendWoMessageToAiAgent();
          }, 500); // 延迟500ms确保智能体已准备好接收消息
        });
      });
    },

    // 处理智能体返回的受理成功操作
    handleAiAgentAcceptSuccess(actionData) {
      try {
        console.log('🤖 处理智能体受理成功操作，使用返回的参数直接更新:', actionData);

        // 直接使用智能体返回的参数更新 common 对象
        if (actionData) {
          if (actionData.workItemId) {
            this.common.workItemId = actionData.workItemId;
            console.log('🤖 更新 workItemId:', actionData.workItemId);
          }
          if (actionData.processDefId) {
            this.common.processDefId = actionData.processDefId;
            console.log('🤖 更新 processDefId:', actionData.processDefId);
          }
          if (actionData.processInsId) {
            this.common.processInstId = actionData.processInsId;
            console.log('🤖 更新 processInstId:', actionData.processInsId);
          }
          if (actionData.woId) {
            this.common.woId = actionData.woId;
            console.log('🤖 更新 woId:', actionData.woId);
          }
          if (actionData.networkType) {
            this.common.networkType = actionData.networkType;
            console.log('🤖 更新 networkType:', actionData.networkType);
          }

          // 根据新生成的workItemId再次调用显示按钮的接口
          this.getShowButton().then(() => {
            // 再次获取工单详情信息（确保所有数据都是最新的）
            this.getWorkOrderInfo();

            // 跳转到详情页面
            this.JumpDetails_Common(this.common);

            // 受理成功后，发送工单信息变更消息给智能体
            this.$nextTick(() => {
              setTimeout(() => {
                this.sendWoMessageToAiAgent();
              }, 1000); // 延迟1秒确保工单数据已更新且智能体已准备好接收消息
            });
          });
        } else {
          console.warn('🤖 智能体未返回有效的参数数据，使用常规刷新方式');
          this.refreshPage();
        }
      } catch (error) {
        console.error('🤖 处理智能体受理成功操作失败:', error);
        this.refreshPage();
      }
    },



    openNewWorkOrderTab(sheetNo) {
      // 打开新的工单Tab页
      if (!sheetNo) {
        this.$message.error('工单编号不能为空');
        return;
      }

      // 构建新工单的路由
      const route = {
        name: 'backbone_commonWoDetail',
        params: {
          sheetId: sheetNo
        },
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          ...this.$route.query
        }
      };

      // 在新标签页中打开
      const routeData = this.$router.resolve(route);
      window.open(routeData.href, '_blank');
    },

    sendLocationToAgent() {
      // 发送当前位置信息给全局智能体
      try {
        const locationData = {
          action: 'locationResponse',
          data: {
            // 这里可以添加具体的位置信息
            currentPage: 'workOrderDetail',
            sheetId: this.headInfo.sheetId,
            timestamp: Date.now()
          }
        };

        // 使用全局智能体通信服务
        if (this.$globalAiAgent && this.$globalAiAgent.isAvailable()) {
          const success = this.$globalAiAgent.sendMessage(locationData);
          if (success) {
            console.log('🤖 通过全局智能体服务发送位置信息:', locationData);
            return;
          }
        }

        console.warn('全局智能体服务不可用，无法发送位置信息');
      } catch (error) {
        console.error('发送位置信息失败:', error);
      }
    },

    // 更新全局智能体数据
    async updateGlobalAiAgentData() {
      try {
        // 直接更新智能体数据，不调用checkAiAgentAvailability（避免覆盖网络类型检查）
        await this.updateGlobalAiAgentDataWithoutAvailabilityCheck({
          route: this.$route
        });

        console.log('🤖 全局智能体数据已更新（跳过可用性检查）');
      } catch (error) {
        console.error('Failed to update global AI agent data:', error);
      }
    },

    // 更新全局智能体数据（不检查可用性，避免覆盖网络类型检查）
    async updateGlobalAiAgentDataWithoutAvailabilityCheck({ route }) {
      try {
        // 更新当前路由
        this.$store.commit('aiAgent/SET_CURRENT_ROUTE', route);

        // 只有在智能体可用时才更新URL和配置
        if (this.isAiAgentAvailable) {
          // 使用配置文件中的智能体URL（支持环境自动切换）
          const aiAgentUrl = this.$store.getters['aiAgent/aiAgentUrl'] || require('@/config/aiAgent').getAiAgentUrl();

          // 更新智能体URL和配置
          this.$store.commit('aiAgent/SET_AI_AGENT_URL', aiAgentUrl);
          this.$store.commit('aiAgent/SET_AI_AGENT_CONFIG', require('@/config/aiAgent').AI_AGENT_CONFIG);

          console.log('🤖 全局智能体数据已更新（不检查可用性）:', {
            url: aiAgentUrl,
            environment: process.env.NODE_ENV,
            note: '跳过可用性检查，保持网络类型检查结果'
          });
        }
      } catch (error) {
        console.error('Failed to update AI agent data without availability check:', error);
        this.$store.commit('aiAgent/SET_AI_AGENT_ERROR', true);
      }
    },

    // 发送初始化数据给全局智能体
    sendInitDataToGlobalAgent() {
      try {
        // ========== 关键检查：只有当前激活的页面才能发送消息 ==========
        // const isCurrentPageActive = this.checkIfCurrentPageIsActive();
        // if (!isCurrentPageActive) {
        //   console.log('🤖 当前页面不是激活状态，跳过发送智能体数据', {
        //     pageInstanceId: this.pageInstanceId,
        //     woId: this.common.woId,
        //     isCurrentPageActive: this.isCurrentPageActive
        //   });
        //   return;
        // }

        // 获取智能体URL参数数据
        const initData = this.getAiAgentUrlParams();

        const responseMessage = {
          action_name: 'initComplete',
          action_data: initData
        };

        console.log('🤖 当前页面是激活状态，准备发送智能体数据:', {
          pageInstanceId: this.pageInstanceId,
          woId: this.common.woId,
          sheetNo: this.headInfo.sheetId,
          message: responseMessage
        });

        // 使用全局智能体通信服务
        if (this.$globalAiAgent && this.$globalAiAgent.isAvailable()) {
          const success = this.$globalAiAgent.sendMessage(responseMessage);
          if (success) {
            console.log('🤖 通过全局智能体服务发送初始化数据成功:', responseMessage);
            return;
          }
        }

        console.warn('全局智能体服务不可用，无法发送初始化数据');
      } catch (error) {
        console.error('发送初始化数据失败:', error);
      }
    },

    // 发送工单信息变更消息给智能体
    sendWoMessageToAiAgent() {
      try {
        // ========== 关键检查：只有当前激活的页面才能发送消息 ==========
        // const isCurrentPageActive = this.checkIfCurrentPageIsActive();
        // if (!isCurrentPageActive) {
        //   console.log('🤖 当前页面不是激活状态，跳过发送工单变更消息', {
        //     pageInstanceId: this.pageInstanceId,
        //     woId: this.common.woId,
        //     isCurrentPageActive: this.isCurrentPageActive
        //   });
        //   return;
        // }

        // 检查智能体是否可用且已展开
        const isNetworkType7 = parseInt(this.common.networkType) === 7;
        const isAiAgentShown = this.showAiAgent;

        if (!isNetworkType7 || !isAiAgentShown || !this.isAiAgentAvailable) {
          console.log('🤖 智能体不可用或未展开，跳过发送工单变更消息', {
            networkType: this.common.networkType,
            isNetworkType7,
            showAiAgent: isAiAgentShown,
            isAiAgentAvailable: this.isAiAgentAvailable
          });
          return;
        }

        // 构建工单变更消息
        const responseMessage = {
          action_name: 'sendWoMessage',
          action_data: {
            ai_wo_process_id: this.$route.query.processInstId, // url中获取 processInstId
            ai_wo_step_id: this.$route.query.workltemld, // url中获取workltemld
            ai_wo_status: this.headInfo.sheetStatus,
            // 包含TOKEN和其他参数，与initComplete保持一致
            ...this.getAiAgentUrlParams()
          }
        };

        console.log('🤖 准备发送工单变更消息给智能体:', {
          pageInstanceId: this.pageInstanceId,
          woId: this.common.woId,
          sheetNo: this.headInfo.sheetId,
          sheetStatus: this.headInfo.sheetStatus,
          message: responseMessage
        });

        // 使用全局智能体通信服务
        if (this.$globalAiAgent && this.$globalAiAgent.isAvailable()) {
          const success = this.$globalAiAgent.sendMessage(responseMessage);
          if (success) {
            console.log('🤖 通过全局智能体服务发送工单变更消息成功:', responseMessage);
            return;
          }
        }

        console.warn('全局智能体服务不可用，无法发送工单变更消息');
      } catch (error) {
        console.error('发送工单变更消息失败:', error);
      }
    },

    // ========== 新增：多工单消息过滤相关方法 ==========

    // 设置当前活跃工单信息
    setCurrentActiveWorkOrder() {
      const workOrderInfo = {
        woId: this.common.woId,
        sheetNo: this.headInfo.sheetId || this.common.sheetNo,
        pageInstanceId: this.pageInstanceId
      };

      this.$store.dispatch('aiAgent/setCurrentActiveWorkOrder', workOrderInfo);
    },

    // 检查智能体消息是否针对当前工单
    checkAiAgentMessageTarget(messageData) {
      // 从消息中提取工单信息
      const messageWorkOrder = {
        woId: messageData.action_data?.ai_wo_id || messageData.woId,
        sheetNo: messageData.action_data?.ai_sheet_no || messageData.sheetNo
      };

      // ========== 关键修复：检查当前页面是否为活跃tab ==========
      const isCurrentPageActive = this.checkIfCurrentPageIsActive();

      console.log('🤖 页面活跃状态检查:', {
        isCurrentPageActive,
        currentWoId: this.common.woId,
        currentSheetNo: this.headInfo.sheetId,
        messageWorkOrder: messageWorkOrder,
        pageInstanceId: this.pageInstanceId
      });

      // 如果当前页面不是活跃tab，直接忽略所有消息（除了initComplete）
      if (!isCurrentPageActive) {
        console.log('🤖 当前页面不是活跃tab，忽略消息处理');
        return false;
      }

      // 如果消息没有指定目标工单，且当前页面是活跃的，则处理消息（向后兼容）
      if (!messageWorkOrder.woId && !messageWorkOrder.sheetNo) {
        console.log('🤖 消息未指定目标工单，当前页面是活跃tab，处理消息');
        return true;
      }

      // 检查工单ID或工单编号是否匹配
      const woIdMatch = this.common.woId && messageWorkOrder.woId && this.common.woId === messageWorkOrder.woId;
      const sheetNoMatch = (this.headInfo.sheetId || this.common.sheetNo) && messageWorkOrder.sheetNo &&
                          (this.headInfo.sheetId === messageWorkOrder.sheetNo || this.common.sheetNo === messageWorkOrder.sheetNo);

      const isMatch = woIdMatch || sheetNoMatch;

      console.log('🤖 消息目标工单检查:', {
        currentWoId: this.common.woId,
        currentSheetNo: this.headInfo.sheetId || this.common.sheetNo,
        messageWorkOrder: messageWorkOrder,
        isMatch,
        woIdMatch,
        sheetNoMatch,
        isCurrentPageActive
      });

      return isMatch;
    },

    // 检查当前页面是否为活跃的tab页面
    checkIfCurrentPageIsActive() {
      try {
        // 主要依赖我们在生命周期中设置的状态
        const isActiveByLifecycle = this.isCurrentPageActive;

        // 辅助检查：当前路由是否匹配
        const currentRoute = this.$route;
        let routeMatches = false;
        if (currentRoute && currentRoute.query) {
          const routeWoId = currentRoute.query.woId;
          const routeWorkItemId = currentRoute.query.workItemId;
          routeMatches = (routeWoId === this.common.woId) &&
                        (routeWorkItemId === this.common.workItemId);
        }

        // ========== 新增：检查当前页面是否为Vuex中的活跃工单 ==========
        const currentActiveWorkOrder = this.$store.getters['aiAgent/currentActiveWorkOrder'];
        const isActiveInStore = currentActiveWorkOrder &&
                               (currentActiveWorkOrder.woId === this.common.woId) &&
                               (currentActiveWorkOrder.pageInstanceId === this.pageInstanceId);

        // 综合判断：生命周期状态 + 路由匹配 + Vuex状态匹配
        const isActive = isActiveByLifecycle && routeMatches && isActiveInStore;

        console.log('🤖 页面活跃状态检查:', {
          isActiveByLifecycle,
          routeMatches,
          isActiveInStore,
          isActive,
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          pageInstanceId: this.pageInstanceId,
          currentActiveWorkOrder
        });

        return isActive;
      } catch (error) {
        console.error('检查页面活跃状态失败:', error);
        // 出错时返回生命周期状态
        return this.isCurrentPageActive;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    position: relative;
    font-size: 20px;
    line-height: 28px;
    font-weight: 700;
    word-break: break-all;
  }

  .head-handle-wrap {
    text-align: right;

    .more-dropdown {
      padding: 0;

      @include themify() {
        border: none;
        border-left: 1px solid themed("$--button-default-border-color");
      }
    }

    .btnleft__group {
      margin-left: 5px;
      margin-bottom: 0px;
      margin-top: 10px;
    }
  }

  .divider {
    margin: 12px 0 16px;
  }

  .head-info2 {
    p {
      margin: 0 auto 5px;
    }
  }

  .head-sheetId {
    font-size: 20px;
    line-height: 28px;
  }

  .head-up-down {
    text-align: center;

    &>div:first-child {
      line-height: 20px;

      @include themify() {
        color: themed("$--color-text-regular");
      }
    }

    &>div:last-child {
      font-weight: 400;
      font-size: 18px;
      line-height: 28px;
      white-space: nowrap;

      &.text-primary {
        color: #c43c43;
      }
    }
  }

  .card-title {
    font-weight: 400;
    font-size: 15px;
    box-shadow: 0 0 5px #aaa;
    padding-left: 10px;
  }
}

::v-deep .alarmDetailTable{
  .el-table__empty-block{
    width: 100% !important;
  }
}

/* 全局智能体激活时的页面样式调整 */
.common-wo-detail-wrapper {
  width: 100%;
  overflow: hidden;

  // 页面内容正常显示，宽度控制由全局Layout层处理
  .draft-eidt-page {
    width: 100%;
    transition: all 0.3s ease;
  }

  // 移动端处理
  &.mobile {
    .draft-eidt-page {
      overflow-x: auto;
    }
  }
}
</style>
