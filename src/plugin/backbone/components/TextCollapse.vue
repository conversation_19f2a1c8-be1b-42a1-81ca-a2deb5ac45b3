<template>
  <div ref="textOverflow" class="text-overflow" :style="boxStyle">
    <span class="real-text" ref="overEllipsis">{{ realText }}</span>
    <span
      class="slot-box"
      :class="{ expanded: localExpanded }"
      ref="slotRef"
      v-if="showSlotNode"
      @click="toggle"
    >
      <slot :click-toggle="toggle" :expanded="localExpanded">
        <i
          class="expand-dom cursor-pointer"
          :class="'el-icon-' + (localExpanded ? 'upload2' : 'download')"
        ></i>
      </slot>
    </span>
  </div>
</template>

<script>
import {
  addResizeListener,
  removeResizeListener,
} from "element-ui/src/utils/resize-event.js";
import debounce from "lodash/debounce";
export default {
  name: "TextCollapse",
  props: {
    text: {
      type: String,
      default: "",
    },
    maxLines: {
      type: Number,
      default: 1,
    },
    width: {
      type: Number,
      default: 0,
    },
    expanded: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      linefeedText: "",
      offset: this.text.length,
      localExpanded: this.expanded,
      showSlotNode: false,
    };
  },
  computed: {
    boxStyle() {
      if (this.width) {
        return {
          width: this.width + "px",
        };
      } else {
        return {};
      }
    },
    realText() {
      // 是否被截取
      const text = this.linefeedText;
      const isCutOut = this.offset !== text.length;
      let realText = text;
      if (isCutOut && !this.localExpanded) {
        realText = text.slice(0, this.offset) + "...";
      }
      return realText;
    },
  },
  watch: {
    text() {
      this.handleChange();
    },
  },
  created() {
    this.handerChangeFunc = debounce(this.handleChange);
  },
  mounted() {
    this.handleChange();
    addResizeListener(this.$el, this.handerChangeFunc);
  },
  beforeDestroy() {
    removeResizeListener(this.$el);
  },
  methods: {
    getLinefeedText() {
      const reg = /<br\/>|\r\n|\r|\n/gi;
      // if (reg.test(this.text)) {
      //   this.linefeedText = this.text
      //     .split(reg)
      //     .filter(item => item != "")
      //     .map(item => item.trim());
      // }
      this.linefeedText = this.text.replace(reg, "\n");
    },
    handleChange() {
      this.getLinefeedText();
      if (!this.hasLinefeed) {
        this.offset = this.linefeedText.length;
        this.$nextTick(() => {
          const { len } = this.getLines();
          this.calculateOffset(0, this.linefeedText.length);
          if (len > this.maxLines) {
            this.showSlotNode = true;
          } else {
            this.showSlotNode = false;
          }
        });
      }
    },
    calculateOffset(from, to) {
      this.$nextTick(() => {
        if (Math.abs(from - to) <= 1) return;
        if (this.isOverflow()) {
          to = this.offset;
        } else {
          from = this.offset;
        }
        this.offset = Math.floor((from + to) / 2);
        this.calculateOffset(from, to);
      });
    },
    isOverflow() {
      const { len, lastWidth } = this.getLines();

      if (len < this.maxLines) {
        return false;
      }
      if (this.maxLines) {
        // 超出部分 行数 > 最大行数 或则  已经是最大行数但最后一行宽度 + 后面内容超出正常宽度

        const slotBoxWidth = this.$refs.slotRef?.clientWidth ?? 0;
        const textBoxWidth = this.$refs.textOverflow.clientWidth;
        const lastLineOver = !!(
          len === this.maxLines && lastWidth + slotBoxWidth > textBoxWidth
        );
        if (len > this.maxLines || lastLineOver) {
          return true;
        }
      }
      return false;
    },
    getLines() {
      const clientRects = this.$refs.overEllipsis.getClientRects();
      const len = Object.keys(
        Array.prototype.slice
          .call(clientRects)
          .reduce((prev, { top, bottom }) => {
            const key = `${top}/${bottom}`;
            if (!prev[key]) {
              prev[key] = true;
            }
            return prev;
          }, {})
      ).length;
      return {
        len: len,
        lastWidth: clientRects[len - 1]?.width || 0,
      };
    },
    toggle() {
      this.localExpanded = !this.localExpanded;
      this.$emit("update:expanded", this.localExpanded);
    },
  },
};
</script>

<style lang="scss">
.el-descriptions-item__content:has(.text-overflow) {
  flex-grow: 1;
  overflow: hidden;
}
.text-overflow {
  position: relative;
  width: 100%;
  white-space: pre-wrap;
  .expand-dom {
    color: #b50b14;
  }
  .slot-box {
    display: inline-block;
    padding-left: 5px;
    // &:not(.expanded) {
    //   position: absolute;
    //   right: 0;
    // }
  }
  .real-text {
    white-space: pre-wrap;
  }
}

</style>
