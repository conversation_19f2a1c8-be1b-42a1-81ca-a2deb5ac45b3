<template>
  <head-fixed-layout class="draft-eidt-page" v-loading="pageLoading">
    <template #header>
      <!-- 测试按钮：动态切换建单方 -->
      <div style="position: absolute; top: 10px; left: 10px; z-index: 9000;">
        <el-button
          size="mini"
          :type="testFaultHandler === 'unicom' ? 'primary' : 'default'"
          @click="switchFaultHandler('unicom')"
        >
          联通模式
        </el-button>
        <el-button
          size="mini"
          :type="testFaultHandler === 'telecom' ? 'primary' : 'default'"
          @click="switchFaultHandler('telecom')"
        >
          电信模式
        </el-button>
        <el-button
          size="mini"
          :type="testFaultHandler === 'mobile' ? 'primary' : 'default'"
          @click="()=>dialogQualitativeVisible = true"
        >
          定性
        </el-button>
        <el-button
          size="mini"
          :type="testFaultHandler === 'mobile' ? 'primary' : 'default'"
          @click="()=>dialogQualitativeAuditVisible = true"
        >
          定性审核
        </el-button>
        <el-button
          size="mini"
          :type="testFaultHandler === 'mobile' ? 'primary' : 'default'"
          @click="()=>dialogTelecomDispatchOrderBack = true"
        >
          回退
        </el-button>
        <el-button
          size="mini"
          :type="testFaultHandler === 'mobile' ? 'primary' : 'default'"
          @click="()=>dialogEliminateFaultsVisible = true"
        >
          消障确认
        </el-button>
      </div>
      <!-- 测试按钮：动态切换派单方式 -->
      <div style="position: absolute; top: 50px; left: 10px; z-index: 9000;">
        <el-button
          size="mini"
          :type="testDispatchMode === 'unicom_to_unicom' ? 'primary' : 'default'"
          @click="switchDispatchMode('unicom_to_unicom')"
        >
          联通派联通
        </el-button>
        <el-button
          size="mini"
          :type="testDispatchMode === 'unicom_to_telecom' ? 'primary' : 'default'"
          @click="switchDispatchMode('unicom_to_telecom')"
        >
          联通派电信
        </el-button>
        <el-button
          size="mini"
          :type="testDispatchMode === 'telecom_to_unicom' ? 'primary' : 'default'"
          @click="switchDispatchMode('telecom_to_unicom')"
        >
          电信派联通
        </el-button>
      </div>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="14" class="head-title">
          <text-collapse
            :text="`【无线网故障工单】${headInfo.title}`"
            :max-lines="2"
          ></text-collapse>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="10" class="head-handle-wrap">
          <el-button-group>
            <el-button type="button" @click="onHeadHandleClick('jcxx')"
              >基础信息</el-button
            >
            <el-button type="button" @click="onHeadHandleClick('gjxq')"
              >告警详情</el-button
            >
            <!-- v-if="ppResult || analysisStatus == 1" -->
            <el-button type="button" @click="onHeadHandleClick('glzd')"
              >关联诊断</el-button
            >
            <el-button
              type="button"
              :style="getStatusStyle()"
              @click="onHeadHandleClick('fkdxq')"
              >反馈单详情</el-button
            >
            <el-dropdown
              @command="onHeadHandleClick"
              class="el-button more-dropdown"
              size="medium"
            >
              <el-button type="button">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <template v-for="(item, i) in headMoreDrops">
                  <template>
                    <el-dropdown-item :command="item.command" :key="i">
                      {{ item.title }}
                    </el-dropdown-item>
                  </template>
                </template>
              </el-dropdown-menu>
            </el-dropdown>
          </el-button-group>
          <br />
          <!-- 按钮动态展示 -->
          <template v-for="(item, i) in processButtonArr">
            <el-popover
              v-if="item.value == '现场打点'"
              :key="i"
              placement="top-start"
              title="提示"
              width="200"
              class="btnleft__group"
              trigger="hover"
              content="请到APP上操作"
            >
              <el-button size="mini" slot="reference">{{
                item.value
              }}</el-button>
            </el-popover>
            <template v-else>
              <el-button
                size="mini"
                type="primary"
                class="btnleft__group"
                :key="i"
                @click="buttonClick(item.value)"
                v-loading.fullscreen.lock="processFullscreenLoading"
                >{{ item.value }}</el-button
              >
            </template>
          </template>
        </el-col>
      </el-row>
      <el-divider
        direction="horizontal"
        content-position="left"
        class="divider"
      ></el-divider>
      <div class="head-info2">
        <el-row :gutter="20" tag="p">
          <el-col
            :xs="24"
            :sm="24"
            :md="13"
            :lg="13"
            tag="p"
            class="head-sheetId"
          >
            工单编号: {{ headInfo.sheetId }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="11" :lg="11" tag="p">
            <el-row :gutter="20" type="flex">
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="7"
                tag="p"
                class="head-up-down"
              >
                <div>当前处理人</div>
                <div
                  class="text-truncate"
                  @click="currentProcessor"
                  style="
                    cursor: pointer;
                    text-decoration: underline;
                    color: #b50b14;
                    user-select: unset;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-size: 18px;
                    line-height: 28px;
                  "
                  :title="headInfo.currentHandler"
                >
                  {{ headInfo.currentHandler }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="4"
                tag="p"
                class="head-up-down"
              >
                <div>工单状态</div>
                <div>
                  {{ headInfo.sheetStatus }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="7"
                tag="p"
                class="head-up-down"
                v-if="headInfo.sheetStatus == '待受理'"
              >
                <div>剩余受理时间</div>
                <div class="text-primary" :title="setAcceptTimeLimit">
                  {{ setAcceptTimeLimit }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="7"
                tag="p"
                class="head-up-down"
                v-if="
                  [
                    '处理中',
                    '待定性',
                    '待确认',
                    '待定性审核',
                    '挂起',
                    '上传故障报告',
                    '故障报告审核',
                  ].includes(headInfo.sheetStatus)
                "
              >
                <div>剩余处理时间</div>
                <div class="text-primary" :title="setHandleTimeLimit">
                  {{ setHandleTimeLimit }}
                </div>
              </el-col>

              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="6"
                tag="p"
                class="head-up-down"
              >
                <div>工单总耗时</div>
                <div class="text-primary" :title="headInfo.totalWorkingTime">
                  {{ headInfo.totalWorkingTime }}
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单方: {{ faultHandlerText }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单人: {{ headInfo.buildSingleMan }}
          </el-col>
          <!-- <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p" v-if="shouldShowBuildDept">
            建单部门: {{ headInfo.buildSingleDept }}
          </el-col> -->
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单时间: {{ headInfo.buildSingleTime }}
          </el-col>
        </el-row>
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            工单来源: {{ headInfo.sourceInfo }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            发生时间: {{ headInfo.occurrenceTime }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            紧急程度: {{ headInfo.emergencyDegree }}
          </el-col>
        </el-row>
      </div>
    </template>

    <base-info
      ref="jcxx"
      v-if="basicWorkOrderData"
      :basicWorkOrderData="basicWorkOrderData"
      :woId="common.woId"
      :workItemId="common.workItemId"
    />
    <!-- 告警详情 -->
     v-if="showAlarm"
    <alarm-detail ref="gjxq" :woId="common.woId"  />
    <!-- 关联诊断 -->
    <!-- v-if="common.woId && (ppResult || analysisStatus == 1)" -->
    <relation-diagnosis
      ref="glzd"
      v-if="basicWorkOrderData"
      :basicWorkOrderData="basicWorkOrderData"
      :woId="common.woId"
    />
    <!-- 反馈单详情 -->
     v-if="showFkdxq && common.woId"
    <feedback-sheet
      ref="fkdxq"

      :isShowAudit="isShowQualitativeReviewButton"
      :isShowQualitative="isShowQualitative"
      :common="common"
      :woId="common.woId"
      :basicWorkOrderData="basicWorkOrderData"
      :qualitativeType="qualitativeType"
      @qualitativeReviewSubmit="qualitativeReviewSubmit"
      @auditResultCallBack="auditResultCallBack"
    />
    <!-- 处理详情 -->
    <deal-details
      ref="clxq"
      v-if="showClxq && this.common.woId != ''"
      :woId="common.woId"
      :basicWorkOrderData="basicWorkOrderData"
    />
    <process-log
      ref="lcrz"
      v-if="showLcrz && this.common.woId != ''"
      :woId="common.woId"
    />
    <flow-chart
      ref="lct"
      v-if="showLct && this.common.woId != ''"
      :common="common"
    />

    <!-- 悬浮 外链接按钮 -->
    <div class="outer-link">
      <span class="num">{{ messageNum }}</span>
      <span class="icon" @click="handleOuterLink"></span>
      <span class="name" @click="handleOuterLink">chatops</span>
    </div>

    <!-- 弹出框信息 -->
    <el-dialog
      title="消息"
      :visible.sync="chatopsVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleCloseMessage"
      width="1000px"
    >
      <iframe
        :src="rsaEncryptUrl"
        frameborder="0"
        width="100%"
        height="500px"
        scrolling="auto"
        style="border: 1px solid #eee; margin-top: -20px"
      ></iframe>
    </el-dialog>

    <el-dialog
      title="阶段反馈"
      :visible.sync="dialogStageFeedbackVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogStageFeedbackClose"
      width="450px"
    >
      <stage-feedback
        :common="common"
        actionName="阶段反馈"
        @stageBackDialogClose="stageBackDialogCommitClose"
      ></stage-feedback>
    </el-dialog>

    <el-dialog
      title="申请挂起"
      :visible.sync="dialogHangUpVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogHangUpClose"
      width="540px"
    >
      <hang-up
        :common="common"
        :opType="1"
        actionName="申请挂起"
        @closeDialogHangUp="dialogHangUpSubmitClose"
      ></hang-up>
    </el-dialog>
    <el-dialog
      title="解挂"
      :visible.sync="dialogSolutionToHangVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogSolutionToHangClose"
      width="540px"
    >
      <hang-up
        :common="common"
        :opType="2"
        actionName="解挂"
        @closeDialogHangUp="dialogSolutionToHangSubmitClose"
      ></hang-up>
    </el-dialog>
    <el-dialog
      title="挂起审核"
      :visible.sync="dialogPendingReviewVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogPendingReviewClose"
      width="450px"
    >
      <audit
        :common="common"
        :opContent="opContent"
        actionName="挂起审核"
        @closeDialogPendingReview="dialogPendingReviewSubmitClose"
      ></audit>
    </el-dialog>
    <el-dialog
      title="转派"
      :visible.sync="dialogTurnToSendVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogTurnToSendClose"
      width="480px"
    >
      <turn-to-send
        @closeDialogTurnToSend="dialogTurnToSendSubmitClose"
        :common="common"
        :type="type"
        actionName="转派"
      ></turn-to-send>
    </el-dialog>

    <el-dialog
      title="回退"
      :visible.sync="dialogTelecomDispatchOrderBack"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleTelecomDispatchOrderBackClose"
      :fullscreen="false"
      width="480px"
    >
      <el-form :model="dialogTelecomDispatchOrderBackRuleForm" :rules="dialogTelecomDispatchOrderBackRules" ref="dialogTelecomDispatchOrderBackRuleForm" label-width="100px">
         <el-form-item label="回退说明" prop="name">
          <el-input 
            type="textarea"
            :rows="3" 
            v-model="dialogTelecomDispatchOrderBackRuleForm.name" 
            placeholder="请添写回退说明"></el-input>
         </el-form-item>
      </el-form>
      <div slot="footer" style="padding: 10px 20px;margin-top: -20px!important; text-align: right">
        <!-- v-loading.fullscreen.lock="transferSubmitLoading" -->
        <el-button
          type="primary"
          @click="handleTelecomDispatchOrderBackSubmit"
          >提 交</el-button
        >
        <el-button @click="()=>{
          dialogTelecomDispatchOrderBack = false;
        }">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="定性审核"
      :visible.sync="dialogQualitativeAuditVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogQualitativeAuditClose"
      width="83%"
      top="5vh"
    >
      <qualitative-audit
        @closeDialogQualitativeAudit="dialogQualitativeAuditSumbitClose"
        :common="common"
        :type="type"
        actionName="定性审核"
      >
      </qualitative-audit>
    </el-dialog>
    <el-dialog
      title="派单评价"
      :visible.sync="dialogOrderEvaluateVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogOrderEvaluateClose"
      width="450px"
    >
      <order-evaluate
        @closeDialogOrderEvaluate="dialogOrderEvaluateSumbitClose"
        :common="common"
        :type="type"
        actionName="派单评价"
      >
      </order-evaluate>
    </el-dialog>
    <el-dialog
      title="消障确认"
      :visible.sync="dialogEliminateFaultsVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogEliminateFaultsClose"
      width="1024px"
      custom-class="detail-dialog"
    >
      <eliminate-faults
        @closeDialogEliminateFaults="dialogEliminateFaultsSubmitClose"
        :common="common"
        :type="type"
        :alarmsData="alarmsData"
        actionName="消障确认"
        :random="random"
      >
      </eliminate-faults>
    </el-dialog>
    <el-dialog
      title="定性"
      :visible.sync="dialogQualitativeVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :common="common"
      @close="qualitativeClose"
      width="83%"
      top="5vh"
    >
      <qualitative
        ref="qualitative"
        :common="common"
        :workItemId="common.workItemId"
        :alarmDeviceVendor="testAlarmDeviceVendor"
        :alarmDeviceType="testAlarmDeviceType"
        :alarmDeviceName="testAlarmDeviceName"
        @qualitativeSubmit="qualitativeSubmit"
      >
      </qualitative>
    </el-dialog>
    <el-dialog
      title="PC现场打点"
      :visible.sync="dialogLocationVisible"
      width="480px"
    >
      <el-form
        ref="locationForm"
        :model="locationForm"
        :rules="locationRules"
        label-width="80px"
      >
        <el-form-item label="经度">
          <el-input v-model="locationForm.longitude"></el-input>
        </el-form-item>
        <el-form-item label="纬度">
          <el-input v-model="locationForm.latitude"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogLocationVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitLocationForm('locationForm')"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="处理人"
      :visible.sync="dialogCurrentProcessorVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="currentProcessorClose"
      :fullscreen="false"
      width="60%"
      top="5vh"
    >
      <current-processor
        :common="common"
        :persons="basicWorkOrderData && basicWorkOrderData.operatePersonId"
      >
      </current-processor>
    </el-dialog>
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "../../workOrder/components/HeadFixedLayout.vue";
import BaseInfo from "./components/BaseInfo.vue";
import AlarmDetail from "./components/AlarmDetail.vue";
import RelationDiagnosis from "./components/RelationDiagnosis.vue";
import FeedbackSheet from "./components/FeedbackSheet.vue";
import DealDetails from "./components/DealDetails.vue";
import ProcessLog from "./components/ProcessLog.vue";
import FlowChart from "./components/FlowChart.vue";
import StageFeedback from "./components/StageFeedback.vue";
import HangUp from "./components/HangUp.vue";
import Audit from "./components/Audit.vue";
// import Abend from "./components/Abend.vue";
// import AbendAudit from "./components/AbendAudit";
import TurnToSend from "./components/TurnToSend.vue";
import QualitativeAudit from "./components/QualitativeAudit.vue";
import OrderEvaluate from "./components/OrderEvaluate.vue";
import EliminateFaults from "./components/EliminateFaults.vue";
import Qualitative from "./components/Qualitative.vue";
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";
import CurrentProcessor from "../../workOrder/components/CurrentProcessor.vue";
import {
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiAccept,
  apiHaveRead,
  getCurrentTime,
  apiProcessComplete,
  apiLocation,
  apiGetRsaEncryptWifi,
  apiMessageRsaEncrypt,
  apiGetMeassgeNum,
} from "./api/CommonApi";
import {
  FIELD_NAMES,
  FAULT_HANDLER_OPTIONS
} from "../config/additionalFields";
export default {
  name: "WorkOrderWaitDetail",
  components: {
    HeadFixedLayout,
    BaseInfo,
    AlarmDetail,
    RelationDiagnosis,
    FeedbackSheet,
    DealDetails,
    ProcessLog,
    FlowChart,
    StageFeedback,
    HangUp,
    Audit,
    TurnToSend,
    QualitativeAudit,
    OrderEvaluate,
    EliminateFaults,
    Qualitative,
    TextCollapse,
    CurrentProcessor,
  },
  data() {
    return {
      headInfo: {
        title: "",
        sheetId: "",
        currentHandler: "",
        currentLink: "",
        sheetStatus: null,
        acceptTimeLimit: "",
        processingTimeLimit: "",
        processTimeLimit: "",
        totalWorkingTime: "",
        buildSingleMan: "",
        buildSingleDept: "",
        buildSingleTime: "",
        sourceInfo: "",
        occurrenceTime: "",
        emergencyDegree: "",
        faultHandler: "", // 建单方/故障处理方
      },
      // 测试用：动态切换建单方
      testFaultHandler: 'unicom',
      // 测试用：动态切换派单方式
      testDispatchMode: 'unicom_to_unicom',
      // 测试用：告警设备信息
      testAlarmDeviceVendor: 'huawei', // 告警设备厂家
      testAlarmDeviceType: 'eNodeB', // 告警设备类型
      testAlarmDeviceName: '北京朝阳CBD基站设备', // 告警设备名称
      headMoreDrops: [
        { command: "clxq", title: "处理详情" },
        { command: "lcrz", title: "流程日志" },
        { command: "lct", title: "流程图" },
      ],
      processButtonArr: [],
      basicWorkOrderData: null,
      dealData: [],
      alarmsData: [],
      messageNum: "", // chatops 未读条数
      isTitleCollapse: false,
      common: {
        woId: "", //工单ID
        workItemId: "", //工作项ID
        processInstId: null, //流程实例ID
        processDefId: null, //流程定义ID
        failureTime: "", //故障发生时间
        failureInformTime: "", //故障通知时间
        actionName: "", //操作按钮名称
        processNode: "", //环节名称
        sheetNo: "", //工单编号
        isSender: null, //是否为建单人
        agentManId: "", //主送人Id
        agentMan: "", //主送人
        copyManId: "", //抄送人Id
        copyMan: "", //抄送人
        sheetStatus: "", //工单状态
        agentDeptCode: "",
        agentDeptName: "",
        copyDeptCode: "",
        copyDeptName: "",
        professionalType: null,
        networkType: null,
        hangOver: null, //挂起历时
        faultCauseDescription: null, //故障原因描述
        networkTypeName: null, //网络类型中文名
        professionalTypeName: null, //专业类型中文名
        faultLevel: null,
        acceptMan: null,
        acceptDeptName: null,
        processTimeLimit: null, //预估处理时限
        alarmClearTime: null, //告警清除时间
        auditResult: null, //审核结果
        isFeedBack: null,
      },
      timing: {
        hangTime: "", //挂起时间
      },
      showFkdxq: false,
      showClxq: false,
      showLcrz: false,
      showLct: false,
      showRelation: true,
      showAlarm: false,
      processFullscreenLoading: false,
      isShowQualitativeReviewButton: false,
      isShowQualitative: false,
      //阶段反馈
      dialogStageFeedbackVisible: false,

      //撤单
      dialogRevokeVisible: false,
      revokeSubmitLoading: false,
      revokeForm: {
        auditOpinion: null,
      },
      //当前处理人
      dialogCurrentProcessorVisible: false,
      // 回退
      dialogTelecomDispatchOrderBack: false,
      dialogTelecomDispatchOrderBackRuleForm: {
        name: ''
      },
      dialogTelecomDispatchOrderBackRules: {
        name: [
          { required: true, message: '请添写回退说明', trigger: 'blur' }
        ]
      },
      //挂起
      dialogHangUpVisible: false,
      //解挂
      dialogSolutionToHangVisible: false,
      //挂起审核
      auditTitle: null,
      opContent: null,
      dialogPendingReviewVisible: false,
      //解挂审核
      dialogSolutionToHangAuditVisible: false,
      //异常终止
      dialogAbendVisible: false,
      //异常终止审核
      dialogAbendAuditVisible: false,
      //转派
      dialogTurnToSendVisible: false,
      //类型
      type: "single",
      //定性审核
      dialogQualitativeAuditVisible: false,
      // 派单评价
      dialogOrderEvaluateVisible: false,
      //重新转派
      dialogAgainTurnToSendVisible: false,
      // 消障确认
      dialogEliminateFaultsVisible: false,
      // 故障定性
      dialogQualitativeVisible: false,
      // PC现场打点
      dialogLocationVisible: false,
      //定性类型
      qualitativeType: "",
      route: null,
      //从哪个页面进入的，不同页面进入，显示的按钮会不同，如：待阅工单只展示已阅、传阅按钮
      fromPage: null,
      pageLoading: true,
      networkType: null,
      turnFlag: null, // 转派了，就不显示转派按钮。'Y'不显示
      regularFdbkFlag: null, // 操作了，阶段反馈，才能处理完成，'Y'显示
      locationFlag: null, //现场打点完了，才显示，'Y'显示
      rsaEncrypt: "", // chatops 未读加密
      rsaEncryptUrl: "", // chatops iframe 路径
      chatopsVisible: false, // chatops 弹出框
      ppResult: null,
      analysisStatus: 0,
      locationForm: {}, // pc现场定位form
      locationRules: {
        longitude: [{ require: true, message: "现场打点经度必填" }],
        latitude: [{ require: true, message: "现场打点纬度必填" }],
      },
      random: 0,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
    setAcceptTimeLimit() {
      let time = this.headInfo.acceptTimeLimit;
      return this.setTime(time);
    },
    setHandleTimeLimit() {
      let time = this.headInfo.processTimeLimit;
      return this.setTime(time);
    },
    // 获取建单方显示文本
    faultHandlerText() {
      const option = FAULT_HANDLER_OPTIONS.find(opt => opt.value === this.headInfo.faultHandler);
      return option ? option.label : '';
    },
    // 是否显示建单部门（联通时隐藏）
    shouldShowBuildDept() {
      return this.headInfo.faultHandler !== 'unicom';
    },
  },
  created() {
    this.route = this.$route;
  },
  mounted() {
    this.common.workItemId = this.$route.query.workItemId;
    this.common.processInstId = this.$route.query.processInstId;
    this.common.woId = this.$route.query.woId;
    this.common.processDefId = this.$route.query.processDefId;
    this.common.processNode = this.$route.query.processNode;
    this.networkType = this.$route.query.networkType;
    this.professionalType = this.$route.query.professionalType;

    this.fromPage = this.$route.query.fromPage;
    this.fromPage = this.$route.query.fromPage;
    if (this.fromPage == "已阅工单") {
      this.getWorkOrderInfo();
    } else if (this.fromPage == "待阅工单") {
      this.processButtonArr = [{ key: "links", value: "已阅" }];
      this.getWorkOrderInfo();
    } else {
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
    }
  },
  methods: {
    setTime(time) {
      if (time == "-") {
        return "-";
      } else if (time <= 0) {
        const absTime = Math.abs(time);
        let format = "已超时";
        if (absTime < 60) {
          format += `${absTime}分`;
        } else {
          const minFormat = absTime % 60 == 0 ? "" : `${absTime % 60}分`;
          format += `${Math.floor(absTime / 60)}小时${minFormat}`;
        }
        return format;
      } else if (time > 0) {
        if (time < 60) {
          return `${time}分`;
        } else {
          const minFormat = time % 60 == 0 ? "" : `${time % 60}分`;
          return `${Math.floor(time / 60)}小时${minFormat}`;
        }
      }
    },
    getShowButton() {
      return new Promise(resolve => {
        let param = {
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          processInstId: this.common.processInstId,
          fromPage: this.fromPage,
        };
        apiGetShowButton(param)
          .then(res => {
            if (res.status == "0") {
              let self = this;
              self.processButtonArr = res?.data?.user ?? [];
              let qualitativeReviewButton = res?.data?.admin ?? [];
              if (
                qualitativeReviewButton.length > 0 &&
                qualitativeReviewButton[0].value == "定性"
              ) {
                this.isShowQualitative = true;
                this.qualitativeType = "定性";
              } else {
                this.isShowQualitative = false;
              }
              if (
                qualitativeReviewButton.length > 0 &&
                qualitativeReviewButton[0].value == "定性审核"
              ) {
                this.isShowQualitativeReviewButton = true;
                this.qualitativeType = "定性审核";
              } else {
                this.isShowQualitativeReviewButton = false;
              }
            }
            resolve("success");
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    getWorkOrderInfo() {
      this.pageLoading = true;
      let param = {
        woId: this.common.woId,
        processInstId: this.common.processInstId,
        workItemId: this.common.workItemId,
      };

      apiGetWorkOrderInfo(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            self.basicWorkOrderData = res?.data?.rows?.[0] ?? {};
            self.headInfo.title = self.basicWorkOrderData.sheetTitle;
            if (
              Object.prototype.hasOwnProperty.call(
                sessionStorage,
                self.basicWorkOrderData.woId
              )
            ) {
              this.common.actionName = sessionStorage.getItem(
                self.basicWorkOrderData.woId
              );
            }

            if (
              this.fromPage != "已阅工单" &&
              this.fromPage != "待阅工单" &&
              this.fromPage != "工单查询"
            ) {
              this.getShowButton();
            }

            self.headInfo.sheetId = self.basicWorkOrderData.sheetNo;
            self.headInfo.currentHandler =
              self.basicWorkOrderData.operatePerson; // 当前处理人
            self.headInfo.acceptTimeLimit =
              self.basicWorkOrderData.remainAcceptTime;
            self.headInfo.processTimeLimit =
              self.basicWorkOrderData.remainHandleTime;
            self.headInfo.sheetStatus = self.basicWorkOrderData.sheetStatus;
            self.headInfo.buildSingleMan = self.basicWorkOrderData.senderName;
            self.headInfo.buildSingleDept =
              self.basicWorkOrderData.senderDeptName;
            self.headInfo.buildSingleTime =
              self.basicWorkOrderData.sheetCreateTime;
            // 获取建单方/故障处理方字段
            self.headInfo.faultHandler = self.basicWorkOrderData[FIELD_NAMES.faultHandler] || 'unicom';
            // 初始化测试建单方
            self.testFaultHandler = self.headInfo.faultHandler;
            this.getWorkOrderSpentTime(self.basicWorkOrderData); //工单总耗时
            self.headInfo.sourceInfo = self.basicWorkOrderData.createType;
            self.headInfo.occurrenceTime =
              self.basicWorkOrderData.alarmCreateTime;
            self.headInfo.emergencyDegree =
              self.basicWorkOrderData.emergencyLevel;
            self.common.failureTime = self.basicWorkOrderData.alarmCreateTime;
            self.common.alarmClearTime = self.basicWorkOrderData.alarmClearTime;
            self.common.failureInformTime =
              self.basicWorkOrderData.sheetCreateTime;
            self.common.processNode = self.basicWorkOrderData.processNode;
            self.common.sheetNo = self.basicWorkOrderData.sheetNo;
            self.common.isSender =
              self.basicWorkOrderData.senderName == self.userInfo.realName
                ? 1
                : 0;
            self.common.agentManId = self.basicWorkOrderData.agentManId;
            self.common.agentMan = self.basicWorkOrderData.agentMan;
            self.common.agentDeptCode = self.basicWorkOrderData.agentDeptCode;
            self.common.agentDeptName = self.basicWorkOrderData.agentDeptName;
            self.common.copyDeptCode = self.basicWorkOrderData.copyDeptCode;
            self.common.copyDeptName = self.basicWorkOrderData.copyDeptName;
            // 添加受理时限
            self.common.acceptTimeLimit = self?.basicWorkOrderData?.acceptTimeLimit;
            self.common.faultLevel = self.basicWorkOrderData.faultLevelCode;
            self.common.acceptMan = self.basicWorkOrderData.acceptMan;
            self.common.acceptDeptName = self.basicWorkOrderData.acceptDeptName;
            self.common.copyManId = self.basicWorkOrderData.copyManId;
            self.common.copyMan = self.basicWorkOrderData.copyMan;
            self.common.sheetStatus = self.headInfo.sheetStatus;
            self.timing.hangTime = self.basicWorkOrderData.suspendDuration;
            self.common.professionalType =
              self.basicWorkOrderData.professionalTypeId;
            self.common.professionalTypeName =
              self.basicWorkOrderData.professionalType;
            self.common.networkType = self.basicWorkOrderData.networkTypeId;
            self.common.networkTypeName = self.basicWorkOrderData.networkType;
            self.common.hangOver = self.basicWorkOrderData.suspendDuration;
            self.common.faultCauseDescription =
              self.basicWorkOrderData.ppResult;
            self.common.remainAcceptTime =
              self.basicWorkOrderData.remainAcceptTime;
            self.common.networkTypeTop = self.networkTypeTop;
            self.common.processTimeLimit =
              self.basicWorkOrderData.processTimeLimit;
            self.isTotalWorkingTimeShow =
              self.basicWorkOrderData.isDefined == 1 ? false : true;
            this.ppResult = self.basicWorkOrderData.ppResult;
            this.analysisStatus = self.basicWorkOrderData.analysisStatus;

            self.common.isFeedBack = self.basicWorkOrderData.isFeedBack;
            this.showClxq = false;
            this.showLcrz = false;
            this.showLct = false;
            this.showAlarm = false;
            this.$nextTick(() => {
              this.showClxq = true;
              this.showLcrz = true;
              this.showLct = true;
              this.showAlarm = true;
            });
            this.getMessageNum();
            if (
              (this.headInfo.sheetStatus == "待定性" &&
                this.common.isFeedBack == "Y") ||
              this.headInfo.sheetStatus == "待定性审核" ||
              this.headInfo.sheetStatus == "已归档" ||
              this.headInfo.sheetStatus == "待评价"
            ) {
              this.showFkdxq = false;
              this.$nextTick(() => {
                this.showFkdxq = true;
              });
            } else {
              this.showFkdxq = false;
            }
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
    getWorkOrderSpentTime(basicData) {
      if (
        basicData.sheetStatusName == "异常归档" ||
        basicData.sheetStatusName == "已归档" ||
        basicData.sheetStatusName == "作废"
      ) {
        let days = moment(
          basicData.sheetFilingTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      } else {
        let days = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      }
    },
    buttonClick(name) {
      this.common.actionName = name;
      switch (name) {
        case "受理":
          this.accept(name);
          break;
        case "已阅":
          this.haveRead();
          break;
        case "定性审核":
          this.dialogQualitativeAuditVisible = true;
          break;
        case "阶段反馈":
          this.dialogStageFeedbackVisible = true;
          break;
        case "申请挂起":
          this.dialogHangUpVisible = true;
          break;
        case "挂起":
          this.dialogHangUpVisible = true;
          break;
        case "解挂":
          this.dialogSolutionToHangVisible = true;
          break;
        case "挂起审核":
          this.opContent = 1;
          this.dialogPendingReviewVisible = true;
          break;
        case "转派":
          this.dialogTurnToSendVisible = true;
          break;
        case "处理完成":
          this.handleProcessFinish();
          break;
        case "消障确认":
          this.random = Math.random();
          this.dialogEliminateFaultsVisible = true;
          break;
        case "故障定性":
          this.dialogQualitativeVisible = true;
          break;
      }
    },
    JumpDetails_Wireless(data) {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      this.$router.replace({
        name: "wireless_orderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "待办工单",
          processNode: data.processNode,
          networkType: data.networkType,
          professionalType: data.professionalType,
        },
      });
    },
    accept(buttonName) {
      this.processFullscreenLoading = true;
      sessionStorage.setItem(this.common.woId, "受理");
      let param = {
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        actionName: buttonName,
        processInstId: this.common.processInstId,
      };
      apiAccept(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("您已受理成功");
            this.common.workItemId = res.data.workItemId;
            this.common.processDefId = res.data.processDefId;
            this.common.processInstId = res.data.processInstId;
            //根据新生成的workItemId再次调用显示按钮的接口
            // this.getShowButton();
            this.getWorkOrderInfo();
            this.JumpDetails_Wireless(this.common);
          } else {
            this.$message.error("受理失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("受理失败");
          this.processFullscreenLoading = false;
        });
    },
    dialogBackSingleClose() {
      this.dialogBackSingleVisible = false;
    },

    //定性提交
    qualitativeSubmit(data) {
      this.common.workItemId = data.workItemId;
      this.common.processInstId = data.processInstId;
      this.common.processDefId = data.processDefId;
      this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },

    //定性审核提交
    qualitativeReviewSubmit(data) {
      this.common.workItemId = data.workItemId;
      this.common.processInstId = data.processInstId;
      this.common.processDefId = data.processDefId;
      this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    // 阶段反馈
    dialogStageFeedbackClose() {
      this.dialogStageFeedbackVisible = false;
    },
    stageBackDialogCommitClose() {
      this.getWorkOrderInfo();

      this.dialogStageFeedbackVisible = false;
    },
    //挂起关闭
    dialogHangUpClose() {
      this.dialogHangUpVisible = false;
    },
    //挂起提交
    dialogHangUpSubmitClose() {
      this.dialogHangUpVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //解挂关闭
    dialogSolutionToHangClose() {
      this.dialogSolutionToHangVisible = false;
    },
    //解挂提交
    dialogSolutionToHangSubmitClose() {
      this.dialogSolutionToHangVisible = false;
      this.closeAndTurnAround();
    },
    //挂起审核关闭
    dialogPendingReviewClose() {
      this.dialogPendingReviewVisible = false;
    },
    //挂起审核提交
    dialogPendingReviewSubmitClose() {
      this.dialogPendingReviewVisible = false;
      this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    //定性审核 关闭
    dialogQualitativeAuditClose() {
      this.dialogQualitativeAuditVisible = false;
    },
    dialogQualitativeAuditSumbitClose() {
      this.dialogQualitativeAuditVisible = false;
      this.closeAndTurnAround();
    },
    // 派单评价 关闭
    dialogOrderEvaluateClose() {
      this.dialogOrderEvaluateVisible = false;
    },
    dialogOrderEvaluateSumbitClose() {
      this.dialogOrderEvaluateVisible = false;
      this.closeAndTurnAround();
    },
    // 消障确认 关闭
    dialogEliminateFaultsClose() {
      this.dialogEliminateFaultsVisible = false;
    },
    dialogEliminateFaultsSubmitClose() {
      this.dialogEliminateFaultsVisible = false;
      this.closeAndTurnAround();
    },
    // 故障定性 关闭
    qualitativeClose() {
      this.dialogQualitativeVisible = false;
      this.$refs.qualitative.onReset();
    },
    dialogQualitativeSubmitClose() {
      this.dialogQualitativeVisible = false;
      this.closeAndTurnAround();
    },
    //解挂审核关闭
    dialogSolutionToHangAuditClose() {
      this.dialogSolutionToHangAuditVisible = false;
    },
    //解挂审核提交
    dialogSolutionToHangAuditSubmitClose() {
      this.dialogSolutionToHangAuditVisible = false;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    //已阅
    haveRead() {
      this.processFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiHaveRead(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("已阅");
            this.closeAndTurnAroundRead();
          } else {
            this.$message.error("已阅失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("已阅失败");
          this.processFullscreenLoading = false;
        });
    },
    //转派
    dialogTurnToSendClose() {
      this.dialogTurnToSendVisible = false;
    },
    //转派提交
    dialogTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogTurnToSendVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogTurnToSendVisible = false;
      }
    },
    //重新转派
    dialogAgainTurnToSendClose() {
      this.dialogAgainTurnToSendVisible = false;
    },
    dialogAgainTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogAgainTurnToSendVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogAgainTurnToSendVisible = false;
      }
    },
    // 处理完成 关闭
    handleProcessFinish() {
      this.$confirm("是否对该工单执行 【处理完成】操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      }).then(() => {
        this.processFullscreenLoading = true;
        let params = {
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          processInstId: this.common.processInstId,
          actionName: "处理完成",
          auditUser: this.userInfo.userName,
          auditTime: getCurrentTime(Date.now()),
        };
        apiProcessComplete(params)
          .then(res => {
            if (res.status == "0") {
              this.$message({
                type: "success",
                message: "处理完成!",
              });
              sessionStorage.removeItem(this.common.woId);

              this.closeAndTurnAround();
            } else {
              this.$message({
                type: "success",
                message: res.msg,
              });
              return false;
            }
          })
          .catch(error => {
            console.log(error);
            this.$message.error(error.msg);
          })
          .finally(() => {
            this.processFullscreenLoading = false;
          });
      });
    },
    getStatusStyle() {
      if (
        // this.common.sheetStatus == "待定性" ||
        this.common.sheetStatus == "待定性审核"
      ) {
        return "color:#b50b14;border-color:#e9b6b9;background-color:#f8e7e8";
      }
    },
    onHeadHandleClick(command) {
      this.$refs[command]?.$el?.scrollIntoView?.({ behavior: "smooth" });
    },
    showTime(val) {
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分";
      }
      return time;
    },
    closeAndTurnAround() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    closeAndTurnAroundRead() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toReadRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    // PC现场打点
    submitLocationForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const params = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            actionName: "现场打点",
            longitude: this.locationForm.longitude,
            latitude: this.locationForm.latitude,
            manageuser: this.userInfo.userName,
            actualSceneSign: "否",
          };
          apiLocation(params)
            .then(res => {
              this.dialogLocationVisible = false;
              this.$message.success({
                message: "PC现场打点成功！",
              });
            })
            .catch(error => {
              this.$message.error(error.msg);
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 外连接
    handleOuterLink() {
      let params = {
        userId: this.$store.getters.userInfo.userName,
        sheetNo: this.basicWorkOrderData.sheetNo,
        title: "",
      };
      apiGetRsaEncryptWifi(params)
        .then(res => {
          if (res.data != "") {
            this.rsaEncrypt = res.data;
            // this.rsaEncryptUrl = "/nfm3/chatOpsWeb/talk-view?" + res.data;
            this.rsaEncryptUrl =
              "http://10.245.0.121:5412/chatOpsWeb/talk-view?appKey=EOMS&params=" +
              res.data;
            this.chatopsVisible = true;
          } else {
            this.$message.error("获取rsa明文加密结果失败");
          }
        })
        .catch(error => {
          this.$message.error(error.msg);
        });
    },
    // chatops 未读条数
    getMessageNum() {
      let groupIds = [];
      groupIds.push(this.headInfo.sheetId);
      let paramsRsa = {
        userId: this.$store.getters.userInfo.userName,
        groupIds: groupIds,
      };
      apiMessageRsaEncrypt(paramsRsa)
        .then(res => {
          if (res.data != "") {
            let paramsChatops = {
              appKey: "EOMS",
              params: res.data,
            };
            apiGetMeassgeNum(paramsChatops)
              .then(resNum => {
                if (resNum.data != "") {
                  this.messageNum = resNum.data[this.headInfo.sheetId];
                  console.log(resNum.data);
                }
              })
              .catch(error => {
                console.log(error.msg);
              });
          } else {
            this.$message.error("获取rsa明文加密结果失败");
          }
        })
        .catch(error => {
          this.$message.error(error.msg);
        });
    },
    // 消息弹出框关闭之后，未读条数清零
    handleCloseMessage() {
      this.chatopsVisible = false;
      this.messageNum = 0;
    },
    auditResultCallBack(auditResult) {
      this.common.auditResult = auditResult;
    },
    currentProcessor() {
      this.dialogCurrentProcessorVisible = true;
    },
    currentProcessorClose() {
      this.dialogCurrentProcessorVisible = false;
    },
    handleTelecomDispatchOrderBackClose() {
      this.dialogTelecomDispatchOrderBack = false;
      this.$refs.dialogTelecomDispatchOrderBackRuleForm.resetFields();
    },
    handleTelecomDispatchOrderBackSubmit() {
      this.$refs.dialogTelecomDispatchOrderBackRuleForm.validate(valid => {
        if (valid) {
          // TODO: 调用API提交回退原因
          console.log(
            "回退原因:",
            this.dialogTelecomDispatchOrderBackRuleForm.name
          );
          this.$message.success("回退提交成功");
          this.handleTelecomDispatchOrderBackClose();
          // 可选：刷新工单信息
          // this.getWorkOrderInfo();
        } else {
          return false;
        }
      });
    },
    // 测试方法：切换建单方
    switchFaultHandler(type) {
      this.testFaultHandler = type;
      // 同时更新headInfo中的faultHandler，以便其他组件能够响应变化
      this.headInfo.faultHandler = type;
      // 更新basicWorkOrderData中的字段，以便BaseInfo组件能够响应变化
      if (this.basicWorkOrderData) {
        this.$set(this.basicWorkOrderData, FIELD_NAMES.faultHandler, type);
      }
      this.$message.success(`已切换到${type === 'unicom' ? '联通' : '电信'}模式`);
    },
    // 测试方法：切换派单方式
    switchDispatchMode(mode) {
      this.testDispatchMode = mode;
      // 通知Qualitative组件更新派单方式
      this.$nextTick(() => {
        if (this.$refs.qualitative) {
          this.$refs.qualitative.testDispatchMode = mode;
        }
      });
      const modeText = {
        'unicom_to_unicom': '联通派联通',
        'unicom_to_telecom': '联通派电信',
        'telecom_to_unicom': '电信派联通'
      };
      this.$message.success(`已切换到${modeText[mode]}模式`);
    },
  },
};
</script>
<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    position: relative;
    font-size: 20px;
    line-height: 28px;
    font-weight: 700;
  }

  .head-handle-wrap {
    text-align: right;

    .more-dropdown {
      padding: 0;

      @include themify() {
        border: none;
        border-left: 1px solid themed("$--button-default-border-color");
      }
    }

    .btnleft__group {
      margin-left: 5px;
      margin-bottom: 0px;
      margin-top: 10px;
    }
  }

  .divider {
    margin: 12px 0 16px;
  }

  .head-info2 {
    p {
      margin: 0 auto 5px;
    }
  }

  .head-sheetId {
    font-size: 20px;
    line-height: 28px;
  }

  .head-up-down {
    text-align: center;

    & > div:first-child {
      line-height: 20px;

      @include themify() {
        color: themed("$--color-text-regular");
      }
    }

    & > div:last-child {
      font-weight: 400;
      font-size: 18px;
      line-height: 28px;
      white-space: nowrap;
      &.text-primary {
        color: #c43c43;
      }
    }
  }

  .card-title {
    font-weight: 400;
    font-size: 18px;
    box-shadow: 0 0 5px #aaa;
    padding-left: 10px;
  }
}

::v-deep .detail-dialog .el-dialog__body {
  padding: 20px;
}

.outer-link {
  position: fixed;
  top: 55%;
  right: 15px;
  z-index: 99;
  padding: 3px 0px 0px 0px;
  border-radius: 999px;
  box-shadow: 0 0 8px #999;
  background: rgba(255, 255, 255, 0.7);
  width: 56px;
  height: 56px;

  .icon {
    display: block;
    width: 36px;
    height: 36px;
    cursor: pointer;
    background: url("../../../assets/icon_chatops.png") center no-repeat;
    background-size: 68% 60%;
    margin-left: 10px;
  }
  .num {
    position: absolute;
    top: -10px;
    right: -5px;
    padding: 2px 8px;
    color: #fff;
    background: #b50b14;
    border-radius: 999px;
  }
  .name {
    color: #b50b14;
    font-size: 11px;
    position: absolute;
    margin-top: -4px;
    font-weight: 500;
    text-align: center;
    width: 56px;
  }
}
</style>
