<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <div class="header-tabMenu">
        <span class="header-title" style="margin-top: 5px; margin-right: 10px"
          >流程图</span
        >
        <div
          v-if="
            tabMenu.length > 0 &&
            common.professionalType != 23 &&
            common.professionalType != 22
          "
          style="display: inline-block"
        >
          <!-- <el-tabs type="card" @tab-click="handleClick">
          <el-tab-pane v-for="(tab, key) in tabMenu" :key="key" :label="tab.processName">
          </el-tab-pane>
        </el-tabs> -->
          <el-radio-group v-model="radio1" @change="handleClick" size="mini">
            <el-radio-button
              v-for="(tab, key) in tabMenu"
              :key="key"
              :label="key"
            >
              {{ tab.processName }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div
          ref="iframeContainer"
          :class="['flowchart-container', { 'ai-active': aiAgentActive }]"
          style="border: 1px solid #e8e8e8; margin-top: 10px"
        >
          <iframe
            v-if="woId && processInstID"
            ref="flowChartIframe"
            :src="processUrlWithFitParams"
            frameborder="0"
            :scrolling="aiAgentActive ? 'no' : 'auto'"
            width="100%"
            :height="aiAgentActive ? '100%' : '530'"
            @load="onIframeLoad"
          ></iframe>
        </div>
      </div>
    </div>
  </el-card>
</template>
<script>
import { apiGetProcessTree } from "../../api/FlowChart";
export default {
  name: "FlowChart",
  props: {
    common: Object,
    // 智能体是否展开
    aiAgentActive: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tabMenu: [],
      woId: this.common.woId,
      processInstID: "",
      processUrlPre:
        "http://************:8081/uflow-api/service/uflowGraph/showWFGraph?zoom=1&tenantId=eoms_netfm3",
      processUrl: "",
      radio1: "0",
      // 响应式相关
      containerWidth: 0,
      containerHeight: 530,
      resizeObserver: null,
      resizeTimer: null,
      reloadTimer: null,
      currentZoom: 1,
    };
  },
  computed: {
    // 根据智能体状态决定URL
    processUrlWithFitParams() {
      if (!this.processUrl) return '';

      if (this.aiAgentActive) {
        // 智能体展开时：使用完美适应逻辑
        const width = this.containerWidth || 800;
        const height = this.containerHeight || 530;
        const zoom = this.calculateFitZoom(width, height);

        let url = this.processUrl.replace(/zoom=[\d.]+/, `zoom=${zoom}`);
        const separator = url.includes('?') ? '&' : '?';
        url += `${separator}width=${width}&height=${height}&fit=true&timestamp=${Date.now()}`;

        return url;
      } else {
        // 智能体关闭时：保持原有逻辑
        return this.processUrl;
      }
    }
  },
  watch: {
    // 监听智能体状态变化
    aiAgentActive: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          console.log(`FlowChart: AI Agent ${newVal ? 'activated' : 'deactivated'}`);

          this.$nextTick(() => {
            // 延迟一点时间确保布局变化完成
            setTimeout(() => {
              this.updateContainerSize();

              // 智能体状态变化时，强制重新计算zoom
              if (newVal && this.containerWidth > 0) {
                const newZoom = this.calculateFitZoom(this.containerWidth, this.containerHeight);
                console.log(`FlowChart: Recalculating zoom for container ${this.containerWidth}x${this.containerHeight}px, new zoom: ${newZoom}`);
              }

              this.reloadIframe();
            }, 350); // 等待CSS transition完成
          });
        }
      },
      immediate: false
    }
  },
  mounted() {
    this.getTabMenu();
    this.initResizeObserver();
    this.setResponsiveHeight();

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleWindowResize);

    // 初始化时检测小屏幕并优化
    this.optimizeForSmallScreen();
  },
  beforeDestroy() {
    this.cleanupResizeObserver();
    window.removeEventListener('resize', this.handleWindowResize);

    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
    if (this.reloadTimer) {
      clearTimeout(this.reloadTimer);
    }
  },
  methods: {
    //监听切换流程图
    setprocessUrl() {
      let userRealName = JSON.parse(sessionStorage.userInfo).realName;
      let username = JSON.parse(sessionStorage.userInfo).userName;
      this.processUrl =
        this.processUrlPre +
        `&processInstID=${this.processInstID}&userRealName=${userRealName}&username=${username}`;
    },
    //获取tab数据
    getTabMenu() {
      let param = {
        woId: this.woId,
      };
      let param1 = JSON.stringify(param);
      apiGetProcessTree(param1).then(res => {
        if (res.status == 0) {
          let self = this;
          if (
            this.common.professionalType == "23" ||
            this.common.professionalType == "22"
          ) {
            self.tabMenu = res.data || [];
            self.processInstID = self.tabMenu[0];
          } else {
            self.tabMenu = res.data || [];
            for (let tab in self.tabMenu) {
              if (
                JSON.parse(
                  JSON.parse(sessionStorage.userInfo).attr2
                ).orgInfo.fullOrgName.indexOf(self.tabMenu[tab].processName) >
                -1
              ) {
                self.processInstID = self.tabMenu[tab].processId;
                self.radio1 = tab;
              }
            }
          }
          if (self.processInstID == "") {
            self.processInstID = self.tabMenu[0].processId;
          }
          self.processUrlPre = res.msg;
          self.setprocessUrl();
        }
      });
    },
    //tab切换流程
    handleClick(index) {
      this.processInstID = this.tabMenu[index].processId;
      this.setprocessUrl();
    },



    // 计算完美适应的zoom值
    calculateFitZoom(width, height) {
      if (width === 0 || height === 0) return 1;

      // 固定高度为530，主要根据宽度计算zoom
      const fixedHeight = 530;

      // 流程图的原始尺寸估算
      const originalWidth = 1350;  // 根据实际流程图内容调整
      const originalHeight = 500;  // 固定高度对应的原始高度

      // 减少左右边距，充分利用容器宽度
      let horizontalMargin = 20; // 大幅减少水平边距
      let verticalMargin = 20;   // 减少垂直边距

      if (this.aiAgentActive) {
        // 智能体激活时，容器更窄，进一步减少边距
        horizontalMargin = 15;
        verticalMargin = 15;
      }

      // 计算基于宽度的缩放比例（主要依据）
      const widthRatio = (width - horizontalMargin) / originalWidth;
      const heightRatio = (fixedHeight - verticalMargin) / originalHeight;

      // 优先考虑宽度适应，但确保高度不超出
      let zoom = Math.min(widthRatio, heightRatio);

      // 为了充分利用空间，减少安全边距
      zoom = zoom * 0.98; // 从95%提高到98%

      // 调整zoom范围，允许更大的缩放以减少空白
      let minZoom = 0.3;  // 提高最小zoom
      let maxZoom = 1.8;  // 允许更大的缩放

      const screenWidth = window.innerWidth;
      if (screenWidth <= 1366) {
        minZoom = 0.25;
        maxZoom = 1.5;
      }

      // 限制zoom范围
      zoom = Math.max(minZoom, Math.min(maxZoom, zoom));

      // 保留三位小数
      return Math.round(zoom * 1000) / 1000;
    },

    // 初始化容器尺寸监听
    initResizeObserver() {
      this.$nextTick(() => {
        const container = this.$refs.iframeContainer;
        if (container) {
          // 初始化容器尺寸
          this.updateContainerSize();

          // 使用ResizeObserver监听容器尺寸变化
          if (window.ResizeObserver) {
            this.resizeObserver = new ResizeObserver(entries => {
              for (let entry of entries) {
                const newWidth = Math.floor(entry.contentRect.width);
                const newHeight = Math.floor(entry.contentRect.height);

                // 只有在智能体展开时才进行尺寸适应
                if (this.aiAgentActive) {
                  // 降低触发阈值，更敏感地检测变化
                  if (Math.abs(newWidth - this.containerWidth) > 10 ||
                      Math.abs(newHeight - this.containerHeight) > 10) {
                    this.containerWidth = newWidth;
                    this.containerHeight = newHeight;

                    // 添加防抖，避免频繁重载
                    clearTimeout(this.reloadTimer);
                    this.reloadTimer = setTimeout(() => {
                      this.reloadIframe();
                    }, 150);
                  }
                }
              }
            });
            this.resizeObserver.observe(container);
          }
        }
      });
    },

    // 清理监听器
    cleanupResizeObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      }
    },

    // 设置固定高度
    setResponsiveHeight() {
      // 固定高度为530px
      this.containerHeight = 530;
    },

    // 处理窗口大小变化
    handleWindowResize() {
      this.setResponsiveHeight();
      // 延迟更新，避免频繁触发
      clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(() => {
        this.updateContainerSize();
        this.reloadIframe();
      }, 300);
    },

    // 更新容器尺寸
    updateContainerSize() {
      const container = this.$refs.iframeContainer;
      if (container) {
        this.containerWidth = Math.floor(container.clientWidth);
        this.containerHeight = Math.floor(container.clientHeight);
      }
    },

    // 兼容旧方法名
    updateContainerWidth() {
      this.updateContainerSize();
    },

    // 重新加载iframe
    reloadIframe() {
      if (this.processUrl) {
        const iframe = this.$refs.flowChartIframe;
        if (iframe) {
          if (this.aiAgentActive && this.containerWidth > 0 && this.containerHeight > 0) {
            // 智能体展开时：使用完美适应逻辑
            const newZoom = this.calculateFitZoom(this.containerWidth, this.containerHeight);
            console.log(`FlowChart AI Active: Container ${this.containerWidth}x${this.containerHeight}px, zoom: ${newZoom}`);
          } else {
            // 智能体关闭时：使用原有逻辑
            console.log('FlowChart AI Inactive: Using original logic');
          }

          // 强制重新加载iframe
          iframe.src = this.processUrlWithFitParams;
        }
      }
    },

    // iframe加载完成事件
    onIframeLoad() {
      const zoom = this.calculateFitZoom(this.containerWidth, this.containerHeight);
      console.log(`FlowChart iframe loaded: ${this.containerWidth}x${this.containerHeight}px, zoom: ${zoom}`);

      // 向iframe发送适应容器的消息
      this.$nextTick(() => {
        const iframe = this.$refs.flowChartIframe;
        if (iframe && iframe.contentWindow) {
          try {
            iframe.contentWindow.postMessage({
              type: 'fitContainer',
              width: this.containerWidth,
              height: this.containerHeight,
              zoom: zoom
            }, '*');
          } catch (error) {
            console.log('Cannot send fit message to iframe:', error);
          }
        }
      });
    },

    // 小屏幕优化
    optimizeForSmallScreen() {
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;

      // 检测是否为小笔记本屏幕
      if (screenWidth <= 1366 && screenHeight <= 768) {
        console.log('Small laptop screen detected, optimizing FlowChart...');

        // 强制更新容器尺寸
        this.$nextTick(() => {
          this.updateContainerSize();

          // 如果智能体已激活，重新计算zoom
          if (this.aiAgentActive) {
            setTimeout(() => {
              this.reloadIframe();
            }, 100);
          }
        });
      }
    },

    // 强制完美适应（供外部调用）
    forceOptimalFit() {
      console.log('FlowChart: Force optimal fit triggered');
      this.updateContainerSize();

      if (this.aiAgentActive && this.containerWidth > 0) {
        const zoom = this.calculateFitZoom(this.containerWidth, this.containerHeight);
        console.log(`FlowChart: Force fit - Container: ${this.containerWidth}x${this.containerHeight}px, Zoom: ${zoom}`);

        setTimeout(() => {
          this.reloadIframe();
        }, 50);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";

// 流程图容器 - 固定高度530px
.flowchart-container {
  width: 100%;
  height: 530px; // 固定高度
  position: relative;
  background: #fff;
  transition: all 0.3s ease;

  iframe {
    display: block;
    border: none;
    background: #fff;
    transition: all 0.3s ease;
  }

  // 智能体展开时：完美适应模式
  &.ai-active {
    overflow: hidden; // 隐藏滚动条

    iframe {
      object-fit: contain; // 保持比例适应
    }
  }

  // 智能体关闭时：保持原有样式（自动滚动）
  &:not(.ai-active) {
    overflow: auto; // 允许滚动
  }
}

// 确保父容器也是响应式的
.header-tabMenu {
  width: 100%;

  > div:last-child {
    width: 100%;
    position: relative;
  }
}
</style>
