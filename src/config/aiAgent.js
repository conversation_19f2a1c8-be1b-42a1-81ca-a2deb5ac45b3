/**
 * 智能体配置文件
 * 用于管理不同环境下的智能体URL配置
 */

// 智能体URL配置选项
export const AI_AGENT_URL_CONFIG = {
  // 方案1: 相对路径（通过nginx转发）- 推荐用于生产环境
  RELATIVE_PATH: '/nfm3/ai-front-end/#/ai',
  // # 对接智能体 - 修改为相对路径转发
  
  // 方案2: 开发环境直连（新的智能体服务地址）http://*************:7066/#/ai
  DEVELOPMENT_DIRECT: 'http://************:8088/nfm3/ai-front-end/#/ai',
  // http://************:8088/nfm3/ai-front-end/#/ai
  
  // 方案3: 生产环境直连（原有的智能体服务地址）
  PRODUCTION_DIRECT: 'http://************:8090/ai-front-end/#/ai',
  
  // 方案4: 本地开发环境
  LOCAL_DEV: 'http://localhost:7066/#/ai'
};

// 当前使用的URL配置
// 修改这里可以快速切换不同的配置方案
export const CURRENT_AI_AGENT_URL = AI_AGENT_URL_CONFIG.RELATIVE_PATH;

// 根据环境自动选择URL的函数
export function getAiAgentUrl() {
  // 可以根据环境变量或其他条件自动选择
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';
  
  // 开发环境使用直连，生产环境使用相对路径
  if (isDevelopment) {
    // 开发环境可以选择直连或相对路径
    return AI_AGENT_URL_CONFIG.DEVELOPMENT_DIRECT;
    // return AI_AGENT_URL_CONFIG.RELATIVE_PATH; // 如果开发环境也要测试nginx转发
  } else if (isProduction) {
    // 生产环境使用相对路径（通过nginx转发）
    return AI_AGENT_URL_CONFIG.RELATIVE_PATH;
  } else {
    // 默认使用相对路径
    return AI_AGENT_URL_CONFIG.RELATIVE_PATH;
  }
}

// 智能体配置选项
export const AI_AGENT_CONFIG = {
  title: '智能助手',
  width: 400,
  height: '100%',
  allowFullscreen: true
};

// 智能体用户偏好默认设置
export const AI_AGENT_USER_PREFERENCES = {
  autoShow: false, // 是否自动显示
  defaultWidth: 33.33, // 默认宽度百分比
  rememberState: true, // 是否记住显示状态
  autoHideOnPageDeactivate: true, // 页面失活时自动隐藏智能体
};

// 导出默认配置
export default {
  url: CURRENT_AI_AGENT_URL,
  config: AI_AGENT_CONFIG,
  userPreferences: AI_AGENT_USER_PREFERENCES,
  getUrl: getAiAgentUrl
};
