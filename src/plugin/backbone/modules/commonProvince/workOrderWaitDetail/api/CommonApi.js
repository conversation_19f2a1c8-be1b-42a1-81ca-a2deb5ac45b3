import {
  getJson,
  postJson,
  getJsonBlob,
  postFormData,
  delJson,
  postJsonBlob,
} from "@/utils/axios";
const apiDictUrl = "commonDict/enum/list"; //公用枚举
const showButtonUrl = "commonprovince/operation/getBtnList"; //详情页面中的按钮展示
const transferSubmitUrl = "commonprovince/operation/action"; // 转派
const actionUrlPublic = "commonprovince/operation/action"; // 通用操作
const groupCodeUrl = "commonprovince/operation/getId"; //获取上传附件id
const getIdUrl = "province/operation/getId"; // 发起工单——初始化工单号 woid
const getWoidUrl = "province/operation/getOrderByCode"; // 外网调用页面时，通过工单号，查询woid
const postGisUrl = "commonprovince/order/getGisInfo"; //现在打点经纬度
const postLocation = "commonprovince/operation/action"; // PC现场打点

const processUrl = "commonprovince/order/getHandleDetailsMerge"; //工单详情，处理详情
const workOrderInfoUrl = "commonprovince/order/getOrderDetail"; //详情页面中的工单基本信息
const exportWorkOrderUrl = "backbone/workflow/queryByWoId/excel"; //工单基本信息导出
const acceptUrl = "commonprovince/operation/action"; //受理
const alarmDetailUrl = "commonprovince/order/getAlarmsPage"; //告警详情表格
const uploadFileDetailUrl = "commonprovince/order/getApproval"; //上传故障报告信息
const syncClearUrl = "commonprovince/operation/syncAlarmsClearedState"; //同步清除
const alarmClearApplyUrl = "commonprovince/operation/action"; //告警清除申请
const manualClearUrl = "commonprovince/operation/action"; //人工确认清除
const relationDiagnosisUrl = "commonprovince/order/getDiagnosis"; //关联诊断
const relationDiagnosisEvaluateInitUrl = "integration/diagnosis/initEvaluation"; //关联诊断功能评价初始化
const relationDiagnosisEvaluateUrl = "integration/diagnosis/saveEvaluation"; //关联诊断功能评价保存
const backSingleUrl = "commonprovince/operation/action"; //返单
const backSingleInitUrl = "commonprovince/operation/getFdbkInit"; //返单初始化
const afterSingleInitUrl = "commonprovince/operation/getAppendFormInit"; //追单初始化
const upInitUrl = "commonprovince/operation/intNetReportForm"; //追单初始化
const qualitativeDetailUrl = "backbone/workflow/ackDefine/detail"; //返查定性详情(返单)
const qualitativeUrl = "backbone/workflow/ackDefine/define"; //定性提交
const qualitativeReviewUrl = "backbone/workflow/ackDefine/defineCheck"; //定性审核
const stageFeedbackUrl = "commonprovince/operation/action"; //阶段反馈
const revokeUrl = "commonprovince/operation/action"; //撤单
const modifyTitleUrl = "commonprovince/operation/saveNetFaultTitle"; //修改主题--福建
const hangUpUrl = "commonprovince/operation/action"; //挂起/解挂 申请
const auditUrl = "commonprovince/operation/action"; //挂起申请审核
const abendUrl = "commonprovince/operation/action"; //异常终止申请
const abendAuditUrl = "commonprovince/operation/action"; //异常终止审核
const afterSingleUrl = "commonprovince/operation/action"; //追单
const haveReadUrl = "backbone/workflow/read/setReadStatus"; //已阅
const circulatedUrl = "backbone/workflow/read/addReadInfo"; //传阅
const circulatedUserTreeUrl = "backbone/tree/searchRedeployUser"; //传阅用户树数据
const circulatedOrgTreeUrl = "backbone/tree/searchRedeployOrg"; //传阅组织树数据
const faultAreaUrl = "backbone/info/faultArea"; //故障发生地区字典数据
const queryAttachmentListUrl = "backbone/info/attachmentList"; //附件组编码查询附件列表
const downloadAppendixUrl = "commonDict/attach/download"; //公共下载附件接口
const deleteFdFileUrl = "backbone/workflow/ackDefine/deleteAttach"; //删除附件接口
const manualFileUrl = "backbone/workflow/queryCircuitAttachment/excel";
const fluenceExcelUrl = "backbone/workflow/queryCutInfluenceExcel/excel"; //光缆excel
const provinceDictUrl = "backbone/info/provinceDict";
const backSingleInternationUrl = "backbone/workflow/intAckDefine/intFeedback"; //国际局返单
const qualitativeInternationDetailUrl =
  "backbone/workflow/intAckDefine/intDetail"; //国际局定性详情
const qualitativeInternationUrl = "backbone/workflow/intAckDefine/intDefine"; //国际局定性
const qualitativeReviewInternationUrl =
  "backbone/workflow/intAckDefine/intDefineCheck"; //国际局定性审核
const itCloudAcceptUrl = "backbone/workflow/it/cloud/accept"; //IT云受理
const oneKeyIvrNoticeUrl = "backbone/workflow/oneKeyIvr/oneKeyIvrNotice"; //一键IVR
const itCloudHangUpUrl = "backbone/workflow/it/cloud/applyInterrupt"; //IT云挂起/解挂 申请
const itCloudAuditUrl = "backbone/workflow/it/cloud/checkInterrupt"; //IT云挂起/解挂 审核
const itCloudAbendUrl = "backbone/workflow/it/cloud/applyInterrupt"; //IT云异常终止
const itCloudAbendAuditUrl = "backbone/workflow/it/cloud/checkInterrupt"; //IT云异常终止 审核
const logUrl = "commonprovince/order/getHandleLogs"; // 工单详情- 流程日志
const shareInfoUrl = "commonprovince/order/getShare"; // 工单详情- 共建共享信息
const drafOrderUrl = "commonprovince/operation/getOrderOne"; // 草稿回显
const setReadUrl = "commonprovince/operation/setReadStatus"; // 待阅列表，打开详情页，设置待阅状态为已阅
const clearQueryUrl = "province/workflow/clearQuery"; // 同步告警清除
const feedbackUrl = "commonprovince/order/getFeedBack"; //工单详情-定性详情
const initQualitative = "province/operation/getFdbkInit"; // 初始化故障定性
const rsaEncryptUrl = "province/operation/getRsaEncrypt"; //外连接参数请求

const messageRsaEncryptUrl = "province/operation/getRsaNoReadParams"; // 获取当前用户群聊未读消息数的params加密参数
const getMessageNumUrl = "chatOps/chatUrl/chatOps/v1/groups/unreadMessage"; // chatops 未读条数
const fileDownloadUrl = "commonprovince/attach/download"; //附件下载
const getPeerProcessorListUrl = "commonprovince/operation/getPeerProcessor"; //获取同行人详情
const resourceInitUrl = "commonprovince/operation/getCorrectionInit"; //资源勘误初始化
const correctionGetRegion = "integration/correction/getRegion"; //资源勘误获取区域
const correctionSubmit = "commonprovince/operation/correction"; //资源勘误提交

const getFlowChartUrl =
  "/commonIntegration/process/queryTroubleshootingFlowChart";
const getAlarmsPageUrl = "commonprovince/order/getAlarmsPage"; // 取告警接口中的主告警
const otdrUrl = "commonIntegration/process/queryOtdrGisChart";
const transRelationDiagnosisUrl = "integration/opticalPower/queryTransRelation";

const guangshuaiUrl = "commonIntegration/process/queryTrendChart";
const MoreRelationInfoUrl = "commonIntegration/process/queryMoreRelationInfo";
const getProcessNodeUrl = "/commonprovince/operation/getProcessNode";
const apiDict = params => getJson(apiDictUrl, params);
const apiGetShowButton = params => postJson(showButtonUrl, params);
const apiTransferSubmit = params =>
  postJson(transferSubmitUrl, params, { timeout: 0 }); //转派
const apiActionPublic = params =>
  postJson(actionUrlPublic, params, { timeout: 0 }); // 通用操作接口
const apiGroupCode = params => getJson(groupCodeUrl, params); // 通用操作接口
const apiGetId = params => getJson(getIdUrl, params);
const apiGetWoid = params => getJson(getWoidUrl, params);

const apiGetWorkOrderInfo = params => postJson(workOrderInfoUrl, params);
const apiExportWorkOrder = params => getJsonBlob(exportWorkOrderUrl, params);
const apiAccept = params => postJson(acceptUrl, params, { timeout: 0 });
const apiQueryAlarmDetail = params => postJson(alarmDetailUrl, params);
const apiQueryUploadFileDetail = params =>
  postJson(uploadFileDetailUrl, params);
const apiSyncClear = params => postJson(syncClearUrl, params);
const apiAlarmClearApply = params =>
  postJson(alarmClearApplyUrl, params, { timeout: 0 });
const apiManualClear = params =>
  postJson(manualClearUrl, params, { timeout: 0 });
const apiGetRelationDiagnosis = params =>
  postJson(relationDiagnosisUrl, params);
const apiEvaluateInitRelationDiagnosis = params =>
  postJson(relationDiagnosisEvaluateInitUrl, params);
const apiEvaluateRelationDiagnosis = params =>
  postJson(relationDiagnosisEvaluateUrl, params);
const apiGetShareInfo = params => postJson(shareInfoUrl, params);
const apiBackSingle = params => postJson(backSingleUrl, params, { timeout: 0 });
const apiBackSingleInit = params => postJson(backSingleInitUrl, params);
const apiAfterSingleInit = params => postJson(afterSingleInitUrl, params);
const apiUpInit = params => postJson(upInitUrl, params);
const apiQualitativeDetail = params => getJson(qualitativeDetailUrl, params);
const apiQualitative = params => postFormData(qualitativeUrl, params);
const apiQualitativeReview = params =>
  postFormData(qualitativeReviewUrl, params);
const apiStageFeedBack = params =>
  postJson(stageFeedbackUrl, params, { timeout: 0 });
const apiRevoke = params => postJson(revokeUrl, params, { timeout: 0 });
const apiModifyTitle = params => postJson(modifyTitleUrl, params);
const apiHangUp = params => postJson(hangUpUrl, params, { timeout: 0 });
const apiAudit = params => postJson(auditUrl, params, { timeout: 0 });
const apiAbend = params => postJson(abendUrl, params, { timeout: 0 });
const apiAbendAudit = params => postJson(abendAuditUrl, params, { timeout: 0 });
const apiAfterSingle = params =>
  postJson(afterSingleUrl, params, { timeout: 0 });
const apiHaveRead = params => postJson(haveReadUrl, params);
const apiCirculated = params => postJson(circulatedUrl, params);
const apiCirculatedUserTree = params => getJson(circulatedUserTreeUrl, params);
const apiCirculatedOrgTree = params => getJson(circulatedOrgTreeUrl, params);
const apiGetFaultArea = params => getJson(faultAreaUrl, params);
const apiDownloadAppendixFile = params =>
  getJsonBlob(downloadAppendixUrl, params);
const apiQueryAttachment = params => getJson(queryAttachmentListUrl, params);
const apiDeleteFdFile = params => delJson(deleteFdFileUrl, params);
const apiDownloadManualFile = params => postJsonBlob(manualFileUrl, params);
const apiGetProvinceDict = params => getJson(provinceDictUrl, params);
const apiBackSingleInternation = params =>
  postJson(backSingleInternationUrl, params);
const apiQualitativeInternationDetail = params =>
  getJson(qualitativeInternationDetailUrl, params);
const apiQualitativeInternation = params =>
  postJson(qualitativeInternationUrl, params);
const apiQualitativeInternationReview = params =>
  postJson(qualitativeReviewInternationUrl, params);
const apiItCloudAccept = params => postJson(itCloudAcceptUrl, params);
const apioneKeyIvrNotice = params => postJson(oneKeyIvrNoticeUrl, params);
const apiItCloudHangUp = params => postJson(itCloudHangUpUrl, params);
const apiItCloudAudit = params => postJson(itCloudAuditUrl, params);
const apiItCloudAbend = params => postJson(itCloudAbendUrl, params);
const apiItCloudAbendAudit = params => postJson(itCloudAbendAuditUrl, params);
const apifluenceExcel = params => postJsonBlob(fluenceExcelUrl, params);
const apiGetProcessInfo = params => postJson(processUrl, params);
const apiGetProcessLog = params => postJson(logUrl, params);
const apiInitOrderDraf = params => postJson(drafOrderUrl, params);
const apiSetRead = params => postJson(setReadUrl, params);
const apiclearQuery = params => postJson(clearQueryUrl, params);
const apiqueryFeedback = params => postJson(feedbackUrl, params);
const apiInitQualitative = params => postJson(initQualitative, params);
const apiGetRsaEncrypt = params => postJson(rsaEncryptUrl, params);
const apiMessageRsaEncrypt = params => postJson(messageRsaEncryptUrl, params);
const apiGetPeerProcessorList = params =>
  postJson(getPeerProcessorListUrl, params);
const apiResourceInitUrl = params => postJson(resourceInitUrl, params);
const apiCorrectionGetRegion = params => getJson(correctionGetRegion, params);
const apicorrectionSubmit = params => postFormData(correctionSubmit, params);
const apiGetMeassgeNum = params => postJson(getMessageNumUrl, params);
const apiFileDownload = params => getJsonBlob(fileDownloadUrl, params);
const apiFileUpload = (url, params) => postFormData(url, params);
const apiGetGis = params => postJson(postGisUrl, params);
const apiLocation = params => postJson(postLocation, params, { timeout: 0 });
const apiGetPpPointLocation = params => getJson(getPpPointLocationUrl, params);
const apiFlowChart = params => getJson(getFlowChartUrl, params);
const apiGetAlarmsPage = params => postJson(getAlarmsPageUrl, params);

const apiGetOtdrData = params => postFormData(otdrUrl, params);
const apiGetTransRelationDiagnosis = params =>
  getJson(transRelationDiagnosisUrl, params);
const apiGuangshuai = params => getJson(guangshuaiUrl, params);
const apiMoreRelationInfo = params => getJson(MoreRelationInfoUrl, params);
const apiGetProcessNode = params => postJson(getProcessNodeUrl, params);
// 时间戳 ，转化成 日期时间格式。如果是当前时间，传参 Date.now()
function getCurrentTime(timeVal) {
  const time = new Date(timeVal);
  const yy = time.getFullYear();
  const mm =
    time.getMonth() + 1 < 10
      ? "0" + (time.getMonth() + 1)
      : time.getMonth() + 1;
  const dd = time.getDate() < 10 ? "0" + time.getDate() : time.getDate();
  const hh = time.getHours() < 10 ? "0" + time.getHours() : time.getHours();
  const mf =
    time.getMinutes() < 10 ? "0" + time.getMinutes() : time.getMinutes();
  const ss =
    time.getSeconds() < 10 ? "0" + time.getSeconds() : time.getSeconds();
  const gettime = yy + "-" + mm + "-" + dd + " " + hh + ":" + mf + ":" + ss;
  return gettime;
}

export {
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiExportWorkOrder,
  apiAccept,
  apiAlarmClearApply,
  apiQueryAlarmDetail,
  apiQueryUploadFileDetail,
  apiSyncClear,
  apiManualClear,
  apiGetRelationDiagnosis,
  apiEvaluateInitRelationDiagnosis,
  apiEvaluateRelationDiagnosis,
  apiBackSingle,
  apiBackSingleInit,
  apiAfterSingleInit,
  apiUpInit,
  apiQualitativeDetail,
  apiQualitative,
  apiQualitativeReview,
  apiStageFeedBack,
  apiGetShareInfo,
  apiRevoke,
  apiModifyTitle,
  apiHangUp,
  apiAudit,
  apiAbend,
  apiAbendAudit,
  apiAfterSingle,
  apiHaveRead,
  apiCirculated,
  apiCirculatedUserTree,
  apiCirculatedOrgTree,
  apiGetFaultArea,
  apiDownloadAppendixFile,
  apiQueryAttachment,
  apiDeleteFdFile,
  apiDownloadManualFile,
  apiGetProvinceDict,
  apiBackSingleInternation,
  apiQualitativeInternationDetail,
  apiQualitativeInternation,
  apiQualitativeInternationReview,
  apiItCloudAccept,
  apioneKeyIvrNotice,
  apiItCloudHangUp,
  apiItCloudAudit,
  apiItCloudAbend,
  apiItCloudAbendAudit,
  apifluenceExcel,
  apiTransferSubmit,
  apiActionPublic,
  getCurrentTime,
  apiGroupCode,
  apiGetProcessInfo,
  apiGetProcessLog,
  apiInitOrderDraf,
  apiSetRead,
  apiclearQuery,
  apiqueryFeedback,
  apiInitQualitative,
  apiGetRsaEncrypt,
  apiFileDownload,
  apiFileUpload,
  apiDict,
  apiGetId,
  apiGetMeassgeNum,
  apiMessageRsaEncrypt,
  apiGetWoid,
  apiGetGis,
  apiLocation,
  apiGetPeerProcessorList,
  apiResourceInitUrl,
  apiCorrectionGetRegion,
  apicorrectionSubmit,
  apiFlowChart,
  apiGetAlarmsPage,
  apiGetOtdrData,
  apiGetTransRelationDiagnosis,
  apiGuangshuai,
  apiMoreRelationInfo,
  apiGetProcessNode,
};
