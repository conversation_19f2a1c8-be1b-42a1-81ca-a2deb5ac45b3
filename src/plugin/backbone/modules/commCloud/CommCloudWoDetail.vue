<template>
  <head-fixed-layout class="draft-eidt-page" v-loading="pageLoading">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="14" class="head-title">
          <text-collapse
            :text="`【通信云故障工单】${headInfo.title}`"
            :max-lines="2"
          ></text-collapse>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="10" class="head-handle-wrap">
          <el-button-group>
            <el-button type="button" @click="onHeadHandleClick('jcxx')"
              >基础信息</el-button
            >
            <el-button type="button" @click="onHeadHandleClick('gjxq')"
              >告警详情</el-button
            >
            <el-button
              type="button"
              v-if="ppResult || analysisStatus == 1"
              @click="onHeadHandleClick('glzd')"
              >关联诊断</el-button
            >
            <el-button type="button" @click="onHeadHandleClick('fkdxq')"
              >反馈单详情</el-button
            >
            <el-dropdown
              @command="onHeadHandleClick"
              class="el-button more-dropdown"
              size="medium"
            >
              <el-button type="button">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="(item, i) in headMoreDrops"
                  :key="i"
                  :command="item.command"
                >
                  {{ item.title }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-button-group>
          <br />
          <!-- 按钮动态展示 -->
          <el-button
            size="mini"
            type="primary"
            v-for="(item, i) in processButtonArr"
            class="btnleft__group"
            :key="i"
            @click="buttonClick(item.value, item.desc)"
            v-loading.fullscreen.lock="processFullscreenLoading"
            >{{ item.value }}
          </el-button>
        </el-col>
      </el-row>
      <el-divider
        direction="horizontal"
        content-position="left"
        class="divider"
      ></el-divider>
      <div class="head-info2">
        <el-row :gutter="20" tag="p">
          <el-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="12"
            tag="p"
            class="head-sheetId"
          >
            工单编号: {{ headInfo.sheetId }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" tag="p">
            <el-row :gutter="20" type="flex">
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="7"
                tag="p"
                class="head-up-down"
              >
                <div>当前处理人</div>
                <div
                  class="text-truncate"
                  @click="currentProcessor"
                  style="
                    cursor: pointer;
                    text-decoration: underline;
                    color: #b50b14;
                    user-select: unset;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-size: 18px;
                    line-height: 28px;
                  "
                  :title="headInfo.currentHandler"
                >
                  {{ headInfo.currentHandler }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="4"
                tag="p"
                class="head-up-down"
              >
                <div>工单状态</div>
                <div>
                  {{ headInfo.sheetStatus }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="7"
                tag="p"
                class="head-up-down"
                v-if="headInfo.sheetStatus == '待受理'"
              >
                <div>剩余受理时间</div>
                <div class="text-primary" :title="setAcceptTimeLimit">
                  {{ setAcceptTimeLimit }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="7"
                tag="p"
                class="head-up-down"
                v-if="
                  [
                    '处理中',
                    '待定性',
                    '待确认',
                    '待定性审核',
                    '挂起',
                    '上传故障报告',
                    '故障报告审核',
                  ].includes(headInfo.sheetStatus)
                "
              >
                <div>剩余处理时间</div>
                <div class="text-primary" :title="setEstimatedProTimeLimit">
                  {{ setEstimatedProTimeLimit }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="6"
                tag="p"
                class="head-up-down"
              >
                <div>工单总耗时</div>
                <div class="text-primary" :title="headInfo.totalWorkingTime">
                  {{ headInfo.totalWorkingTime }}
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单人: {{ headInfo.buildSingleMan }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单部门: {{ headInfo.buildSingleDept }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单时间: {{ headInfo.buildSingleTime }}
          </el-col>
        </el-row>
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            工单来源: {{ headInfo.sourceInfo }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            发生时间: {{ headInfo.occurrenceTime }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            紧急程度: {{ headInfo.emergencyDegree }}
          </el-col>
        </el-row>
      </div>
    </template>

    <base-info
      ref="jcxx"
      v-if="basicWorkOrderData"
      :basicWorkOrderData="basicWorkOrderData"
      :woId="common.woId"
      :workItemId="common.workItemId"
    />
    <alarm-detail
      ref="gjxq"
      v-if="common.woId && headInfo.occurrenceTime"
      :common="common"
      :occurrenceTime="headInfo.occurrenceTime"
      :isShowClearAlarmApply="isShowClearAlarmApply"
      :isShowManualConfirm="isShowManualConfirm"
      @alarmClearCallBack="alarmClearCallBack"
    />
    <relation-diagnosis
      ref="glzd"
      v-if="common.woId && (ppResult || analysisStatus == 1)"
      :woId="common.woId"
      :ppResult="ppResult"
      :analysisStatus="analysisStatus"
    />
    <feedback-sheet
      ref="fkdxq"
      v-if="showFkdxq && common.woId"
      :isShowAudit="isShowQualitativeReviewButton"
      :common="common"
      :woId="common.woId"
      :qualitativeType="qualitativeType"
      @qualitativeReviewSubmit="qualitativeReviewSubmit"
      @qualitativeSubmit="qualitativeSubmit"
    />
    <deal-details
      ref="clxq"
      v-if="showClxq"
      :woId="common.woId"
      :common="common"
    />
    <process-log ref="lcrz" v-if="showLcrz" :woId="common.woId" />
    <flow-chart ref="lct" v-if="showLct" :common="common" />
    <el-dialog
      title="追单"
      :visible.sync="dialogAfterSingleVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogAfterSingleClose"
      width="600px"
    >
      <after-single
        :common="common"
        @closeAfterSingleDialog="dialogAfterSingleSubmitClose"
      ></after-single>
    </el-dialog>
    <el-dialog
      title="返单"
      :visible.sync="dialogBackSingleVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogBackSingleClose"
      :fullscreen="false"
      width="83%"
      top="5vh"
    >
      <span slot="title">
        <span style="line-height: 24px; font-size: 18px; color: #333"
          >返单</span
        >
        <el-button
          type="primary"
          style="float: right; margin-right: 35px"
          @click="changeRuleVisible"
          >返单审核规则</el-button
        >
      </span>
      <back-single
        ref="backSingleForm"
        :common="common"
        :timing="timing"
        :showType="dialogBackSingleVisible"
        :backSingleTime="backSingleTime"
        @closeBackSingleDialog="dialogBackSingleSubmitClose"
      ></back-single>
    </el-dialog>
    <el-dialog
      width="620px"
      title="返单审核规则"
      :visible.sync="ruleVisible"
      :close-on-click-modal="false"
      append-to-body
    >
      <p
        style="
          font-size: 16px;
          line-height: 130%;
          margin-top: 0;
          margin-bottom: 0;
        "
      >
        返单界面标记“*”符号字段必须按实际情况填写，审核时重点关注内容如下，填写信息包含即可，返单页面无对应字段的请自行选择字段填写：<br />
        (1)排查对象（资源池类型（SDN或非SDN）设备类型、设备名称）<br />
        (2)排查结果<br />
        (3)故障影响范围（据实反馈故障影响业务情况） <br />
        (4)故障处理情况（若需换件请明确时间）<br />
        (5)故障原因分析<br />
        (6)反馈人信息(联系方式)<br />
        日志保留天数最少1天，最多365天，默认180天。<br />
      </p>
    </el-dialog>
    <el-dialog
      title="阶段反馈"
      :visible.sync="dialogStageFeedbackVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogStageFeedbackClose"
      width="450px"
    >
      <stage-feedback
        :common="common"
        @stageBackDialogClose="stageBackDialogCommitClose"
      ></stage-feedback>
    </el-dialog>
    <el-dialog
      title="撤单"
      :visible.sync="dialogRevokeVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogRevokeClose"
      width="480px"
    >
      <el-form
        ref="revokeForm"
        :model="revokeForm"
        :rules="revokeFormRules"
        label-width="90px"
      >
        <el-form-item label="审核意见:" prop="auditOpinion">
          <el-input
            type="textarea"
            :rows="2"
            placeholder="请填写审核意见"
            v-model="revokeForm.auditOpinion"
            style="width: 300px"
            show-word-limit
            maxlength="1000"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="handleRevokeSubmit('revokeForm')"
          v-loading.fullscreen.lock="revokeSubmitLoading"
          >提 交</el-button
        >
        <el-button @click="onResetRevoke">重 置</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="挂起"
      :visible.sync="dialogHangUpVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogHangUpClose"
      width="540px"
    >
      <hang-up
        :common="common"
        :opType="1"
        @closeDialogHangUp="dialogHangUpSubmitClose"
      ></hang-up>
    </el-dialog>
    <el-dialog
      title="解挂"
      :visible.sync="dialogSolutionToHangVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogSolutionToHangClose"
      width="540px"
    >
      <hang-up
        :common="common"
        :opType="2"
        @closeDialogHangUp="dialogSolutionToHangSubmitClose"
      ></hang-up>
    </el-dialog>
    <el-dialog
      title="挂起审核"
      :visible.sync="dialogPendingReviewVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogPendingReviewClose"
      width="450px"
    >
      <audit
        :common="common"
        :opContent="opContent"
        @closeDialogPendingReview="dialogPendingReviewSubmitClose"
      ></audit>
    </el-dialog>
    <el-dialog
      title="解挂审核"
      :visible.sync="dialogSolutionToHangAuditVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogSolutionToHangAuditClose"
      width="450px"
    >
      <audit
        :common="common"
        :opContent="opContent"
        @closeDialogSolutionToHangAudit="dialogSolutionToHangAuditSubmitClose"
      ></audit>
    </el-dialog>
    <el-dialog
      title="异常终止"
      :visible.sync="dialogAbendVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogAbendClose"
      width="450px"
    >
      <abend
        :common="common"
        @closeDialogAbend="dialogAbendSubmitClose"
      ></abend>
    </el-dialog>
    <el-dialog
      title="异常终止审核"
      :visible.sync="dialogAbendAuditVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogAbendAuditClose"
      width="450px"
    >
      <abend-audit
        :common="common"
        @closeDialogAbendAudit="dialogAbendAuditSubmitClose"
      ></abend-audit>
    </el-dialog>
    <el-dialog
      title="转派"
      :visible.sync="dialogTurnToSendVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogTurnToSendClose"
      width="480px"
    >
      <turn-to-send
        @closeDialogTurnToSend="dialogTurnToSendSubmitClose"
        :common="common"
        :type="type"
        actionName="转派"
      >
      </turn-to-send>
    </el-dialog>
    <el-dialog
      title="重新转派"
      :visible.sync="dialogAgainTurnToSendVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogAgainTurnToSendClose"
      width="480px"
    >
      <turn-to-send
        @closeDialogTurnToSend="dialogAgainTurnToSendSubmitClose"
        :common="common"
        :type="type"
        actionName="重新转派"
      ></turn-to-send>
    </el-dialog>
    <one-key-ivr
      :dialogOneKeyIvrNotice.sync="dialogOneKeyIvrNotice"
      :common="common"
      :sheetId="headInfo.sheetId && headInfo.sheetId"
      :sheetCreateTime="headInfo.buildSingleTime && headInfo.buildSingleTime"
    />
    <el-dialog
      title="定性审核"
      :visible.sync="dialogQualitativeReviewVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="qualitativeReviewClose"
      width="83%"
      top="5vh"
    >
      <qualitative-review
        ref="qualitativeReview"
        :common="common"
        :workItemId="common.workItemId"
        v-if="showDxsh"
        @qualitativeReviewSubmit="qualitativeReviewSubmit"
      ></qualitative-review>
    </el-dialog>
    <el-dialog
      title="定性"
      :visible.sync="dialogQualitativeVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :common="common"
      @close="qualitativeClose"
      width="83%"
      top="5vh"
    >
      <qualitative
        ref="qualitative"
        :common="common"
        :showType="dialogQualitativeVisible"
        :workItemId="common.workItemId"
        @qualitativeSubmit="qualitativeSubmit"
      ></qualitative>
    </el-dialog>
    <el-dialog
      title="处理人"
      :visible.sync="dialogCurrentProcessorVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="currentProcessorClose"
      :fullscreen="false"
      width="60%"
      top="5vh"
    >
      <current-processor
        :common="common"
        :persons="basicWorkOrderData.operatePersonId"
      >
      </current-processor>
      <!-- <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="date" label="姓名" width="70"> </el-table-column>
        <el-table-column prop="name" label="组织" width="180">
        </el-table-column>
        <el-table-column prop="address" label="电话" width="100">
        </el-table-column>
        <el-table-column prop="address" label="邮箱" width="100">
        </el-table-column>
      </el-table> -->
      <!-- <span>这是一段信息</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </span> -->
    </el-dialog>
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "../workOrder/components/HeadFixedLayout.vue";
import BaseInfo from "./components/CommCloudBaseInfo.vue";
import AlarmDetail from "./components/CommCoudAlarmDetail.vue";
import FeedbackSheet from "./components/CommCloudFeedbackSheet.vue";
import DealDetails from "./components/CommCloudDealDetails.vue";
import ProcessLog from "../workOrder/workOrderWaitDetail/components/ProcessLog.vue";
import FlowChart from "../workOrder/workOrderWaitDetail/components/FlowChart.vue";
import BackSingle from "./components/CommCloudBackSingle.vue";
import StageFeedback from "../workOrder/workOrderWaitDetail/components/StageFeedback.vue";
import HangUp from "../workOrder/workOrderWaitDetail/components/HangUp.vue";
import Audit from "../workOrder/workOrderWaitDetail/components/Audit.vue";
import Abend from "../workOrder/workOrderWaitDetail/components/Abend.vue";
import AbendAudit from "../workOrder/workOrderWaitDetail/components/AbendAudit";
import TurnToSend from "../workOrder/workOrderWaitDetail/components/TurnToSend.vue";
import oneKeyIvr from "./components/CommCloudoneKeyIVR.vue";
import Qualitative from "./components/CommCloudQualitative.vue";
import QualitativeReview from "./components/CommCloudQualitativeReview.vue";
import AfterSingle from "./components/CommCloudAfterSingle";
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";
import CurrentProcessor from "../workOrder/components/CurrentProcessor.vue";
import RelationDiagnosis from "./components/CommCloudRelationDiagnosis.vue";
import {
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiCommCloudAccept,
  apiRevoke,
  apiHaveRead,
} from "./api/CommCloudApi";
import { mixin } from "../../../../mixins";
export default {
  name: "WorkOrderWaitDetail",
  components: {
    HeadFixedLayout,
    BaseInfo,
    AlarmDetail,
    FeedbackSheet,
    DealDetails,
    ProcessLog,
    FlowChart,
    BackSingle,
    RelationDiagnosis,
    StageFeedback,
    HangUp,
    Audit,
    Abend,
    AbendAudit,
    AfterSingle,
    TurnToSend,
    oneKeyIvr,
    Qualitative,
    QualitativeReview,
    TextCollapse,
    CurrentProcessor,
  },
  mixins: [mixin],
  data() {
    return {
      headInfo: {
        title: "",
        sheetId: "",
        currentHandler: "",
        currentLink: "",
        sheetStatus: null,
        acceptTimeLimit: "",
        totalWorkingTime: "",
        buildSingleMan: "",
        buildSingleDept: "",
        buildSingleTime: "",
        sourceInfo: "",
        occurrenceTime: "",
        emergencyDegree: "",
        estimatedProTimeLimit: "", //剩余处理时间隔
      },
      headMoreDrops: [
        { command: "clxq", title: "处理详情" },
        { command: "lcrz", title: "流程日志" },
        { command: "lct", title: "流程图" },
      ],
      processButtonArr: [],
      basicWorkOrderData: "",
      common: {
        woId: "", //工单ID
        workItemId: "", //工作项ID
        processInstId: null, //流程实例ID
        processDefId: null, //流程定义ID
        failureTime: "", //故障发生时间
        failureInformTime: "", //故障通知时间
        actionName: "", //操作按钮名称
        processNode: "", //环节名称
        sheetNo: "", //工单编号
        isSender: null, //是否为建单人
        agentManId: "", //主送人Id
        agentMan: "", //主送人
        copyManId: "", //抄送人Id
        copyMan: "", //抄送人
        sheetStatus: "", //工单状态
        agentDeptCode: "",
        agentDeptName: "",
        copyDeptCode: "",
        copyDeptName: "",
        professionalType: null,
        professionalTypeName: null, //专业类型名称
        hangOver: null, //挂起历时
        faultCauseDescription: null, //故障原因描述
        busName: null, //业务名称
        auditResult: null,
        isUploadReport: null, //是否上传故障报告
        feedbackResult: null,
        emergencyLevel: null,
        remainAcceptTime: null, //剩余处理时间
        alarmClearTime: null,
      },
      timing: {
        hangTime: "", //挂起时间
      },
      showFkdxq: false,
      showClxq: false,
      showLcrz: false,
      showLct: false,
      ppResult: null,
      analysisStatus: 0,
      processFullscreenLoading: false,
      isShowClearAlarmApply: false,
      //当前处理人
      dialogCurrentProcessorVisible: false,
      //阶段反馈
      dialogStageFeedbackVisible: false,
      //返单
      dialogBackSingleVisible: false, //一干
      //撤单
      dialogRevokeVisible: false,
      revokeSubmitLoading: false,
      revokeForm: {
        auditOpinion: null,
      },
      revokeFormRules: {
        auditOpinion: [
          {
            required: true,
            message: "请填写审核意见",
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "revokeForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      //挂起
      dialogHangUpVisible: false,
      //解挂
      dialogSolutionToHangVisible: false,
      //挂起审核
      auditTitle: null,
      opContent: null,
      dialogPendingReviewVisible: false,
      //解挂审核
      dialogSolutionToHangAuditVisible: false,
      //异常终止
      dialogAbendVisible: false,
      //异常终止审核
      dialogAbendAuditVisible: false,
      //追单
      dialogAfterSingleVisible: false,
      //转派
      dialogTurnToSendVisible: false,
      //类型
      type: "single",
      //重新转派
      dialogAgainTurnToSendVisible: false,
      //一键IVR
      dialogOneKeyIvrNotice: false,
      route: null,
      //从哪个页面进入的，不同页面进入，显示的按钮会不同，如：待阅工单只展示已阅、传阅按钮
      fromPage: null,
      pageLoading: true,
      isShowManualConfirm: false,
      dialogQualitativeReviewVisible: false, //定性审核弹出框
      isShowQualitativeReviewButton: false,
      dialogQualitativeVisible: false, //定性弹出框
      qualitativeType: "",
      showDxsh: false,
      isBackSingle: false,
      ruleVisible: false,
      backSingleTime: 0,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
    setAcceptTimeLimit() {
      let time = this.headInfo.acceptTimeLimit;
      if (time == undefined || time == null || time == "" || time == "-") {
        return "-";
      } else {
        return this.setTime(time);
      }
    },
    setEstimatedProTimeLimit() {
      let time = this.headInfo.estimatedProTimeLimit;
      if (time == undefined || time == null || time == "" || time == "-") {
        return "-";
      } else {
        return this.setTime(time);
      }
    },
  },
  created() {
    this.route = this.$route;
  },
  mounted() {
    this.common.workItemId = this.$route.query.workItemId;
    this.common.processInstId = this.$route.query.processInstId;
    this.common.woId = this.$route.query.woId;
    this.common.processDefId = this.$route.query.processDefId;
    this.fromPage = this.$route.query.fromPage;
    if (this.fromPage == "已阅工单") {
      this.getWorkOrderInfo();
    } else if (this.fromPage == "待阅工单") {
      this.processButtonArr = [{ key: "links", value: "已阅" }];
      this.getWorkOrderInfo();
    } else {
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
    }
  },
  methods: {
    setTime(time) {
      if (time == "-") {
        return "-";
      } else if (time <= 0) {
        const absTime = Math.abs(time);
        let format = "已超时";
        if (absTime < 60) {
          format += `${absTime}分`;
        } else {
          const minFormat = absTime % 60 == 0 ? "" : `${absTime % 60}分`;
          format += `${Math.floor(absTime / 60)}小时${minFormat}`;
        }
        return format;
      } else if (time > 0) {
        if (time < 60) {
          return `${time}分`;
        } else {
          const minFormat = time % 60 == 0 ? "" : `${time % 60}分`;
          return `${Math.floor(time / 60)}小时${minFormat}`;
        }
      }
    },
    setDialogOneKeyIvrNotice(val) {
      this.dialogOneKeyIvrNotice = val;
    },
    changeRuleVisible() {
      this.ruleVisible = true;
    },
    getShowButton() {
      return new Promise(resolve => {
        let param = {
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          processInstId: this.common.processInstId,
          fromPage: this.fromPage,
        };
        apiGetShowButton(param)
          .then(res => {
            if (res.status == "0") {
              let self = this;
              self.processButtonArr = res?.data?.user ?? [];
              self.processButtonArr = this.removeArrByVal(
                self.processButtonArr,
                "撤单"
              ); //过滤掉撤单
              let alarmButton = res?.data?.admin ?? [];
              //定性审核不显示在反馈单详情中
              // if (
              //   qualitativeReviewButton.length > 0 &&
              //   qualitativeReviewButton.some(item => item.value == "定性审核")
              // ) {
              //   this.isShowQualitativeReviewButton = true;
              // } else {
              //   this.isShowQualitativeReviewButton = false;
              // }
              if (
                alarmButton.length > 0 &&
                alarmButton.some(item => item.value == "清除告警申请")
              ) {
                this.isShowClearAlarmApply = true;
              } else {
                this.isShowClearAlarmApply = false;
              }
              if (
                alarmButton.length > 0 &&
                alarmButton.some(item => item.value == "人工确认清除告警")
              ) {
                this.isShowManualConfirm = true;
              } else {
                this.isShowManualConfirm = false;
              }
            }
            resolve("success");
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    getWorkOrderInfo() {
      this.pageLoading = true;
      let formData = new FormData();
      formData.append("woId", this.common.woId);
      formData.append(
        "processInstId",
        this.common.processInstId ? this.common.processInstId : ""
      );
      formData.append(
        "workItemId",
        this.common.workItemId ? this.common.workItemId : ""
      );
      apiGetWorkOrderInfo(formData)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            self.basicWorkOrderData = res?.data?.rows?.[0] ?? {};
            self.headInfo.title = self.basicWorkOrderData.sheetTitle;

            self.headInfo.sheetId = self.basicWorkOrderData.sheetNo;
            self.headInfo.currentHandler =
              self.basicWorkOrderData.operatePerson; // 当前处理人
            self.headInfo.acceptTimeLimit =
              self.basicWorkOrderData.remainAcceptTime;
            self.headInfo.estimatedProTimeLimit =
              self.basicWorkOrderData.remainHandleTime;
            self.headInfo.sheetStatus = self.basicWorkOrderData.sheetStatus;
            self.headInfo.buildSingleMan = self.basicWorkOrderData.senderName;
            self.headInfo.buildSingleDept =
              self.basicWorkOrderData.senderDeptName;
            self.headInfo.buildSingleTime =
              self.basicWorkOrderData.sheetCreateTime;
            this.getWorkOrderSpentTime(self.basicWorkOrderData); //工单总耗时
            self.headInfo.sourceInfo = self.basicWorkOrderData.createType;
            self.headInfo.occurrenceTime =
              self.basicWorkOrderData.alarmCreateTime;
            self.headInfo.emergencyDegree =
              self.basicWorkOrderData.emergencyLevel;
            self.common.failureTime = self.basicWorkOrderData.alarmCreateTime;
            self.common.failureInformTime =
              self.basicWorkOrderData.sheetCreateTime;
            self.common.processNode = self.basicWorkOrderData.processNode;
            self.common.sheetNo = self.basicWorkOrderData.sheetNo;
            self.common.isSender =
              self.basicWorkOrderData.senderName == self.userInfo.realName
                ? 1
                : 0;
            self.common.agentManId = self.basicWorkOrderData.agentManId;
            self.common.agentMan = self.basicWorkOrderData.agentMan;
            self.common.agentDeptCode = self.basicWorkOrderData.agentDeptCode;
            self.common.agentDeptName = self.basicWorkOrderData.agentDeptName;
            self.common.copyDeptCode = self.basicWorkOrderData.copyDeptCode;
            self.common.copyDeptName = self.basicWorkOrderData.copyDeptName;
            // 添加受理时限
            self.common.acceptTimeLimit = self.basicWorkOrderData.acceptTimeLimit;
            self.common.copyManId = self.basicWorkOrderData.copyManId;
            self.common.copyMan = self.basicWorkOrderData.copyMan;
            self.common.sheetStatus = self.headInfo.sheetStatus;
            self.timing.hangTime = self.basicWorkOrderData.suspendDuration;
            self.common.professionalType =
              self.basicWorkOrderData.professionalTypeId;
            self.common.professionalTypeName =
              self.basicWorkOrderData.professionalType;
            self.common.hangOver = self.basicWorkOrderData.suspendDuration;
            self.common.faultCauseDescription =
              self.basicWorkOrderData.ppResult;
            self.common.busName = self.basicWorkOrderData.busName;
            self.common.auditResult = self.basicWorkOrderData.auditResult;
            self.common.isUploadReport = self.basicWorkOrderData.isUploadReport;
            self.common.feedbackResult = self.basicWorkOrderData.feedbackResult;
            self.common.emergencyLevel = self.basicWorkOrderData.emergencyLevel;
            self.common.remainAcceptTime =
              self.basicWorkOrderData.remainAcceptTime;
            self.common.alarmClearTime = self.basicWorkOrderData.alarmClearTime;
            this.ppResult = self.basicWorkOrderData.ppResultOri;
            this.analysisStatus =
              self?.basicWorkOrderData?.analysisStatus ?? null;
            this.showClxq = false;
            this.showLcrz = false;
            this.showLct = false;
            this.$nextTick(() => {
              this.showClxq = true;
              this.showLcrz = true;
              this.showLct = true;
            });
            if (
              self.basicWorkOrderData.auditResult == "0" ||
              self.basicWorkOrderData.feedbackResult == "0" ||
              self.headInfo.sheetStatus == "待定性审核" ||
              self.headInfo.sheetStatus == "待定性" ||
              self.headInfo.sheetStatus == "已归档" ||
              self.headInfo.sheetStatus == "作废"
            ) {
              this.showFkdxq = false;
              this.$nextTick(() => {
                this.showFkdxq = true;
              });
            } else {
              this.showFkdxq = false;
            }
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
    getWorkOrderSpentTime(basicData) {
      if (
        basicData.sheetStatus == "异常归档" ||
        basicData.sheetStatus == "已归档" ||
        basicData.sheetStatus == "作废"
      ) {
        let days = moment(
          basicData.sheetFilingTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      } else {
        let days = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      }
    },
    buttonClick(name, desc) {
      this.common.actionName = name;
      switch (name) {
        case "受理":
          this.accept(name);
          break;
        case "追单":
          this.common.isObject = desc;
          this.dialogAfterSingleVisible = true;
          break;
        case "撤单":
          this.dialogRevokeVisible = true;
          break;
        case "已阅":
          this.haveRead();
          break;
        case "返单":
          if (this.isBackSingle) {
            this.backSingleTime = Math.random();
            this.dialogBackSingleVisible = true;
          } else {
            this.$message.warning("告警未被清除，不可返单");
          }

          break;
        case "阶段反馈":
          this.dialogStageFeedbackVisible = true;
          break;
        case "挂起":
          this.dialogHangUpVisible = true;
          break;
        case "解挂":
          this.dialogSolutionToHangVisible = true;
          break;
        case "挂起审核":
          this.opContent = 1;
          this.dialogPendingReviewVisible = true;
          break;
        case "解挂审核":
          this.opContent = 2;
          this.dialogSolutionToHangAuditVisible = true;
          break;
        case "异常终止":
          this.dialogAbendVisible = true;
          break;
        case "异常终止审核":
          this.dialogAbendAuditVisible = true;
          break;
        case "转派":
          this.dialogTurnToSendVisible = true;
          break;
        case "重新转派":
          this.dialogAgainTurnToSendVisible = true;
          break;
        case "一键催办IVR":
          this.dialogOneKeyIvrNotice = true;
          break;
        case "定性":
          this.dialogQualitativeVisible = true;
          break;
        case "定性审核":
          this.showDxsh = false;
          this.$nextTick(() => {
            this.showDxsh = true;
          });
          this.dialogQualitativeReviewVisible = true;
          break;
      }
    },
    removeArrByVal(arr, val) {
      let index = arr.indexOf(val);
      if (index > -1) {
        arr.splice(index, 1);
      }
      return arr;
    },
    JumpDetails_TXY(data) {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      this.$router.replace({
        name: "commCloud_woDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "待办工单",
          networkType: data.networkType,
        },
      });
    },
    accept(buttonName) {
      this.processFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        actionName: buttonName,
        processInsId: this.common.processInstId,
      };
      apiCommCloudAccept(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("您已受理成功");
            this.common.workItemId = res.data.workItemId;
            this.common.processDefId = res.data.processDefId;
            this.common.processInstId = res.data.processInstId;
            //根据新生成的workItemId再次调用显示按钮的接口
            this.getShowButton();
            this.getWorkOrderInfo();
            this.JumpDetails_TXY(this.common);
          } else {
            this.$message.error("受理失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("受理失败");
          this.processFullscreenLoading = false;
        });
    },
    dialogBackSingleClose() {
      this.dialogBackSingleVisible = false;
      this.$refs.backSingleForm.onReset();
    },
    //返单提交
    dialogBackSingleSubmitClose(data) {
      this.common.workItemId = data.workItemId;
      this.common.processInstId = data.processInstId;
      this.common.processDefId = data.processDefId;
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogBackSingleVisible = false;
      if (data.currentPage) {
        this.JumpDetails_TXY(this.common);
      } else {
        this.closeAndTurnAround();
      }
    },
    //定性提交
    qualitativeSubmit(data) {
      this.dialogQualitativeVisible = false;
      this.closeAndTurnAround();
    },
    qualitativeClose() {
      this.dialogQualitativeVisible = false;
      this.$refs.qualitative.onReset();
    },
    qualitativeReviewClose() {
      this.dialogQualitativeReviewVisible = false;
      this.$refs.qualitativeReview.onReset();
    },
    //定性审核提交
    qualitativeReviewSubmit(data) {
      this.dialogQualitativeReviewVisible = false;
      this.closeAndTurnAround();
    },
    dialogStageFeedbackClose() {
      this.dialogStageFeedbackVisible = false;
    },
    currentProcessor() {
      this.dialogCurrentProcessorVisible = true;
    },
    currentProcessorClose() {
      this.dialogCurrentProcessorVisible = false;
    },
    stageBackDialogCommitClose() {
      this.showClxq = false;
      this.showLcrz = false;
      this.$nextTick(() => {
        this.showLcrz = true;
        this.showClxq = true;
      });
      this.dialogStageFeedbackVisible = false;
    },
    handleRevokeSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.revokeSubmitLoading = true;
          let param = {
            processNode: this.common.processNode,
            sheetNo: this.common.sheetNo,
          };
          apiRevoke(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("撤单成功");
                this.dialogRevokeClose();
                this.closeAndTurnAround();
              } else {
                this.$message.error("撤单失败");
              }
              this.revokeSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("撤单失败");
              this.revokeSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    onResetRevoke() {
      this.revokeForm = {
        ...this.$options.data,
      };
    },
    //撤单
    dialogRevokeClose() {
      this.onResetRevoke();
    },
    //挂起关闭
    dialogHangUpClose() {
      this.dialogHangUpVisible = false;
    },
    //挂起提交
    dialogHangUpSubmitClose() {
      this.dialogHangUpVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //解挂关闭
    dialogSolutionToHangClose() {
      this.dialogSolutionToHangVisible = false;
    },
    //解挂提交
    dialogSolutionToHangSubmitClose() {
      this.dialogSolutionToHangVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //挂起审核关闭
    dialogPendingReviewClose() {
      this.dialogPendingReviewVisible = false;
    },
    //挂起审核提交
    dialogPendingReviewSubmitClose() {
      this.dialogPendingReviewVisible = false;
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.closeAndTurnAround();
    },
    //解挂审核关闭
    dialogSolutionToHangAuditClose() {
      this.dialogSolutionToHangAuditVisible = false;
    },
    //解挂审核提交
    dialogSolutionToHangAuditSubmitClose() {
      this.dialogSolutionToHangAuditVisible = false;
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.closeAndTurnAround();
    },
    dialogAbendClose() {
      this.dialogAbendVisible = false;
    },
    //异常终止提交
    dialogAbendSubmitClose() {
      this.dialogAbendVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    dialogAbendAuditClose() {
      this.dialogAbendAuditVisible = false;
    },
    //异常终止审核
    dialogAbendAuditSubmitClose() {
      this.processButtonArr = [];
      this.dialogAbendAuditVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //已阅
    haveRead() {
      this.processFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiHaveRead(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("已阅");
            this.closeAndTurnAroundRead();
          } else {
            this.$message.error("已阅失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("已阅失败");
          this.processFullscreenLoading = false;
        });
    },
    //追单
    dialogAfterSingleClose() {
      this.dialogAfterSingleVisible = false;
    },
    //追单
    dialogAfterSingleSubmitClose(val) {
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogAfterSingleVisible = false;
      if (val == 1) {
        this.closeAndTurnAround();
      }
    },
    //转派
    dialogTurnToSendClose() {
      this.dialogTurnToSendVisible = false;
    },
    //转派提交
    dialogTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogTurnToSendVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogTurnToSendVisible = false;
      }
    },
    //重新转派
    dialogAgainTurnToSendClose() {
      this.dialogAgainTurnToSendVisible = false;
    },
    dialogAgainTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogAgainTurnToSendVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogAgainTurnToSendVisible = false;
      }
    },
    getStatusStyle() {
      if (
        this.common.sheetStatus == "待定性" ||
        this.common.sheetStatus == "待定性审核"
      ) {
        return "color:#b50b14;border-color:#e9b6b9;background-color:#f8e7e8";
      }
    },
    onHeadHandleClick(command) {
      this.$refs[command]?.$el?.scrollIntoView?.({ behavior: "smooth" });
    },
    alarmClearCallBack(data) {
      this.isBackSingle = data;
    },
    showTime(val) {
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分";
      }
      return time;
    },
    closeAndTurnAround() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    closeAndTurnAroundRead() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toReadRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    position: relative;
    font-size: 20px;
    line-height: 28px;
    font-weight: 700;
  }

  .head-handle-wrap {
    text-align: right;

    .more-dropdown {
      padding: 0;

      @include themify() {
        border: none;
        border-left: 1px solid themed("$--button-default-border-color");
      }
    }

    .btnleft__group {
      margin-left: 5px;
      margin-bottom: 0px;
      margin-top: 10px;
    }
  }

  .divider {
    margin: 12px 0 16px;
  }

  .head-info2 {
    p {
      margin: 0 auto 5px;
    }
  }

  .head-sheetId {
    font-size: 20px;
    line-height: 28px;
  }

  .head-up-down {
    text-align: center;

    & > div:first-child {
      line-height: 20px;

      @include themify() {
        color: themed("$--color-text-regular");
      }
    }

    & > div:last-child {
      font-weight: 400;
      font-size: 18px;
      line-height: 28px;
      white-space: nowrap;
      &.text-primary {
        color: #c43c43;
      }
    }
  }

  .card-title {
    font-weight: 400;
    font-size: 15px;
    box-shadow: 0 0 5px #aaa;
    padding-left: 10px;
  }
}
</style>
