<template>
  <head-fixed-layout class="wireless-create-order">
    <template #header>
      <el-row :gutter="20">
        <el-col
          :xs="24"
          :sm="15"
          :md="18"
          class="head-title"
          v-if="woId == null"
          >无线网故障工单拟稿</el-col
        >
        <el-col
          :xs="24"
          :sm="15"
          :md="18"
          class="head-title"
          v-if="woId != null"
          >无线网故障工单草稿</el-col
        >

        <el-col :xs="24" :sm="9" :md="6" class="head-handle-wrap">
          <el-button
            type="primary"
            @click="onSheetDel"
            v-loading.fullscreen.lock="sheetCgDelLoading"
            v-if="woId != null"
            >删除</el-button
          >

          <el-button
            type="primary"
            @click="onSheetSave"
            v-loading.fullscreen.lock="sheetSaveLoading"
            >保存</el-button
          >
          <el-button
            type="primary"
            @click="onSheetSubmit"
            v-loading.fullscreen.lock="sheetCommitLoading"
            >提交</el-button
          >
        </el-col>
      </el-row>
    </template>

    <el-form
      :model="sheetForm"
      ref="sheetForm"
      :rules="sheetFormRules"
      label-width="130px"
      label-position="right"
    >
      <el-card shadow="always" :body-style="{ padding: '10px' }">
        <div slot="header" class="card-title">
          <span>工单基本信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人:" prop="senderName">
              <el-input
                v-model="sheetForm.senderName"
                placeholder="请输入建人"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人部门:" prop="senderDeptName">
              <el-input
                v-model="sheetForm.senderDeptName"
                placeholder="请输入建单部门"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单时间:" prop="sheetCreateTime">
              <el-input v-model="sheetForm.sheetCreateTime" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="16" :md="16" :offset="0">
            <el-form-item label="工单主题:" prop="sheetTitle">
              <el-input
                v-model="sheetForm.sheetTitle"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="400"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="归属地市:" prop="regionName" required>
              <el-select
                style="width: 100%"
                placeholder="请选择归属地市"
                v-model="sheetForm.regionName"
                filterable
              >
                <el-option
                  v-for="(item, i) in faultRegionOptions"
                  :key="i"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="工单来源:" required>
              <dict-select
                :value.sync="sheetForm.createType"
                :dictId="10003"
                placeholder="请选择内容"
                :notSelect="true"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="发生时间:" prop="alarmCreateTime">
              <el-date-picker
                v-model="sheetForm.alarmCreateTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="紧急程度:" prop="emergencyLevel" required>
              <dict-select
                :value.sync="sheetForm.emergencyLevel"
                :dictId="10001"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="工单优先级:" prop="faultLevel" required>
              <dict-select
                :value.sync="sheetForm.faultLevel"
                :dictId="81001"
                placeholder="请选择"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item
              label="预估处理时限:"
              prop="processTimeLimit"
              required
            >
              <el-input
                v-model="sheetForm.processTimeLimit"
                placeholder="请输入预估处理时限"
                clearable
                style="width: 100%"
                maxlength="11"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item
              label="受理时限(分钟):"
              prop="acceptTimeLimit"
              required
            >
              <el-input
                v-model="sheetForm.acceptTimeLimit"
                placeholder="请输入受理时限"
                clearable
                style="width: 100%"
                maxlength="11"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属专业:" prop="professionalType" required>
              <dict-select
                :value.sync="sheetForm.professionalType"
                :dictId="10002"
                style="width: 100%"
                :notSelect="true"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="网络类型:" prop="networkType">
              <dict-select
                :value.sync="sheetForm.networkType"
                :dictId="70001"
                placeholder="请选择内容"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="业务中断:" prop="businessDown">
              <el-radio-group v-model="sheetForm.businessDown">
                <el-radio :label="'是'">是</el-radio>
                <el-radio :label="'否'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>


          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警地市:" prop="regionCode" required>
              <el-select
                style="width: 100%"
                placeholder="请选择告警地市"
                v-model="sheetForm.regionCode"
                filterable
                @change="getFaultAreaCommonCounty"
              >
                <el-option
                  v-for="(item, i) in faultRegionOptions"
                  :key="i"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警区县:" prop="cityCode" required>
              <el-select
                style="width: 100%"
                placeholder="请选择告警区县"
                v-model="sheetForm.cityCode"
                filterable
              >
                <el-option
                  v-for="(item, i) in faultAreaCommonCounty"
                  :key="i"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="覆盖场景:" prop="coverScene">
              <dict-select
                :value.sync="sheetForm.coverScene"
                :dictId="81002"
                placeholder="请选择"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <!-- 电联新增 - 开始 -->
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="故障处理方:" :prop="FIELD_NAMES.faultHandler" required>
              <el-select
                v-model="sheetForm[FIELD_NAMES.faultHandler]"
                @change="onFaultHandlerChange"
                style="width: 100%"
                placeholder="请选择故障处理方"
              >
                <el-option
                  v-for="option in faultHandlerOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :xs="24"
            :sm="8"
            :md="8"
            :offset="0"
            v-if="shouldShowSharedConstruction"
          >
            <el-form-item
              label="是否共建共享:"
              :prop="FIELD_NAMES.isSharedConstruction"
              required
            >
              <el-radio-group v-model="sheetForm[FIELD_NAMES.isSharedConstruction]">
                <el-radio
                  v-for="option in sharedConstructionOptions"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- 电联新增 - 截止 -->
          <el-col :xs="24" :sm="23" :md="24" :offset="0">
            <el-form-item label="故障现象:" prop="faultPhenomenon" required>
              <el-input
                v-model="sheetForm.faultPhenomenon"
                placeholder="请输入内容"
                type="textarea"
                :rows="2"
                clearable
                style="width: 100%"
                show-word-limit
                maxlength="3000"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" :offset="0">
            <el-form-item label="备注:" prop="falutComment">
              <el-input
                v-model="sheetForm.falutComment"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
      >
        <div slot="header" class="card-title">
          <span>专业故障信息</span>
        </div>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="基站名称:" prop="siteName" required>
              <el-input
                v-model="sheetForm.siteName"
                placeholder="请输入内容"
                style="width: 100%"
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="基站地址:" prop="siteAddress">
              <el-input
                v-model="sheetForm.siteAddress"
                placeholder="请输入内容"
                style="width: 100%"
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="基站级别:" prop="siteLevel" required>
              <el-input
                v-model="sheetForm.siteLevel"
                placeholder="请输入内容"
                style="width: 100%"
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="基站编号:" prop="siteCode">
              <el-input
                v-model="sheetForm.siteCode"
                placeholder="请输入内容"
                style="width: 100%"
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="规模退服:" prop="scaleWithdrawal">
              <el-input
                v-model="sheetForm.scaleWithdrawal"
                placeholder="请输入内容"
                style="width: 100%"
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>

          <!-- 电联新增 - 开始 -->
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="基站类型:" :prop="FIELD_NAMES.baseStationType">
              <el-select
                v-model="sheetForm[FIELD_NAMES.baseStationType]"
                style="width: 100%"
                placeholder="请选择基站类型"
                clearable
              >
                <el-option
                  v-for="option in baseStationTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="站址名称:" :prop="FIELD_NAMES.siteNameNew">
              <el-input
                v-model="sheetForm[FIELD_NAMES.siteNameNew]"
                placeholder="请输入站址名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="站址经纬度:" :prop="FIELD_NAMES.siteCoordinates">
              <el-input
                v-model="sheetForm[FIELD_NAMES.siteCoordinates]"
                placeholder="请输入站址经纬度"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="站址类别:" :prop="FIELD_NAMES.siteCategory">
              <el-select
                v-model="sheetForm[FIELD_NAMES.siteCategory]"
                style="width: 100%"
                placeholder="请选择站址类别"
                clearable
              >
                <el-option
                  v-for="option in siteCategoryOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属区域:" :prop="FIELD_NAMES.belongingArea">
              <el-input
                v-model="sheetForm[FIELD_NAMES.belongingArea]"
                placeholder="请输入所属区域"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属场景:" :prop="FIELD_NAMES.belongingScene">
              <el-select
                v-model="sheetForm[FIELD_NAMES.belongingScene]"
                style="width: 100%"
                placeholder="请选择所属场景"
                clearable
              >
                <el-option
                  v-for="option in belongingSceneOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 电联新增 - 截止 -->
        </el-row>
      </el-card>

      <!-- 共建共享告警信息（电信模式） -->
      <el-card
        v-if="shouldShowTelecomAlarmInfo"
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
      >
        <div slot="header" class="card-title">
          <span>共建共享告警信息</span>
        </div>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警ID:" :prop="FIELD_NAMES.alarmId" required>
              <el-input
                v-model="sheetForm[FIELD_NAMES.alarmId]"
                placeholder="请输入告警ID"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="发生时间:" :prop="FIELD_NAMES.alarmOccurTime" required>
              <el-date-picker
                v-model="sheetForm[FIELD_NAMES.alarmOccurTime]"
                type="datetime"
                placeholder="请选择发生时间"
                style="width: 100%"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警名称:" :prop="FIELD_NAMES.alarmName" required>
              <el-input
                v-model="sheetForm[FIELD_NAMES.alarmName]"
                placeholder="请输入告警名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警类别:" :prop="FIELD_NAMES.alarmType">
              <el-input
                v-model="sheetForm[FIELD_NAMES.alarmType]"
                placeholder="请输入告警类别"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警地市:" :prop="FIELD_NAMES.alarmCityCode" required>
              <el-input
                v-model="sheetForm[FIELD_NAMES.alarmCityCode]"
                placeholder="请输入告警地市"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警区县:" :prop="FIELD_NAMES.alarmRegionCode" required>
              <el-input
                v-model="sheetForm[FIELD_NAMES.alarmRegionCode]"
                placeholder="请输入告警区县"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警详情:" :prop="FIELD_NAMES.alarmDetail" required>
              <el-input
                v-model="sheetForm[FIELD_NAMES.alarmDetail]"
                placeholder="请输入告警详情"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警级别:" :prop="FIELD_NAMES.alarmLevel" required>
              <el-select
                v-model="sheetForm[FIELD_NAMES.alarmLevel]"
                style="width: 100%"
                placeholder="请选择告警级别"
                clearable
              >
                <el-option
                  v-for="option in alarmLevelOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警定位:" :prop="FIELD_NAMES.alarmLocation" required>
              <el-input
                v-model="sheetForm[FIELD_NAMES.alarmLocation]"
                placeholder="请输入告警定位"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="故障类别:" :prop="FIELD_NAMES.faultCategory" required>
              <el-select
                v-model="sheetForm[FIELD_NAMES.faultCategory]"
                style="width: 100%"
                placeholder="请选择故障类别"
                clearable
              >
                <el-option
                  v-for="option in faultCategoryOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="设备厂家:" :prop="FIELD_NAMES.deviceManufacturer" required>
              <el-input
                v-model="sheetForm[FIELD_NAMES.deviceManufacturer]"
                placeholder="请输入设备厂家"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="设备类型:" :prop="FIELD_NAMES.deviceType" required>
              <el-input
                v-model="sheetForm[FIELD_NAMES.deviceType]"
                placeholder="请输入设备类型"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
      >
        <div slot="header" class="card-title">
          <span>工单派送信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="主送人:" prop="builderZs" required>
              <el-input v-model="sheetForm.builderZs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderZsList">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderZs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderZs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'org'"
                  >
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.builderZsList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.builderZsName"
                    placeholder="请输入主送人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderZs')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('builderZs')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.builderZsListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('builderZs', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('builderZs')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 3px">
                    +{{ organizeForm.builderZsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('lordSentDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="派单模式:" prop="seizeOrders" required>
              <el-radio-group v-model="sheetForm.seizeOrders">
                <el-radio :label="1">抢单受理</el-radio>
                <el-radio :label="0">均可受理</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0" v-if="shouldShowSecondSender">
            <el-form-item label="次送人:" prop="builderAcceptMan">
              <el-input
                v-model="sheetForm.builderAccept"
                placeholder="添加人员"
              >
                <template
                  v-for="(tag, index) in organizeForm.builderAcceptList"
                >
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderAccept', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderAccept', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'org'"
                  >
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.builderAcceptList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.builderAcceptName"
                    placeholder="请输入主送人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderAccept')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('builderAccept')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.builderAcceptListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('builderAccept', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('builderAccept')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 3px">
                    +{{ organizeForm.builderAcceptList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('secondDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="抄送人:" prop="copyMan">
              <el-input v-model="sheetForm.builderCs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderCsList">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderCs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderCs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'org'"
                  >
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.builderCsList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.builderCsName"
                    placeholder="请输入抄送人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderCs')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('builderCs')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.builderCsListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('builderCs', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('builderCs')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ organizeForm.builderCsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('ccDetermine')"
                >
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="短信通知:" prop="isSendSms">
              <el-radio-group v-model="sheetForm.isSendSms">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            :xs="24"
            :sm="8"
            :md="8"
            :offset="0"
            v-if="sheetForm.isSendSms == '1'"
          >
            <el-form-item
              label="接收人:"
              prop="recipient"
              :rules="[
                {
                  required: sheetForm.isSendSms == '1' ? true : false,
                  message: '请选择接收人',
                },
              ]"
            >
              <el-input v-model="sheetForm.recipient" placeholder="添加人员">
                <el-tag
                  slot="prefix"
                  style="margin-top: 5px"
                  v-for="(tag, index) in organizeForm.recipientList"
                  :key="index"
                  closable
                  @close="handleClose('recipient', tag)"
                  v-show="index < 1"
                >
                  {{ tag.name }}
                </el-tag>

                <el-popover
                  slot="prefix"
                  v-if="organizeForm.recipientList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.recipientName"
                    placeholder="请输入接收人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('recipient')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('recipient')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.recipientListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('recipient', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('recipient')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ organizeForm.recipientList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('recipientDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :offset="0" v-if="sheetForm.isSendSms == '1'">
            <el-form-item label="发送内容:" prop="sendContent">
              <el-input
                v-model="sheetForm.sendContent"
                placeholder="请输入内容"
                type="textarea"
                :rows="4"
                clearable
                style="width: 100%"
                maxlength="1000"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>

    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      :showContactUserTab="diaPeople.showContactUserTab"
      :multiple-select-enable="diaPeople.multipleSelectEnable"
      :single-select-tip="diaPeople.singleSelectTip"
      :professionalType="mainProfessionalType"
      @on-save="onSavePeople"
      :appendToBody="true"
      :showContactOrgTab="diaPeople.showContactOrgTab"
      :userAttribution="userAttribution"
    />
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "./../workOrder/components/HeadFixedLayout.vue";
import { apiGetOrgInfo, getCurrentTime } from "./../workOrder/api/CommonApi";
import {
  apiInitOrderDraf,
  apiArea,
  apiDeleteDraft,
} from "./workOrderWaitDetail/api/CommonApi";
import { apiWirelessBuild } from "./../workOrder/api/WorkOrderDraftEdit";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";
import DictSelect from "../workOrder/components/DictSelect.vue";
import { mixin } from "../../../../mixins";
import {
  FIELD_NAMES,
  FAULT_HANDLER_OPTIONS,
  SHARED_CONSTRUCTION_OPTIONS,
  BASE_STATION_TYPE_OPTIONS,
  SITE_CATEGORY_OPTIONS,
  BELONGING_SCENE_OPTIONS,
  ALARM_LEVEL_OPTIONS,
  FAULT_CATEGORY_OPTIONS
} from "./config/additionalFields";
export default {
  name: "WirelessWorkOrderDraftEdit",
  components: {
    HeadFixedLayout,
    DiaTissueTree,
    DictSelect,
  },
  mixins: [mixin],

  data() {
    var validHappenTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择发生时间"));
      } else {
        let seconds = moment(
          this.sheetForm.sheetCreateTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.sheetForm.happenTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0) {
          callback(new Error("“发生时间”不能晚于“建单时间”，请重新选择"));
        } else {
          callback();
        }
      }
    };
    return {
      userAttribution: "ProvinceWirelessUser",
      sheetForm: {
        sender: "", //建单人的登录名
        senderName: "", //建单人真实姓名
        senderDeptName: "", // 创建人部门
        senderDept: null, //建单人部门ID
        sheetCreateTime: getCurrentTime(Date.now()), // 建单时间

        sheetTitle: "", //工单主题
        createType: "", //工单来源
        alarmCreateTime: "", //发生时间
        regionCode: "", //告警地市
        cityCode: "", //告警区县
        emergencyLevel: "", //紧急程度
        acceptTimeLimit: "30", // 受理时限
        processTimeLimit: "", //预估处理时限
        professionalType: "7", //专业
        networkType: "", //网络类型
        businessDown: "", //业务中断
        faultLevel: "6", //工单优先级
        faultPhenomenon: "", //故障现象
        falutComment: "", //备注

        siteName: "", //基站名称
        siteAddress: "", //基站地址
        siteLevel: "", //基站级别
        siteCode: "", //基站编号
        scaleWithdrawal: "", //规模退服

        builderZs: "", //主送
        builderZsUserId: "", //主送人ID
        builderZsOrgId: "", //主送组织ID
        builderZsUserName: "", //主送人名称
        builderZsOrgName: "", //主送组织名称

        builderAccept: "", //次送
        builderAcceptUserId: "", //次送人ID
        builderAcceptOrgId: "", //次送组织ID
        builderAcceptUserName: "", //次送人名称
        builderAcceptOrgName: "", //次送组织名称

        builderCs: "", //抄送
        builderCsUserId: "", //抄送人ID
        builderCsOrgId: "", //抄送组织ID
        builderCsUserName: "", //抄送人名称
        builderCsOrgName: "", //抄送组织名称
        recipient: "", //短信接收人
        recipientUserName: "", //短信接收人名称
        recipientUserId: "", //短信接收人ID

        agentManDetail: "",
        copyManDetail: "",
        recipientDetailUserName: "",

        isSendSms: 0,
        seizeOrders: 0,
        sendContent:
          "1、光缆：**， ××××光缆在××省××（网元）与××（网元）之间中断，影响××××波分系统，承载的SDH系统保护倒换，已经派单给省公司，并电话通知处理，现在该省正在利用备用纤芯倒接;(该段落OLP正常切换;)(分公司正在协调处理;)2、设备：××， ××××系统中断，初步怀疑是××网元××板卡问题，承载的业务目前不受影响，已经派单给省公司，并电话通知处理，现在分公司正在利用备用波道倒接；(该系统承载的SDH系统正常倒换，业务不受影响；)(该系统承载的SDH系统倒换不正常，已经影响业务；)(影响的业务有（无）××重保电路。)",
        coverScene: "",

        // 电联新增字段
        [FIELD_NAMES.faultHandler]: "unicom", // 故障处理方，默认联通
        [FIELD_NAMES.isSharedConstruction]: "yes", // 是否共建共享，默认为是

        // 新增字段
        [FIELD_NAMES.baseStationType]: "", // 基站类型
        [FIELD_NAMES.siteNameNew]: "", // 站址名称（新增字段）
        [FIELD_NAMES.siteCoordinates]: "", // 站址经纬度
        [FIELD_NAMES.siteCategory]: "", // 站址类别
        [FIELD_NAMES.belongingArea]: "", // 所属区域
        [FIELD_NAMES.belongingScene]: "", // 所属场景

        // 电信模式新增字段 - 共建共享告警信息
        [FIELD_NAMES.alarmId]: "", // 告警ID
        [FIELD_NAMES.alarmOccurTime]: "", // 发生时间
        [FIELD_NAMES.alarmName]: "", // 告警名称
        [FIELD_NAMES.alarmType]: "", // 告警类别
        [FIELD_NAMES.alarmCityCode]: "", // 告警地市
        [FIELD_NAMES.alarmRegionCode]: "", // 告警区县
        [FIELD_NAMES.alarmDetail]: "", // 告警详情
        [FIELD_NAMES.alarmLevel]: "", // 告警级别
        [FIELD_NAMES.alarmLocation]: "", // 告警定位
        [FIELD_NAMES.faultCategory]: "", // 故障类别
        [FIELD_NAMES.deviceManufacturer]: "", // 设备厂家
        [FIELD_NAMES.deviceType]: "", // 设备类型
      },
      organizeForm: {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",

        builderAcceptList: [],
        builderAcceptListCopy: [],
        builderAcceptName: "",

        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",

        recipientList: [],
        recipientListCopy: [],
        recipientName: "",
      },

      sheetFormRules: {
        sheetTitle: [
          { required: true, message: "工单主题不能为空" },
          {
            validator: this.checkLength,
            max: 400,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        createType: [{ required: true, message: "请选择工单来源" }],
        alarmCreateTime: [{ validator: validHappenTime, required: true }],
        emergencyLevel: [{ required: true, message: "请选择紧急程度" }],
        acceptTimeLimit: [
          { required: true, message: "请输入受理时限" },
          {
            validator: this.checkLength,
            max: 11,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        falutComment: [
          {
            validator: this.checkLength,
            max: 255,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],

        siteName: [
          {
            validator: this.checkLength,
            max: 255,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        siteAddress: [
          {
            validator: this.checkLength,
            max: 255,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        siteLevel: [
          {
            validator: this.checkLength,
            max: 255,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        siteCode: [
          {
            validator: this.checkLength,
            max: 255,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        scaleWithdrawal: [
          {
            validator: this.checkLength,
            max: 255,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        sendContent: [
          {
            required: true,
            message: "请输入发送内容",
            trigger: "blur",
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],

        professionalType: [{ required: true, message: "请选择所属专业" }],
        businessDown: [{ required: true, message: "请选择业务是否中断" }],
        faultPhenomenon: [
          { required: true, message: "请输入故障现象" },
          {
            validator: this.checkLength,
            max: 3000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        builderZs: [{ required: true, message: "主送人不能为空" }],
        processTimeLimit: [
          { required: true, message: "请输入预估处理时限" },
          {
            validator: this.checkLength,
            max: 11,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        regionCode: [{ required: true, message: "请选择告警地市" }],
        cityCode: [{ required: true, message: "请选择告警区县" }],
        faultLevel: [{ required: true, message: "请选择工单优先级" }],

        // 电联新增字段验证规则
        [FIELD_NAMES.faultHandler]: [{ required: true, message: "请选择故障处理方", trigger: "change" }],
        [FIELD_NAMES.isSharedConstruction]: [{ required: true, message: "请选择是否共建共享", trigger: "change" }],

        // 基站名称和基站级别必填验证
        siteName: [{ required: true, message: "请输入基站名称", trigger: "blur" }],
        siteLevel: [{ required: true, message: "请选择基站级别", trigger: "change" }],

        // 电信模式必填字段验证规则
        [FIELD_NAMES.alarmId]: [{ required: true, message: "请输入告警ID", trigger: "blur" }],
        [FIELD_NAMES.alarmOccurTime]: [{ required: true, message: "请选择发生时间", trigger: "change" }],
        [FIELD_NAMES.alarmName]: [{ required: true, message: "请输入告警名称", trigger: "blur" }],
        [FIELD_NAMES.alarmCityCode]: [{ required: true, message: "请输入告警地市", trigger: "blur" }],
        [FIELD_NAMES.alarmRegionCode]: [{ required: true, message: "请输入告警区县", trigger: "blur" }],
        [FIELD_NAMES.alarmDetail]: [{ required: true, message: "请输入告警详情", trigger: "blur" }],
        [FIELD_NAMES.alarmLevel]: [{ required: true, message: "请选择告警级别", trigger: "change" }],
        [FIELD_NAMES.alarmLocation]: [{ required: true, message: "请输入告警定位", trigger: "blur" }],
        [FIELD_NAMES.faultCategory]: [{ required: true, message: "请选择故障类别", trigger: "change" }],
        [FIELD_NAMES.deviceManufacturer]: [{ required: true, message: "请输入设备厂家", trigger: "blur" }],
        [FIELD_NAMES.deviceType]: [{ required: true, message: "请输入设备类型", trigger: "blur" }],
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "建单人主送",
          secondDetermine: "建单人次送",
          ccDetermine: "建单人抄送",
          recipientDetermine: "接收人选择",
        },
        multipleSelectEnable: true,
        singleSelectTip: "",
        showOrgsTree: true,
        showOrgsTreeMap: {
          recipientDetermine: false,
        },
        showContactUserTab: false,
        showContactUserTabMap: {
          lordSentDetermine: true,
          secondDetermine: true,
          ccDetermine: true,
          recipientDetermine: true,
        },
        showContactOrgTab: false,
        showContactOrgTabMap: {
          lordSentDetermine: true,
          secondDetermine: true,
          ccDetermine: true,
        },
      },
      attachmentDialogVisible: false,
      sheetCommitLoading: false,
      sheetSaveLoading: false,
      incidenceDialogVisible: false,
      sheetCgDelLoading: false,
      orgInfoData: {}, //组织信息数据缓存
      route: null,
      userInfoData: {},
      areaCode: "",
      category: "",
      faultRegionOptions: [],
      faultAreaCommonCounty: [],
      sheetNo: null,
      woId: null,
      processInstId: null,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      isDiaOrgsUserTree: false,

      // 电联新增字段选项
      FIELD_NAMES,
      faultHandlerOptions: FAULT_HANDLER_OPTIONS,
      sharedConstructionOptions: SHARED_CONSTRUCTION_OPTIONS,
      baseStationTypeOptions: BASE_STATION_TYPE_OPTIONS,
      siteCategoryOptions: SITE_CATEGORY_OPTIONS,
      belongingSceneOptions: BELONGING_SCENE_OPTIONS,
      alarmLevelOptions: ALARM_LEVEL_OPTIONS,
      faultCategoryOptions: FAULT_CATEGORY_OPTIONS,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
    mainProfessionalType() {
      return this.$route.query.mainProfessionalType;
    },
    // 是否显示共建共享字段
    shouldShowSharedConstruction() {
      return this.sheetForm[FIELD_NAMES.faultHandler] === 'unicom';
    },
    // 是否显示次送人字段
    shouldShowSecondSender() {
      return this.sheetForm[FIELD_NAMES.faultHandler] === 'unicom';
    },
    // 是否显示电信模式的共建共享告警信息
    shouldShowTelecomAlarmInfo() {
      return this.sheetForm[FIELD_NAMES.faultHandler] === 'telecom';
    },
  },
  watch: {
    "sheetForm.emergencyLevel": {
      handler: function (val) {
        if (val == "3") {
          this.sheetForm.processTimeLimit = "8";
        } else {
          this.sheetForm.processTimeLimit = "72";
        }
      },
    },
    // "sheetForm.alarmCity": {
    //   handler: function (val) {
    //     this.getFaultAreaCommonCounty(val);
    //   },
    // },
    "organizeForm.builderZsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.builderZs = "已选";
        } else {
          this.sheetForm.builderZs = "";
        }

        let userList = newV.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = newV.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList.length == 1) {
          this.sheetForm.seizeOrders = 0;
        }
        if (orgList.length == 1) {
          this.sheetForm.seizeOrders = 0;
        }
        // if (newV.length > 1) {
        //   this.sheetForm.seizeOrders = 1;
        // } else {
        //   this.sheetForm.seizeOrders = null;
        // }
      },
      deep: true,
    },
    "organizeForm.recipientList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.recipient = "已选";
        } else {
          this.sheetForm.recipient = "";
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.sheetForm.createType = "2"; // 电子运维新建
    this.sheetForm.professionalType = "7"; //专业类型
    this.userInfoData = JSON.parse(this.userInfo.attr2);
    this.sheetForm.sheetCreateTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.sheetForm.happenTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    // 初始化 信息来源
    this.getOrgInfo();
    if (this.$route.query?.orderType == "caogao") {
      this.woId = this.$route.query.woId;
      this.initOrder();
    }
    window["__process_response_from_getDevice"] = value => {
      let objStr = "";
      let result = JSON.parse(value);
      let objArray = JSON.parse(result?.system);
      objArray.forEach(obj => {
        objStr += obj.sysName + ",";
      });
      if (objStr) {
        objStr = objStr.substr(0, objStr.length - 1);
      }

      this.sheetForm.title += this.sheetForm.title ? "," + objStr : objStr;
    };
  },
  methods: {
    // 故障处理方变化处理
    onFaultHandlerChange(value) {
      // 当故障处理方改变时的处理逻辑
      if (value === 'unicom') {
        // 选择联通时，设置共建共享默认为"是"
        this.sheetForm[FIELD_NAMES.isSharedConstruction] = 'yes';
      } else {
        // 选择电信时，清空共建共享字段
        this.sheetForm[FIELD_NAMES.isSharedConstruction] = '';
      }
    },

    // 回显
    initOrder() {
      // url 中 获取 sheetNo 参数
      const url = window.location.href;
      const queryStri = url.substring(url.indexOf("?") + 1).split("&");
      let sheetNoUrl = "";
      queryStri.forEach(item => {
        if (item.indexOf("sheetNo") >= 0) {
          sheetNoUrl = decodeURI(item.split("=")[1]);
        }
      });
      let params = {
        woId: this.$route.query.woId,
        sheetNo: this.$route.query.sheetNo || sheetNoUrl,
      };
      apiInitOrderDraf(params)
        .then(async res => {
          if (res.status == "0") {
            let self = this;
            let resData = Object.assign(
              res.data.sheetInfo,
              res.data.sheetInfoWireless
            );
            self.sheetForm = resData;
            self.sheetForm.professionalType = String(resData.professionalType);
            self.sheetForm.createType = String(resData.createType);
            self.sheetForm.emergencyLevel = String(resData.emergencyLevel);
            self.sheetForm.agentManDetail = resData.agentManDetail;
            this.sheetForm.builderZsOrgId = resData.agentDeptCode;
            this.sheetForm.builderZsUserId = resData.agentManId;
            this.sheetForm.builderZsUserName = resData.agentMan;
            this.sheetForm.builderZsOrgName = resData.agentDeptName;
            this.sheetForm.copyManDetail = resData.copyManDetail;
            this.sheetForm.builderCsUserId = resData.copyManId;
            this.sheetForm.builderCsOrgId = resData.copyDeptCode;
            this.sheetForm.builderCsUserName = resData.copyMan;
            this.sheetForm.builderCsOrgName = resData.copyDeptName;
            (this.sheetForm.builderAcceptUserId = resData.acceptManId),
              (this.sheetForm.builderAcceptOrgId = resData.acceptDeptCode),
              (this.sheetForm.builderAcceptUserName = resData.acceptMan),
              (this.sheetForm.builderAcceptOrgName = resData.acceptDeptName),
              (this.sheetForm.recipientUserName = resData.smsToUsername);
            this.sheetForm.recipientUserId = resData.smsToUserid;

            // 电联新增字段回显处理
            if (resData[FIELD_NAMES.faultHandler]) {
              this.sheetForm[FIELD_NAMES.faultHandler] = resData[FIELD_NAMES.faultHandler];
            }
            if (resData[FIELD_NAMES.isSharedConstruction] !== undefined) {
              this.sheetForm[FIELD_NAMES.isSharedConstruction] = resData[FIELD_NAMES.isSharedConstruction];
            }

            // 新增字段回显处理
            if (resData[FIELD_NAMES.baseStationType]) {
              this.sheetForm[FIELD_NAMES.baseStationType] = resData[FIELD_NAMES.baseStationType];
            }
            if (resData[FIELD_NAMES.siteNameNew]) {
              this.sheetForm[FIELD_NAMES.siteNameNew] = resData[FIELD_NAMES.siteNameNew];
            }
            if (resData[FIELD_NAMES.siteCoordinates]) {
              this.sheetForm[FIELD_NAMES.siteCoordinates] = resData[FIELD_NAMES.siteCoordinates];
            }
            if (resData[FIELD_NAMES.siteCategory]) {
              this.sheetForm[FIELD_NAMES.siteCategory] = resData[FIELD_NAMES.siteCategory];
            }
            if (resData[FIELD_NAMES.belongingArea]) {
              this.sheetForm[FIELD_NAMES.belongingArea] = resData[FIELD_NAMES.belongingArea];
            }
            if (resData[FIELD_NAMES.belongingScene]) {
              this.sheetForm[FIELD_NAMES.belongingScene] = resData[FIELD_NAMES.belongingScene];
            }

            // 电信模式字段回显处理
            if (resData[FIELD_NAMES.alarmId]) {
              this.sheetForm[FIELD_NAMES.alarmId] = resData[FIELD_NAMES.alarmId];
            }
            if (resData[FIELD_NAMES.alarmOccurTime]) {
              this.sheetForm[FIELD_NAMES.alarmOccurTime] = resData[FIELD_NAMES.alarmOccurTime];
            }
            if (resData[FIELD_NAMES.alarmName]) {
              this.sheetForm[FIELD_NAMES.alarmName] = resData[FIELD_NAMES.alarmName];
            }
            if (resData[FIELD_NAMES.alarmType]) {
              this.sheetForm[FIELD_NAMES.alarmType] = resData[FIELD_NAMES.alarmType];
            }
            if (resData[FIELD_NAMES.alarmCityCode]) {
              this.sheetForm[FIELD_NAMES.alarmCityCode] = resData[FIELD_NAMES.alarmCityCode];
            }
            if (resData[FIELD_NAMES.alarmRegionCode]) {
              this.sheetForm[FIELD_NAMES.alarmRegionCode] = resData[FIELD_NAMES.alarmRegionCode];
            }
            if (resData[FIELD_NAMES.alarmDetail]) {
              this.sheetForm[FIELD_NAMES.alarmDetail] = resData[FIELD_NAMES.alarmDetail];
            }
            if (resData[FIELD_NAMES.alarmLevel]) {
              this.sheetForm[FIELD_NAMES.alarmLevel] = resData[FIELD_NAMES.alarmLevel];
            }
            if (resData[FIELD_NAMES.alarmLocation]) {
              this.sheetForm[FIELD_NAMES.alarmLocation] = resData[FIELD_NAMES.alarmLocation];
            }
            if (resData[FIELD_NAMES.faultCategory]) {
              this.sheetForm[FIELD_NAMES.faultCategory] = resData[FIELD_NAMES.faultCategory];
            }
            if (resData[FIELD_NAMES.deviceManufacturer]) {
              this.sheetForm[FIELD_NAMES.deviceManufacturer] = resData[FIELD_NAMES.deviceManufacturer];
            }
            if (resData[FIELD_NAMES.deviceType]) {
              this.sheetForm[FIELD_NAMES.deviceType] = resData[FIELD_NAMES.deviceType];
            }

            this.woId = resData.woId; //工单id
            this.sheetNo = resData.sheetNo; //工单编号
            this.processInstId = resData.processInstId; //流程实例id
            if (this.sheetForm.sendContent == "") {
              this.sheetForm.sendContent =
                "1、光缆：**， ××××光缆在××省××（网元）与××（网元）之间中断，影响××××波分系统，承载的SDH系统保护倒换，已经派单给省公司，并电话通知处理，现在该省正在利用备用纤芯倒接;(该段落OLP正常切换;)(分公司正在协调处理;)2、设备：××， ××××系统中断，初步怀疑是××网元××板卡问题，承载的业务目前不受影响，已经派单给省公司，并电话通知处理，现在分公司正在利用备用波道倒接；(该系统承载的SDH系统正常倒换，业务不受影响；)(该系统承载的SDH系统倒换不正常，已经影响业务；)(影响的业务有（无）××重保电路。)";
            }
            this.processing();
            let cityCode = resData.cityCode;
            this.getFaultAreaCommonCounty(resData.regionCode).then(() => {
              this.$set(this.sheetForm, "cityCode", cityCode);
            });
            // this.$nextTick(() => {
            //   this.$refs.sheetForm.clearValidate();
            // });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.orgInfoData = res.data;
            this.sheetForm.senderName = res.data.trueName;
            this.sheetForm.senderDeptName = res?.data?.orgInfo?.orgName ?? "";
            this.sheetForm.senderDept = res?.data?.orgInfo?.orgId ?? "";
            this.sheetForm.senderDept = res?.data?.orgInfo?.orgId ?? "";
            this.sheetForm.sender = res?.data?.userName ?? "";

            this.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.category = res?.data?.category ?? "";
            this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    onSheetDel() {
      this.$confirm("是否删除工单草稿？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        this.sheetCgDelLoading = true;
        let param = {
          woIds: Array.isArray(this.woId) ? this.woId : [this.woId],
        };
        apiDeleteDraft(param)
          .then(res => {
            if (res.status == "0") {
              this.$message.success("删除成功");
              this.closeAndTurnAround("backbone_myDraft");
            } else {
              this.$message.error("删除失败");
            }
            this.sheetCgDelLoading = false;
          })
          .catch(error => {
            console.log(error);
            this.sheetCgDelLoading = false;
          });
      });
    },
    closeAndTurnAround() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_myDraft",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },

    //获取告警区县
    getFaultAreaCommonCounty(arr) {
      this.sheetForm.cityCode = "";
      let param = {
        areaCode: arr,
      };
      return new Promise(resolve => {
        apiArea(param)
          .then(res => {
            if (res.status == "0") {
              this.faultAreaCommonCounty = res?.data ?? [];
            }
            resolve("success");
          })
          .catch(error => {
            console.log(error);
          });
      });
    },

    //获取告地市、归属地市
    getFaultAreaOptions() {
      let param = {
        areaCode: this.areaCode,
      };
      apiArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //保存到草稿
    onSheetSave() {
      this.entering();
      this.$confirm("是否保存到草稿？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$refs.sheetForm.validate(valid => {
            if (valid) {
              let formData = new FormData();
              this.sheetSaveLoading = true;
              let self = this;
              let param = {
                sheetInfo: {
                  woId: self.woId, //工单id
                  sheetNo: self.sheetNo, //工单编号
                  processInstId: self.processInstId, //流程实例id
                  sender: self.sheetForm.sender, //建单人
                  senderDeptName: self.sheetForm.senderDeptName, //建单部门
                  senderName: self.sheetForm.senderName, //建单人中文名
                  senderDept: self.sheetForm.senderDept, //建单部门ID
                  sheetCreateTime: self.sheetForm.sheetCreateTime, //建单时间
                  sheetTitle: self.sheetForm.sheetTitle, //工单主题
                  createType: self.sheetForm.createType, //工单来源
                  alarmCreateTime: self.sheetForm.alarmCreateTime, //发生时间
                  regionCode: self.sheetForm.regionCode, //告警地市
                  cityCode: self.sheetForm.cityCode, //告警区县
                  emergencyLevel: self.sheetForm.emergencyLevel, //紧急程度
                  acceptTimeLimit: self.sheetForm.acceptTimeLimit, //受理时限
                  processTimeLimit: self.sheetForm.processTimeLimit, //预估处理时限
                  professionalType: self.sheetForm.professionalType, //所属专业
                  networkType: self.sheetForm.networkType, //网络类型
                  businessDown: self.sheetForm.businessDown, //业务中断
                  faultLevel: self.sheetForm.faultLevel, //工单优先级
                  faultPhenomenon: self.sheetForm.faultPhenomenon, //故障现象
                  falutComment: self.sheetForm.falutComment, //备注

                  siteName: self.sheetForm.siteName, //基站名称
                  siteAddress: self.sheetForm.siteAddress, //基站地址
                  siteLevel: self.sheetForm.siteLevel, //基站级别
                  siteCode: self.sheetForm.siteCode, //基站编号
                  scaleWithdrawal: self.sheetForm.scaleWithdrawal, //规模退服

                  agentDeptCode: self.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                  agentManId: self.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                  agentMan: self.sheetForm.builderZsUserName, //主送人员 中文名称
                  agentDeptName: self.sheetForm.builderZsOrgName, //主送部门 中文名称

                  acceptDeptCode: self.sheetForm.builderAcceptOrgId, //次送是组织 传入组织ID
                  acceptManId: self.sheetForm.builderAcceptUserId, //次送是人员 传入人员ID
                  acceptMan: self.sheetForm.builderAcceptUserName, //次送人员 中文名称
                  acceptDeptName: self.sheetForm.builderAcceptOrgName, //次送部门 中文名称

                  copyDeptCode: self.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                  copyManId: self.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                  copyMan: self.sheetForm.builderCsUserName,
                  copyDeptName: self.sheetForm.builderCsOrgName,

                  isSendSms: self.sheetForm.isSendSms, //是否短信通知
                  smsToUserid: self.sheetForm.recipientUserId,
                  smsToUsername: self.sheetForm.recipientUserName,
                  sendContent:
                    self.sheetForm.isSendSms == "0"
                      ? null
                      : self.sheetForm.sendContent,

                  seizeOrders: self.sheetForm.seizeOrders,
                  sheetStatus: 1, //1是草稿 2是提交到待办

                  agentManDetail: self.sheetForm.agentManDetail,
                  copyManDetail: self.sheetForm.copyManDetail,
                  recipientDetailUserName:
                    self.sheetForm.recipientDetailUserName,
                  coverScene: self.sheetForm.coverScene,

                  // 电联新增字段
                  [FIELD_NAMES.faultHandler]: self.sheetForm[FIELD_NAMES.faultHandler],
                  [FIELD_NAMES.isSharedConstruction]: self.sheetForm[FIELD_NAMES.isSharedConstruction],

                  // 新增字段
                  [FIELD_NAMES.baseStationType]: self.sheetForm[FIELD_NAMES.baseStationType],
                  [FIELD_NAMES.siteNameNew]: self.sheetForm[FIELD_NAMES.siteNameNew],
                  [FIELD_NAMES.siteCoordinates]: self.sheetForm[FIELD_NAMES.siteCoordinates],
                  [FIELD_NAMES.siteCategory]: self.sheetForm[FIELD_NAMES.siteCategory],
                  [FIELD_NAMES.belongingArea]: self.sheetForm[FIELD_NAMES.belongingArea],
                  [FIELD_NAMES.belongingScene]: self.sheetForm[FIELD_NAMES.belongingScene],

                  // 电信模式新增字段
                  [FIELD_NAMES.alarmId]: self.sheetForm[FIELD_NAMES.alarmId],
                  [FIELD_NAMES.alarmOccurTime]: self.sheetForm[FIELD_NAMES.alarmOccurTime],
                  [FIELD_NAMES.alarmName]: self.sheetForm[FIELD_NAMES.alarmName],
                  [FIELD_NAMES.alarmType]: self.sheetForm[FIELD_NAMES.alarmType],
                  [FIELD_NAMES.alarmCityCode]: self.sheetForm[FIELD_NAMES.alarmCityCode],
                  [FIELD_NAMES.alarmRegionCode]: self.sheetForm[FIELD_NAMES.alarmRegionCode],
                  [FIELD_NAMES.alarmDetail]: self.sheetForm[FIELD_NAMES.alarmDetail],
                  [FIELD_NAMES.alarmLevel]: self.sheetForm[FIELD_NAMES.alarmLevel],
                  [FIELD_NAMES.alarmLocation]: self.sheetForm[FIELD_NAMES.alarmLocation],
                  [FIELD_NAMES.faultCategory]: self.sheetForm[FIELD_NAMES.faultCategory],
                  [FIELD_NAMES.deviceManufacturer]: self.sheetForm[FIELD_NAMES.deviceManufacturer],
                  [FIELD_NAMES.deviceType]: self.sheetForm[FIELD_NAMES.deviceType],
                },
              };
              formData.append("jsonParam", JSON.stringify(param));
              this.submitSave(formData);
            } else {
              return false;
            }
          });
        })
        .catch(() => {});
    },
    submitSave(formData) {
      apiWirelessBuild(formData)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("保存成功");
            this.$refs.sheetForm.resetFields();
            this.closeAndTurnAroundMyDraft();
          } else {
            this.$message.error("保存失败");
          }
          this.sheetSaveLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("保存失败");
          this.sheetSaveLoading = false;
        });
    },
    //提交工单
    onSheetSubmit() {
      this.entering();
      this.$confirm("是否提交工单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      }).then(() => {
        this.$refs.sheetForm.validate((valid, a) => {
          if (this.uncheck(a)) {
            let formData = new FormData();
            this.sheetSaveLoading = true;
            let self = this;
            let param = {
              sheetInfo: {
                woId: self.woId, //工单id
                sheetNo: self.sheetNo, //工单编号
                processInstId: self.processInstId, //流程实例id
                sender: self.sheetForm.sender, //建单人
                senderDeptName: self.sheetForm.senderDeptName, //建单部门
                senderName: self.sheetForm.senderName, //建单人中文名
                senderDept: self.sheetForm.senderDept, //建单部门ID
                sheetCreateTime: self.sheetForm.sheetCreateTime, //建单时间
                sheetTitle: self.sheetForm.sheetTitle, //工单主题
                createType: self.sheetForm.createType, //工单来源
                alarmCreateTime: self.sheetForm.alarmCreateTime, //发生时间
                regionCode: self.sheetForm.regionCode, //告警地市
                cityCode: self.sheetForm.cityCode, //告警区县
                emergencyLevel: self.sheetForm.emergencyLevel, //紧急程度
                acceptTimeLimit: self.sheetForm.acceptTimeLimit, //受理时限
                processTimeLimit: self.sheetForm.processTimeLimit, //预估处理时限
                professionalType: self.sheetForm.professionalType, //所属专业
                networkType: self.sheetForm.networkType, //网络类型
                businessDown: self.sheetForm.businessDown, //业务中断
                faultLevel: self.sheetForm.faultLevel, //工单优先级
                faultPhenomenon: self.sheetForm.faultPhenomenon, //故障现象
                falutComment: self.sheetForm.falutComment, //备注

                siteName: self.sheetForm.siteName, //基站名称
                siteAddress: self.sheetForm.siteAddress, //基站地址
                siteLevel: self.sheetForm.siteLevel, //基站级别
                siteCode: self.sheetForm.siteCode, //基站编号
                scaleWithdrawal: self.sheetForm.scaleWithdrawal, //规模退服

                agentDeptCode: self.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                agentManId: self.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                agentMan: self.sheetForm.builderZsUserName, //主送人员 中文名称
                agentDeptName: self.sheetForm.builderZsOrgName, //主送部门 中文名称

                acceptDeptCode: self.sheetForm.builderAcceptOrgId, //次送是组织 传入组织ID
                acceptManId: self.sheetForm.builderAcceptUserId, //次送是人员 传入人员ID
                acceptMan: self.sheetForm.builderAcceptUserName, //次送人员 中文名称
                acceptDeptName: self.sheetForm.builderAcceptOrgName, //次送部门 中文名称

                copyDeptCode: self.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                copyManId: self.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                copyMan: self.sheetForm.builderCsUserName,
                copyDeptName: self.sheetForm.builderCsOrgName,

                isSendSms: self.sheetForm.isSendSms, //是否短信通知
                smsToUserid: self.sheetForm.recipientUserId,
                smsToUsername: self.sheetForm.recipientUserName,
                sendContent:
                  self.sheetForm.isSendSms == "0"
                    ? null
                    : self.sheetForm.sendContent,

                seizeOrders: self.sheetForm.seizeOrders,
                sheetStatus: 2, //1是草稿 2是提交到待办

                agentManDetail: self.sheetForm.agentManDetail,
                copyManDetail: self.sheetForm.copyManDetail,
                recipientDetailUserName: self.sheetForm.recipientDetailUserName,
                coverScene: self.sheetForm.coverScene,

                // 电联新增字段
                [FIELD_NAMES.faultHandler]: self.sheetForm[FIELD_NAMES.faultHandler],
                [FIELD_NAMES.isSharedConstruction]: self.sheetForm[FIELD_NAMES.isSharedConstruction],

                // 新增字段
                [FIELD_NAMES.baseStationType]: self.sheetForm[FIELD_NAMES.baseStationType],
                [FIELD_NAMES.siteNameNew]: self.sheetForm[FIELD_NAMES.siteNameNew],
                [FIELD_NAMES.siteCoordinates]: self.sheetForm[FIELD_NAMES.siteCoordinates],
                [FIELD_NAMES.siteCategory]: self.sheetForm[FIELD_NAMES.siteCategory],
                [FIELD_NAMES.belongingArea]: self.sheetForm[FIELD_NAMES.belongingArea],
                [FIELD_NAMES.belongingScene]: self.sheetForm[FIELD_NAMES.belongingScene],

                // 电信模式新增字段
                [FIELD_NAMES.alarmId]: self.sheetForm[FIELD_NAMES.alarmId],
                [FIELD_NAMES.alarmOccurTime]: self.sheetForm[FIELD_NAMES.alarmOccurTime],
                [FIELD_NAMES.alarmName]: self.sheetForm[FIELD_NAMES.alarmName],
                [FIELD_NAMES.alarmType]: self.sheetForm[FIELD_NAMES.alarmType],
                [FIELD_NAMES.alarmCityCode]: self.sheetForm[FIELD_NAMES.alarmCityCode],
                [FIELD_NAMES.alarmRegionCode]: self.sheetForm[FIELD_NAMES.alarmRegionCode],
                [FIELD_NAMES.alarmDetail]: self.sheetForm[FIELD_NAMES.alarmDetail],
                [FIELD_NAMES.alarmLevel]: self.sheetForm[FIELD_NAMES.alarmLevel],
                [FIELD_NAMES.alarmLocation]: self.sheetForm[FIELD_NAMES.alarmLocation],
                [FIELD_NAMES.faultCategory]: self.sheetForm[FIELD_NAMES.faultCategory],
                [FIELD_NAMES.deviceManufacturer]: self.sheetForm[FIELD_NAMES.deviceManufacturer],
                [FIELD_NAMES.deviceType]: self.sheetForm[FIELD_NAMES.deviceType],
              },
            };
            formData.append("jsonParam", JSON.stringify(param));
            this.submitOrder(formData);
          } else {
            return false;
          }
        });
      });
    },

    submitOrder(formData) {
      apiWirelessBuild(formData)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("工单提交成功");
            this.$refs.sheetForm.resetFields();
            this.closeAndTurnAroundTo();
          } else {
            this.$message.error("工单提交失败");
          }
          this.sheetCommitLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("工单提交失败");
          this.sheetCommitLoading = false;
        });
    },

    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree =
        this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      this.diaPeople.showContactUserTab =
        this.diaPeople.showContactUserTabMap[diaSaveName] ?? false;
      this.diaPeople.showContactOrgTab =
        this.diaPeople.showContactOrgTabMap[diaSaveName] ?? false;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //建单人主送确定
    lordSentDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }
      this.organizeForm.builderZsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderZsList)
      );
    },
    //建单人次送确定
    secondDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let zs = this.organizeForm.builderAcceptList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderAcceptList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let acc = this.organizeForm.builderAcceptList.findIndex(val => {
            return val.id === item.userName;
          });
          if (acc > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderAcceptList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let acc = this.organizeForm.builderAcceptList.findIndex(val => {
            return val.id === item.id;
          });
          if (acc > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderAcceptList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let accOrg = this.organizeForm.builderAcceptList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (accOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderAcceptList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let accOrg = this.organizeForm.builderAcceptList.findIndex(val => {
            return val.id === item.id;
          });
          if (accOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderAcceptList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      this.organizeForm.builderAcceptListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderAcceptList)
      );
    },
    //抄送人确定
    ccDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (csOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (csOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }
      this.organizeForm.builderCsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderCsList)
      );
    },
    //接收人确定
    recipientDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.id;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.organizeForm.recipientListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.recipientList)
      );
    },

    stitchingAlgorithm(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },

    closeAndTurnAroundMyDraft() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.$route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_myDraft",
      });
    },

    closeAndTurnAroundTo() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.$route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },

    // input 清空value
    btnClearable(val) {
      this.sheetForm[val] = "";
    },

    processing() {
      //主送人数据处理
      if (
        this.sheetForm.builderZsUserId != null &&
        this.sheetForm.builderZsUserId != "" &&
        this.sheetForm.agentManDetail != null &&
        this.sheetForm.agentManDetail != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.builderZsUserId.split(",");
        let usersCheckedManDetail = this.sheetForm.agentManDetail.split(",");
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (var i = 0; i < usersCheckedId.length; i++) {
          this.organizeForm.builderZsList.push({
            bz: "user",
            id: usersCheckedId[i],
            name: usersCheckedName[i],
            orgName: usersCheckedOrgName[i],
            mobilePhone: usersCheckedMobilePhone[i],
          });
        }
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      //主送组织数据处理
      if (
        this.sheetForm.builderZsOrgId != null &&
        this.sheetForm.builderZsOrgId != "" &&
        this.sheetForm.builderZsOrgName != null &&
        this.sheetForm.builderZsOrgName != ""
      ) {
        let orgsCheckedName = this.sheetForm.builderZsOrgName.split(",");
        let orgsCheckedId = this.sheetForm.builderZsOrgId.split(",");
        for (var j = 0; j < orgsCheckedId.length; j++) {
          this.organizeForm.builderZsList.push({
            bz: "org",
            id: orgsCheckedId[j],
            orgName: orgsCheckedName[j],
          });
        }
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      //次送人数据处理
      if (
        this.sheetForm.builderAcceptUserId != null &&
        this.sheetForm.builderAcceptUserId != "" &&
        this.sheetForm.builderAcceptUserName != null &&
        this.sheetForm.builderAcceptUserName != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.builderAcceptUserId.split(",");
        let usersCheckedManDetail = this.sheetForm.builderAcceptUserName.split(
          ","
        );
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (var k = 0; k < usersCheckedId.length; k++) {
          this.organizeForm.builderAcceptList.push({
            bz: "user",
            id: usersCheckedId[k],
            name: usersCheckedName[k],
            orgName: usersCheckedOrgName[k],
            mobilePhone: usersCheckedMobilePhone[k],
          });
        }
        this.organizeForm.builderAcceptListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderAcceptList)
        );
      }
      //次送组织数据处理
      if (
        this.sheetForm.builderAcceptOrgId != null &&
        this.sheetForm.builderAcceptOrgId != "" &&
        this.sheetForm.builderAcceptOrgName != null &&
        this.sheetForm.builderAcceptOrgName != ""
      ) {
        let orgsCheckedName = this.sheetForm.builderAcceptOrgName.split(",");
        let orgsCheckedId = this.sheetForm.builderAcceptOrgId.split(",");
        for (var l = 0; l < orgsCheckedId.length; l++) {
          this.organizeForm.builderAcceptList.push({
            bz: "org",
            id: orgsCheckedId[l],
            orgName: orgsCheckedName[l],
          });
        }
        this.organizeForm.builderAcceptListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderAcceptList)
        );
      }

      //抄送人数据处理
      if (
        this.sheetForm.builderCsUserId != null &&
        this.sheetForm.builderCsUserId != "" &&
        this.sheetForm.copyManDetail != null &&
        this.sheetForm.copyManDetail != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.builderCsUserId.split(",");
        let usersCheckedManDetail = this.sheetForm.copyManDetail.split(",");
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (let m = 0; m < usersCheckedId.length; m++) {
          this.organizeForm.builderCsList.push({
            bz: "user",
            id: usersCheckedId[m],
            name: usersCheckedName[m],
            orgName: usersCheckedOrgName[m],
            mobilePhone: usersCheckedMobilePhone[m],
          });
        }
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      //抄送组织数据处理
      if (
        this.sheetForm.builderCsOrgId != null &&
        this.sheetForm.builderCsOrgId != "" &&
        this.sheetForm.builderCsOrgName != null &&
        this.sheetForm.builderCsOrgName != ""
      ) {
        let orgsCheckedName = this.sheetForm.builderCsOrgName.split(",");
        let orgsCheckedId = this.sheetForm.builderCsOrgId.split(",");
        for (let n = 0; n < orgsCheckedId.length; n++) {
          this.organizeForm.builderCsList.push({
            bz: "org",
            id: orgsCheckedId[n],
            orgName: orgsCheckedName[n],
          });
        }
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      //接收人数据处理
      if (
        this.sheetForm.recipientUserId != null &&
        this.sheetForm.recipientUserId != "" &&
        this.sheetForm.recipientDetailUserName != null &&
        this.sheetForm.recipientDetailUserName != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.recipientUserId.split(",");
        let usersCheckedManDetail = this.sheetForm.recipientDetailUserName.split(
          ","
        );
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (var a = 0; a < usersCheckedId.length; a++) {
          this.organizeForm.recipientList.push({
            bz: "user",
            id: usersCheckedId[a],
            name: usersCheckedName[a],
            orgName: usersCheckedOrgName[a],
            mobilePhone: usersCheckedMobilePhone[a],
          });
        }
        this.organizeForm.recipientListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.recipientList)
        );
      }
    },

    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      this.multipleSelection;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "builderZs") {
        this.organizeForm.builderZsList.splice(
          this.arrayIndex(this.organizeForm.builderZsList, tag),
          1
        );
        this.organizeForm.builderZsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderZsListCopy, tag),
          1
        );
      }
      if (val == "builderAccept") {
        this.organizeForm.builderAcceptList.splice(
          this.arrayIndex(this.organizeForm.builderAcceptList, tag),
          1
        );
        this.organizeForm.builderAcceptListCopy.splice(
          this.arrayIndex(this.organizeForm.builderAcceptListCopy, tag),
          1
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsList.splice(
          this.arrayIndex(this.organizeForm.builderCsList, tag),
          1
        );
        this.organizeForm.builderCsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderCsListCopy, tag),
          1
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientList.splice(
          this.arrayIndex(this.organizeForm.recipientList, tag),
          1
        );
        this.organizeForm.recipientListCopy.splice(
          this.arrayIndex(this.organizeForm.recipientListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "builderZs" && this.organizeForm.builderZsList != null) {
        this.organizeForm.builderZsListCopy = [];
        this.organizeForm.builderZsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderZsName ||
            row.orgName == this.organizeForm.builderZsName
          ) {
            this.organizeForm.builderZsListCopy.push(row);
          }
        });
      }
      if (
        val == "builderAccept" &&
        this.organizeForm.builderAcceptList != null
      ) {
        this.organizeForm.builderAcceptListCopy = [];
        this.organizeForm.builderAcceptList.forEach(row => {
          if (
            row.name == this.organizeForm.builderAcceptName ||
            row.orgName == this.organizeForm.builderAcceptName
          ) {
            this.organizeForm.builderAcceptListCopy.push(row);
          }
        });
      }
      if (val == "builderCs" && this.organizeForm.builderCsList != null) {
        this.organizeForm.builderCsListCopy = [];
        this.organizeForm.builderCsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderCsName ||
            row.orgName == this.organizeForm.builderCsName
          ) {
            this.organizeForm.builderCsListCopy.push(row);
          }
        });
      }
      if (val == "recipient" && this.organizeForm.recipientList != null) {
        this.organizeForm.recipientListCopy = [];
        this.organizeForm.recipientList.forEach(row => {
          if (
            row.name == this.organizeForm.recipientName ||
            row.orgName == this.organizeForm.recipientName
          ) {
            this.organizeForm.recipientListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "builderZs") {
        this.organizeForm.builderZsName = "";
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      if (val == "builderAccept") {
        this.organizeForm.builderAcceptName = "";
        this.organizeForm.builderAcceptListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderAcceptList)
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsName = "";
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientName = "";
        this.organizeForm.recipientListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.recipientList)
        );
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    entering() {
      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length > 0
      ) {
        this.sheetForm.builderZs = "1";
        let userList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.builderZsUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.builderZsUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.agentManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.builderZsOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.builderZsOrgId = orgsCheckedId.join(",");
        }
      }
      if (
        this.organizeForm.builderAcceptList &&
        this.organizeForm.builderAcceptList.length > 0
      ) {
        this.sheetForm.builderAccept = "1";
        let userList = this.organizeForm.builderAcceptList.filter(
          (item, index) => {
            return item.bz == "user";
          }
        );
        let orgList = this.organizeForm.builderAcceptList.filter(
          (item, index) => {
            return item.bz == "org";
          }
        );
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.builderAcceptUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.builderAcceptUserId = usersCheckedId.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.builderAcceptOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.builderAcceptOrgId = orgsCheckedId.join(",");
        }
      }
      if (
        this.organizeForm.builderCsList &&
        this.organizeForm.builderCsList.length > 0
      ) {
        let userList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.builderCsUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.builderCsUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.copyManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.builderCsOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.builderCsOrgId = orgsCheckedId.join(",");
        }
      }
      if (
        this.organizeForm.recipientList &&
        this.organizeForm.recipientList.length > 0
      ) {
        let userList = this.organizeForm.recipientList.filter((item, index) => {
          return item.bz == "user";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.recipientUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.recipientUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.recipientDetailUserName = userDetailName.join(",");
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.wireless-create-order {
  .head-title {
    font-size: 24px;
    line-height: 28px;
  }
  .head-handle-wrap {
    text-align: right;
  }
  .card-title {
    font-size: 17px;
    letter-spacing: 0;
    padding-left: 10px;
  }
  .select_style {
    background: #b50b14;
    border-radius: 2px;
    border-color: #b50b14;
  }
}
</style>
