<template>
  <div
    class="layout-grid"
    :class="{
      'layout-fullscreen': fullScreen,
      'ai-agent-active': showAiAgent,
    }"
  >
    <!-- 主要内容区域（左边栏 + 工单详情） -->
    <el-container class="layout-main-area" direction="horizontal">
      <frame-aside
        :width="asideWidth"
        :style="asideStyle"
        v-show="!fullScreen"
      ></frame-aside>
      <el-container
        class="layout-container"
        direction="vertical"
        :style="layoutContainerStyle"
      >
        <div
          ref="header"
          class="frame-header-wrap"
          :class="headerClass"
          :style="headerNavbarStyle"
          v-show="!fullScreen"
        >
          <frame-head-navbar
            v-if="showHeadNavBar"
            :height="headNavbar + 'px'"
          ></frame-head-navbar>
          <tab-view v-if="!hideTabView"></tab-view>
        </div>
        <el-main
          class="frame-container-main"
          :class="{ 'fullscreen-page': routeFullScreen }"
          :style="containerStyle"
        >
          <slot></slot>
          <el-button
            type="primary"
            class="btn-exit-full-screen"
            @click.stop="toggleFullScreen(false)"
            v-if="fullScreen"
          >
            <el-tooltip content="退出全屏"
              ><base-icon
                :icon-class="
                  fullScreen ? 'exit-full-screen' : 'full-screen'
                " /></el-tooltip
          ></el-button>
        </el-main>
      </el-container>
      <!-- 全局智能体控制按钮 -->
      <ai-agent-toggle-button
        v-if="isAiAgentAvailable"
        :show="showAiAgent"
        :loading="false"
        @toggle="toggleGlobalAiAgent"
        :class="{ 'ai-agent-active': showAiAgent }"
        :style="{ zIndex: 2000 }"
      />
    </el-container>

    <!-- 全局智能体区域 -->
    <div v-show="showAiAgent" class="layout-ai-agent">
      <!-- 智能体容器 - 直接显示，不显示loading -->
      <ai-agent-container
        ref="globalAiAgentContainer"
        :config="aiAgentConfig"
        :url="aiAgentUrl"
        @close="closeGlobalAiAgent"
      />
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import FrameAside from "./FrameAside";
import FrameHeadNavbar from "./FrameHeadNavbar";
import TabView from "../TabView/TabView";
import AiAgentContainer from "@/components/AiAgent/AiAgentContainer.vue";
import AiAgentToggleButton from "@/components/AiAgent/AiAgentToggleButton.vue";
import { mapGetters } from "vuex";

export default {
  name: "FrameLayout",
  components: {
    FrameAside,
    FrameHeadNavbar,
    TabView,
    AiAgentContainer,
    AiAgentToggleButton,
  },
  data() {
    return {
      headerHeight: 50,
      headNavbar: 50,
    };
  },
  computed: {
    ...mapGetters([
      "layoutMode",
      "sidebar",
      "fixedHeader",
      "fullScreen",
      "visitedViews",
      "topShowLogo",
      "topShowBreadcrumb",
      "hideTabView",
      "openOnceFullscreen",
      "keepAliveInstance",
      "frameStyle",
      // 智能体相关状态
      "isAiAgentAvailable",
      "showAiAgent",
      "aiAgentLoading",
      "aiAgentError",
      "aiAgentUrl",
      "aiAgentConfig",
    ]),
    asideWidth() {
      return this.sidebar.collapse ? "65px" : "208px";
    },
    asideStyle() {
      return {
        // height: `cal(100vh-${this.height})`,
        position: this.sidebar.fixed ? "fixed" : "",
        overflow: "hidden",
        minHeight: "100vh",
      };
    },
    layoutContainerStyle() {
      return {
        marginLeft: this.fullScreen ? "" : this.asideWidth,
      };
    },
    containerStyle() {
      const height = this.frameStyle.headerHeight - 16;
      return {
        marginTop: this.fullScreen
          ? "0"
          : this.fixedHeader
          ? height + "px"
          : "",
        minHeight: this.fullScreen
          ? ""
          : this.fixedHeader
          ? `calc(100vh - ${height}px)`
          : "",
        padding: this.routeFullScreen ? "0px" : "",
      };
    },
    headerClass() {
      return { "frame-header-fix": this.fixedHeader };
    },
    headerNavbarStyle() {
      return {
        width: this.fixedHeader ? `calc(100% - ${this.asideWidth})` : "",
      };
    },
    routeFullScreen() {
      return this.$route.meta.openTarget === "FullScreen";
    },
    showHeadNavBar() {
      return process.env.NODE_ENV !== "production";
    },
  },
  watch: {
    $route(to) {
      if (
        this.openOnceFullscreen &&
        this.keepAliveInstance["keys"].findIndex(tmp => tmp === to.fullPath) !==
          -1
      ) {
        this.toggleFullScreen(
          to.meta.openTarget === "FullScreen" && this.fullScreen
        );
      } else {
        this.toggleFullScreen(to.meta.openTarget === "FullScreen");
      }
    },
    layoutMode() {
      this.updateStyle();
    },
    fullScreen() {
      this.updateStyle();
    },
  },
  created() {
    this.toggleFullScreen(this.$route.meta.openTarget === "FullScreen");
  },
  mounted() {
    this.$nextTick(() => {
      this.$store.dispatch(
        "keepAlive/updateActionInstance",
        this.$slots.default[0].componentInstance
      );
      this.updateStyle();
      this.updateHeaderHeight();
    });

    // 监听窗口大小变化，重新计算头部高度
    window.addEventListener("resize", this.updateHeaderHeight);

    // 注册全局智能体通信服务
    this.registerGlobalAiAgentService();

    // 设置全局智能体消息监听器
    this.setupGlobalAiAgentMessageListener();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.updateHeaderHeight);

    // 移除全局智能体消息监听器
    if (this.globalAiAgentMessageHandler) {
      window.removeEventListener("message", this.globalAiAgentMessageHandler);
    }

    // 注销全局智能体通信服务
    this.unregisterGlobalAiAgentService();
  },
  methods: {
    toggleFullScreen(fullScreen) {
      this.$store.dispatch("settings/toggleFullScreen", fullScreen);
    },
    updateStyle() {
      this.$nextTick(() => {
        this.$store.commit(
          "settings/UPDATE_FRAMESTYLE",
          window.getComputedStyle(this.$refs.header).height
        );
        // 同时更新智能体面板的头部高度
        this.updateHeaderHeight();
      });
    },

    // ==================== 全局智能体相关方法 ====================

    // 切换全局智能体显示状态
    async toggleGlobalAiAgent() {
      if (!this.isAiAgentAvailable) {
        return;
      }

      if (this.showAiAgent) {
        this.hideGlobalAiAgent();
      } else {
        await this.showGlobalAiAgent();
      }
    },

    // 显示全局智能体
    async showGlobalAiAgent() {
      if (!this.isAiAgentAvailable) {
        return;
      }

      // 通过Vuex action显示智能体
      await this.$store.dispatch("aiAgent/showAiAgent");
    },

    // 隐藏全局智能体
    hideGlobalAiAgent() {
      // 通过Vuex action隐藏智能体
      this.$store.dispatch("aiAgent/hideAiAgent");
    },

    // 关闭全局智能体（从组件内部调用）
    closeGlobalAiAgent() {
      this.hideGlobalAiAgent();
    },

    // 更新头部高度，用于智能体面板定位
    updateHeaderHeight() {
      this.$nextTick(() => {
        const headerElement = this.$refs.header;
        if (headerElement) {
          const headerHeight = headerElement.offsetHeight;
          // 设置CSS变量，供智能体面板使用
          document.documentElement.style.setProperty(
            "--header-height",
            `${headerHeight}px`
          );
          console.log("🎨 头部高度已更新:", headerHeight + "px");
        }
      });
    },

    // ==================== 全局智能体通信方法 ====================

    // 向全局智能体发送消息
    sendMessageToGlobalAiAgent(message) {
      const aiAgentContainer = this.$refs.globalAiAgentContainer;
      console.log("🤖 尝试发送消息给全局智能体:", {
        message,
        hasContainer: !!aiAgentContainer,
        hasPostMessage:
          aiAgentContainer &&
          typeof aiAgentContainer.postMessage === "function",
        showAiAgent: this.showAiAgent,
      });

      if (
        aiAgentContainer &&
        typeof aiAgentContainer.postMessage === "function"
      ) {
        try {
          aiAgentContainer.postMessage(message);
          console.log("🤖 通过全局Layout成功发送消息给智能体:", message);
          return true;
        } catch (error) {
          console.error("🤖 发送消息失败:", error);
          return false;
        }
      } else {
        console.warn("🤖 全局智能体容器未找到或postMessage方法不可用", {
          container: aiAgentContainer,
          postMessageType: aiAgentContainer
            ? typeof aiAgentContainer.postMessage
            : "undefined",
        });
        return false;
      }
    },

    // 获取全局智能体iframe引用
    getGlobalAiAgentIframe() {
      const aiAgentContainer = this.$refs.globalAiAgentContainer;
      if (aiAgentContainer && aiAgentContainer.$el) {
        return aiAgentContainer.$el.querySelector(".ai-agent-iframe");
      }
      return null;
    },

    // 注册全局智能体通信服务
    registerGlobalAiAgentService() {
      // 将通信方法挂载到Vue原型上，供全局访问
      if (!Vue.prototype.$globalAiAgent) {
        const self = this; // 保存this引用
        Vue.prototype.$globalAiAgent = {
          sendMessage: function (message) {
            return self.sendMessageToGlobalAiAgent(message);
          },
          getIframe: function () {
            return self.getGlobalAiAgentIframe();
          },
          isAvailable: function () {
            const isAvailable =
              self.showAiAgent && self.$refs.globalAiAgentContainer;
            console.log("🤖 检查全局智能体可用性:", {
              showAiAgent: self.showAiAgent,
              hasContainer: !!self.$refs.globalAiAgentContainer,
              isAvailable,
            });
            return isAvailable;
          },
        };
        console.log("🤖 全局智能体通信服务已注册");
      }
    },

    // 注销全局智能体通信服务
    unregisterGlobalAiAgentService() {
      if (Vue.prototype.$globalAiAgent) {
        delete Vue.prototype.$globalAiAgent;
        console.log("🤖 全局智能体通信服务已注销");
      }
    },

    // ==================== 全局智能体消息监听 ====================

    // 设置全局智能体消息监听器
    setupGlobalAiAgentMessageListener() {
      // 直接使用箭头函数避免this绑定问题
      this.globalAiAgentMessageHandler = event => {
        this.handleGlobalAiAgentMessage(event);
      };
      window.addEventListener("message", this.globalAiAgentMessageHandler);
      console.log("🤖 [全局Layout] 智能体消息监听器已启动");
    },

    // 处理全局智能体发送的消息
    async handleGlobalAiAgentMessage(event) {
      // 过滤掉开发工具消息
      if (event.data && event.data.source === "react-devtools-content-script")
        return;
      if (event.data && event.data.source === "react-devtools-bridge") return;
      if (event.data && event.data.source === "react-devtools-detector") return;

      // 检查是否是智能体消息
      if (event.data && event.data.action_name) {
        console.log("🤖 [全局Layout] 收到智能体消息:", event.data);

        // 处理智能体动作
        switch (event.data.action_name) {
          case "woNewTab":
            // 处理打开新工单tab的消息
            this.handleWoNewTabMessage(event.data);
            break;

          default:
            // 其他消息不在全局处理，交给具体页面处理
            break;
        }
      }
    },

    // 处理打开新工单tab的消息
    handleWoNewTabMessage(messageData) {
      try {
        const actionData = messageData.action_data;
        if (!actionData || !actionData.woId) {
          console.error(
            "🤖 [全局Layout] woNewTab消息缺少必要参数:",
            messageData
          );
          return;
        }

        console.log("🤖 [全局Layout] 处理打开新工单tab:", actionData);

        // 根据网络类型确定路由名称和参数
        const routeInfo = this.getWorkOrderRouteInfo(actionData);
        if (!routeInfo) {
          console.error("🤖 [全局Layout] 不支持的网络类型:", {
            networkType: actionData.networkType,
            supportedTypes: "7(核心网), 2(省分通用)",
          });
          return;
        }

        // 构建路由参数
        const routeParams = {
          name: routeInfo.routeName,
          query: {
            workItemId: actionData.workItemId,
            processInstId: actionData.processInstId,
            woId: actionData.woId,
            processDefId: actionData.processDefId,
            processNode: actionData.processNode,
            fromPage: actionData.fromPage || "智能体",
            networkType: actionData.networkType,
            networkTypeTop: actionData.networkTypeTop,
            professionalType: actionData.professionalType,
            ...routeInfo.extraQuery,
          },
        };

        console.log("🤖 [全局Layout] 准备跳转到新工单:", routeParams);

        // 执行路由跳转
        try {
          const routeResult = this.$router.push(routeParams);
          // 检查是否返回Promise，如果是则处理catch
          if (routeResult && typeof routeResult.catch === "function") {
            routeResult.catch(err => {
              // 忽略重复导航错误
              if (err.name !== "NavigationDuplicated") {
                console.error("🤖 [全局Layout] 路由跳转失败:", err);
              }
            });
          }
          console.log("🤖 [全局Layout] 路由跳转成功");
        } catch (err) {
          // 处理同步错误
          if (err.name !== "NavigationDuplicated") {
            console.error("🤖 [全局Layout] 路由跳转失败:", err);
          }
        }
      } catch (error) {
        console.error("🤖 [全局Layout] 处理woNewTab消息失败:", error);
      }
    },

    // 根据网络类型获取工单路由信息
    getWorkOrderRouteInfo(actionData) {
      const { networkType, networkTypeTop, professionalType } = actionData;
      const networkTypeTopNum = parseInt(networkTypeTop || networkType);

      console.log("🤖 [全局Layout] 解析工单路由信息:", {
        networkType,
        networkTypeTop,
        networkTypeTopNum,
        professionalType,
      });

      // 根据 networkTypeTop 判断路由
      switch (networkTypeTopNum) {
        case 1:
          // 省分
          return {
            routeName: "provinceOrder_detail",
            extraQuery: {},
          };

        case 2:
          // 省分通用专业 (SFTY)
          return {
            routeName: "commonProvinceOrder_detail",
            extraQuery: {},
          };

        case 4:
          // 高铁 (GT)
          return {
            routeName: "gtOrder_detail",
            extraQuery: {},
          };

        case 5:
          // 集团通用
          return {
            routeName: "common_orderDetail",
            extraQuery: {
              frameTabTitle: "集团通用故障工单",
            },
          };

        case 6:
          // 无线网
          return {
            routeName: "wireless_orderDetail",
            extraQuery: {},
          };

        case 7:
          // 核心网
          return {
            routeName: "common_orderDetail",
            extraQuery: {
              frameTabTitle: "核心网故障工单",
            },
          };

        case 8:
          // IP专业
          return {
            routeName: "ipProfessionOrder_Detail",
            extraQuery: {
              frameTabTitle: "IP专业故障工单",
            },
          };

        case 9:
          // 平台
          return {
            routeName: "common_orderDetail",
            extraQuery: {
              frameTabTitle: "平台故障工单",
            },
          };

        case 10:
          // GNOC督办单
          return {
            routeName: "supervise_orderDetail",
            extraQuery: {
              frameTabTitle: "GNOC督办单",
            },
          };

        case 0:
          // 根据专业类型进一步判断
          switch (professionalType) {
            case "传输网":
              return {
                routeName: "backbone_toDoRepairOrderDetail",
                extraQuery: {},
              };

            case "IT云设备":
              return {
                routeName: "itCloud_woDetail",
                extraQuery: {},
              };

            case "通信云":
              return {
                routeName: "commCloud_woDetail",
                extraQuery: {},
              };

            default:
              console.warn(
                "🤖 [全局Layout] networkTypeTop=0时不支持的专业类型:",
                professionalType
              );
              return null;
          }

        default:
          // 其他网络类型暂不支持
          console.warn("🤖 [全局Layout] 不支持的网络类型:", {
            networkType,
            networkTypeTop,
            networkTypeTopNum,
            professionalType,
            supportedTypes:
              "1(省分), 2(省分通用), 4(高铁), 5(集团通用), 6(无线网), 7(核心网), 8(IP专业), 9(平台), 10(GNOC督办单), 0(根据专业类型)",
          });
          return null;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.horizontal-menu-breadcrumb {
  width: 100%;
  @include themify() {
    background-color: themed("$componentBg");
  }
  margin-left: 0px !important;
  margin-top: 0px;
  padding: 0 12px;
}

::v-deep .el-backtop {
  background: rgba(0, 0, 0, 0.4) !important;
  .el-button--info {
    background-color: rgba(0, 0, 0, 0.4);
    border-color: rgba(0, 0, 0, 0.4);
  }
}

// Grid 布局样式 - 使用 CSS Grid 管理布局
.layout-grid {
  position: relative;
  height: 100vh;
  overflow: hidden;

  // CSS 变量定义
  --ai-agent-min-width: 320px;
  --ai-agent-max-width: 600px;

  // 默认状态：只显示主要内容区域
  display: grid;
  grid-template-columns: 1fr;
  grid-template-areas: "main";

  // 智能体激活状态：主要内容区域 + 智能体区域
  &.ai-agent-active {
    grid-template-columns: 1fr minmax(
        var(--ai-agent-min-width),
        min(40%, var(--ai-agent-max-width))
      );
    grid-template-areas: "main agent";
    gap: 20px; // 固定20px间隔
  }

  // 全屏状态：隐藏智能体，主内容区域占满
  &.layout-fullscreen {
    grid-template-columns: 1fr;
    grid-template-areas: "main";
    gap: 0;

    .layout-ai-agent {
      display: none; // 全屏时隐藏智能体
    }
  }

  // 主要内容区域（左边栏 + 工单详情）
  .layout-main-area {
    position: relative;
    grid-area: main;
    min-width: 400px; // 最小宽度保证
    overflow: hidden;
  }

  // 继承原有的布局样式
  .layout-container {
    .frame-header-wrap {
      width: 100%;
      @include themify() {
        background-color: rgba(themed("$componentBg"), 1);
        // border-bottom: $--border-base;
      }
      // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    }
    .head-navbar {
      width: 100%;
      margin-bottom: 2px;
    }
    .frame-header-fix {
      position: fixed;
      z-index: 2000;
    }
    ::v-deep .frame-tabs .el-tabs__header {
      margin: 0 0;
    }
    .frame-container-main {
      // background-color: $frameContainerBg;
      padding: 0px;
      overflow-x: hidden;
      overflow: hidden;
      ::v-deep {
        .el-loading-mask {
          z-index: auto;
        }
        > *:first-child:not([class~="full-main"]) {
          // padding: 8px;
        }
        > .full-main {
          padding: 0;
        }
      }
    }
  }

  .btn-exit-full-screen {
    position: fixed;
    top: 240px;
    right: 0;
    width: 48px;
    height: 48px;
    @include themify() {
      background-color: rgba(themed("$--color-primary"), 0.4);
    }
    line-height: 48px;
    font-size: 24px;
    text-align: center;
    padding: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    z-index: 999;
    &:hover,
    &:focus {
      @include themify() {
        background-color: rgba(themed("$--color-primary"), 0.5);
      }
    }
  }

  // 智能体区域
  .layout-ai-agent {
    grid-area: agent;
    margin-top: var(--header-height, 0px); // 从顶部导航栏下方开始
    height: calc(100vh - var(--header-height, 0px)); // 减去顶部导航栏高度
    max-height: calc(100vh - var(--header-height, 0px)); // 防止溢出
    background: #fff;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    border-left: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
    overflow: hidden; // 防止内容溢出

    // 移除了loading相关样式，因为iframe内部已处理loading效果

    // 确保智能体容器完美填充
    ::v-deep .ai-agent-container {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden; // 防止内容溢出

      .ai-agent-content {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .ai-agent-iframe {
          flex: 1;
          width: 100%;
          height: 100%;
          border: none;
          outline: none;
          background: #f5f5f5; // 加载时的背景色

          // 确保iframe完全填充
          &:focus {
            outline: none;
          }
        }

        .ai-agent-error {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          padding: 20px;
          text-align: center;

          .error-icon {
            font-size: 48px;
            color: #f56c6c;
            margin-bottom: 16px;
          }

          .error-text {
            h3 {
              margin: 0 0 8px 0;
              color: #303133;
              font-size: 16px;
            }

            p {
              margin: 0 0 16px 0;
              color: #606266;
              font-size: 14px;
            }
          }
        }
      }
    }

    // 智能体面板的滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 响应式处理 - Grid 自动适应
  @media (max-width: 1200px) {
    --ai-agent-min-width: 300px;
    --ai-agent-max-width: 480px;
  }

  @media (max-width: 768px) {
    &.ai-agent-active {
      grid-template-columns: 0 1fr; // 移动端隐藏主内容，智能体全屏
      grid-template-areas: "main agent";
      gap: 0;
    }

    .layout-main-area {
      overflow: hidden;
      width: 0;
    }
  }

  @media (max-width: 480px) {
    --ai-agent-min-width: 100%;
    --ai-agent-max-width: 100%;
  }
}

// 旋转动画
@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
