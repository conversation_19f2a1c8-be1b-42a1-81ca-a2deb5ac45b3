<template>
  <div
    class="common-wo-detail-wrapper"
    :class="{
      mobile: isMobile,
    }"
    :style="wrapperStyle"
  >
    <head-fixed-layout class="draft-eidt-page" v-loading="pageLoading">
      <template #header>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="14" class="head-title">
            <text-collapse-title
              :text="`【省分故障工单】${headInfo.title}`"
              :max-lines="3"
            ></text-collapse-title>
            <el-button
              class="modifyT"
              @click="modifyTitle"
              v-if="showModifyBtn"
            ></el-button>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="10" class="head-handle-wrap">
            <el-button-group>
              <el-button type="button" @click="onHeadHandleClick('jcxx')"
                >基础信息</el-button
              >
              <el-button
                v-if="showAlarm"
                type="button"
                @click="onHeadHandleClick('gjxq')"
                >告警详情</el-button
              >
              <el-button
                type="button"
                v-if="showRelation"
                @click="onHeadHandleClick('glzd')"
                >关联诊断</el-button
              >
              <!-- <el-button
                type="button"
                :style="getStatusStyle()"
                @click="onHeadHandleClick('fkdxq')"
                >反馈单详情</el-button
              > -->
              <el-dropdown
                @command="onHeadHandleClick"
                class="el-button more-dropdown"
                size="medium"
              >
                <el-button type="button">更多</el-button>
                <el-dropdown-menu slot="dropdown">
                  <template v-for="(item, i) in headMoreDrops">
                    <template v-if="item.command == 'gjgx'">
                      <el-dropdown-item
                        :command="item.command"
                        :key="i"
                        v-if="showShareInfo"
                      >
                        {{ item.title }}
                      </el-dropdown-item>
                    </template>
                    <template v-else-if="item.command == 'scbg'">
                      <el-dropdown-item
                        :command="item.command"
                        :key="i"
                        v-if="showUpInfo"
                      >
                        {{ item.title }}
                      </el-dropdown-item>
                    </template>
                    <template v-else>
                      <el-dropdown-item :command="item.command" :key="i">
                        {{ item.title }}
                      </el-dropdown-item>
                    </template>
                  </template>
                </el-dropdown-menu>
              </el-dropdown>
            </el-button-group>
            <br />
          </el-col>
        </el-row>
        <!-- 按钮动态展示 -->
        <div class="head-handle-wrap" style="text-align: right">
          <template v-for="(item, i) in processButtonArr">
            <el-popover
              v-if="item == '现场打点'"
              :key="i"
              placement="top-start"
              title="提示"
              width="200"
              class="btnleft__group"
              trigger="hover"
              content="请到APP上操作"
            >
              <el-button size="mini" slot="reference">{{ item }}</el-button>
            </el-popover>
            <el-popover
              v-else-if="item == '返单'"
              :key="i"
              placement="bottom"
              width="310"
              popper-class="szTipCla"
              v-model="fbTipVisible"
              trigger="manual"
            >
              <div><i class="el-icon-warning"></i>{{ fbTipMsg }}</div>
              <el-button
                slot="reference"
                size="mini"
                type="primary"
                class="btnleft__group"
                :key="i"
                @click="buttonClick(item)"
                v-loading.fullscreen.lock="processFullscreenLoading"
                >{{ item }}</el-button
              >
            </el-popover>
            <template v-else>
              <!--              <template v-if="item == '转派' && turnFlag == 'Y'"></template>-->
              <template v-if="item == '告警清除申请'"></template>
              <template v-else-if="item == '人工确认清除'"></template>
              <template v-else-if="item == '定性审核'"></template>
              <template v-else-if="item == '故障报告审核'"></template>
              <!--              <template v-else-if="item == '现场打点'">-->
              <!--                <el-button-->
              <!--                  type="primary"-->
              <!--                  class="btnleft__group"-->
              <!--                  :key="i"-->
              <!--                  disabled-->
              <!--                  >{{ item }}</el-button-->
              <!--                >-->
              <!--              </template>-->
              <template v-else>
                <el-button
                  size="mini"
                  type="primary"
                  class="btnleft__group"
                  :key="i"
                  @click="buttonClick(item)"
                  v-loading.fullscreen.lock="processFullscreenLoading"
                  >{{ item }}</el-button
                >
              </template>
            </template>
          </template>
        </div>
        <el-divider
          direction="horizontal"
          content-position="left"
          class="divider"
        ></el-divider>
        <div class="head-info2">
          <el-row :gutter="20" tag="p">
            <el-col
              :xs="24"
              :sm="24"
              :md="12"
              :lg="12"
              tag="p"
              class="head-sheetId"
            >
              工单编号: {{ headInfo.sheetId }}
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12" tag="p">
              <el-row :gutter="20" type="flex" justify="end">
                <el-col
                  :xs="24"
                  :sm="12"
                  :md="12"
                  :lg="5"
                  tag="p"
                  class="head-up-down"
                >
                  <div>当前处理人</div>
                  <div
                    class="sheetNo_class"
                    @click="openPeerProcessor"
                    style="
                      cursor: pointer;
                      text-decoration: underline;
                      color: #b50b14;
                      user-select: unset;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      font-size: 16px;
                      line-height: 28px;
                    "
                    :title="headInfo.currentHandler"
                  >
                    {{ headInfo.currentHandler }}
                  </div>
                </el-col>
                <el-col
                  :xs="24"
                  :sm="12"
                  :md="12"
                  :lg="5"
                  tag="p"
                  class="head-up-down"
                >
                  <div>工单状态</div>
                  <div style="font-size: 16px; line-height: 28px">
                    {{ headInfo.sheetStatus }}
                  </div>
                </el-col>
                <el-col
                  :xs="24"
                  :sm="12"
                  :md="12"
                  :lg="7"
                  tag="p"
                  class="head-up-down"
                  v-if="headInfo.sheetStatus == '待受理'"
                >
                  <div>剩余受理时间</div>
                  <div
                    class="text-primary"
                    style="
                      cursor: pointer;
                      color: #b50b14;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      font-size: 16px;
                      line-height: 28px;
                    "
                    :title="setAcceptTimeLimit"
                  >
                    {{ setAcceptTimeLimit }}
                  </div>
                </el-col>
                <el-col
                  :xs="24"
                  :sm="12"
                  :md="12"
                  :lg="7"
                  tag="p"
                  class="head-up-down"
                  v-if="
                    headInfo.sheetStatus != '待受理' &&
                    headInfo.sheetStatus != '已归档' &&
                    headInfo.sheetStatus != '作废' &&
                    headInfo.sheetStatus != '异常归档'
                  "
                >
                  <div>剩余处理时间</div>
                  <div
                    class="text-primary"
                    style="
                      cursor: pointer;
                      color: #b50b14;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      font-size: 16px;
                      line-height: 28px;
                    "
                    :title="setHandleTimeLimit"
                  >
                    {{ setHandleTimeLimit }}
                  </div>
                </el-col>

                <el-col
                  :xs="24"
                  :sm="12"
                  :md="12"
                  :lg="6"
                  tag="p"
                  class="head-up-down"
                >
                  <div>工单总耗时0</div>
                  <div
                    class="text-primary"
                    style="
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      font-size: 16px;
                      color: #c43c43;
                      line-height: 28px;
                    "
                    :title="headInfo.totalWorkingTime"
                  >
                    {{ headInfo.totalWorkingTime }}
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row :gutter="20" tag="p">
            <el-col :xs="24" :sm="24" :md="12" :lg="8" tag="p">
              建单人: {{ headInfo.buildSingleMan }}
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="8" tag="p">
              建单部门: {{ headInfo.buildSingleDept }}
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="8" tag="p">
              建单时间: {{ headInfo.buildSingleTime }}
            </el-col>
          </el-row>
          <el-row :gutter="20" tag="p">
            <el-col :xs="24" :sm="24" :md="12" :lg="8" tag="p">
              工单来源: {{ headInfo.sourceInfo }}
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="8" tag="p">
              发生时间: {{ headInfo.occurrenceTime }}
            </el-col>
            <!--          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">-->
            <!--            发生地区: {{ headInfo.faultRegion }}-->
            <!--          </el-col>-->
            <el-col :xs="24" :sm="24" :md="12" :lg="8" tag="p">
              紧急程度: {{ headInfo.emergencyDegree }}
            </el-col>
          </el-row>
        </div>
      </template>

      <base-info
        ref="jcxx"
        v-if="basicWorkOrderData"
        :basicWorkOrderData="basicWorkOrderData"
        :woId="common.woId"
        :workItemId="common.workItemId"
      />
      <!-- 告警详情 -->
      <alarm-detail
        ref="gjxq"
        :common="common"
        :clear-apply-btn-show="alarmClearApplyBtnShow"
        :manual-btn-show="manualAlarmClearBtnShow"
        v-if="showAlarm"
      />
      <!-- 关联诊断 --->
      <relation-diagnosis
        ref="glzd"
        v-if="showRelation"
        :woId="common.woId"
        :basicWorkOrderData="basicWorkOrderData"
        :key="lastReciveNum"
        :postReciveNum="reciveNum"
      />
      <!-- 反馈单详情 -->
      <feedback-sheet
        ref="fkdxq"
        v-if="showFkdxq && common.woId"
        :isShowAudit="isShowQualitativeAuditButton"
        :common="common"
        :woId="common.woId"
        :basicWorkOrderData="basicWorkOrderData"
        @qualitativeReviewSubmit="qualitativeReviewSubmit"
      />
      <!--    上传故障报告信息-->
      <upload-fault-file
        ref="scbg"
        :common="common"
        :isShowReportReview="dialogUpFFAuditVisible"
        v-if="showUpInfo"
        @dialogReportAuditSubmitClose="dialogReportAuditSubmitClose"
      />
      <!--    共建共享处理信息-->
      <share-handle-detail
        ref="gjgx"
        :woId="common.woId"
        v-if="showShareInfo"
        @showAudit="showShareAudit"
      />

      <!-- 处理详情 -->
      <deal-details
        ref="clxq"
        v-if="showClxq && this.common.woId != ''"
        :woId="common.woId"
        :basicWorkOrderData="basicWorkOrderData"
      />
      <!--    流程日志-->
      <process-log
        ref="lcrz"
        v-if="showLcrz && this.common.woId != ''"
        :woId="common.woId"
        :LogsData="LogsData"
      />
      <!--    流程图-->
      <flow-chart
        ref="lct"
        v-if="showLct && this.common.woId != ''"
        :common="common"
      />

      <!--    阶段反馈-->
      <el-dialog
        title="阶段反馈"
        :visible.sync="dialogStageFeedbackVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogStageFeedbackClose"
        width="550px"
      >
        <stage-feedback
          :common="common"
          actionName="阶段反馈"
          @stageBackDialogClose="stageBackDialogCommitClose"
        ></stage-feedback>
      </el-dialog>

      <!--    返单-->
      <el-dialog
        title="返单"
        :visible.sync="dialogBackSingleVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogBackSingleClose"
        :fullscreen="false"
        width="83%"
        top="5vh"
      >
        <back-single
          ref="backSingleForm"
          :common="common"
          :timing="timing"
          @closeBackSingleDialog="dialogBackSingleSubmitClose"
        ></back-single>
      </el-dialog>

      <!--    资源勘误-->
      <el-dialog
        title="资源勘误"
        :visible.sync="dialogZYKWVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogZYKWClose"
        :fullscreen="false"
        width="84%"
        top="5vh"
      >
        <ResourceCorrigendum
          :common="common"
          @rcDialog="dialogZYKWSubmitClose"
        ></ResourceCorrigendum>
      </el-dialog>

      <!--修改主题-->
      <el-dialog
        title="修改主题"
        :visible.sync="dialogModifyTitleVisiable"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogModifyTitleClose"
        width="600px"
      >
        <el-form ref="modifyForm" :model="modifyForm" label-width="90px">
          <el-form-item
            label="工单主题:"
            prop="title"
            :rules="{
              required: true,
              message: '请填写工单主题',
            }"
          >
            <el-input
              maxlength="400"
              show-word-limit
              @keyup.native="descTipZT(400, 'title', 'showgdzt')"
              type="textarea"
              :rows="3"
              placeholder="请填写工单主题"
              v-model="modifyForm.title"
              style="width: 100%"
            >
            </el-input>
            <div class="el-form-item__error" v-if="showgdzt">
              已超过填写字数上限
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="handleModifySubmit('modifyForm')"
            v-loading.fullscreen.lock="modifySubmitLoading"
            >提 交</el-button
          >
          <el-button @click="onResetModify">重 置</el-button>
        </div>
      </el-dialog>

      <!--撤单-->
      <el-dialog
        title="撤单"
        :visible.sync="dialogRevokeVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogRevokeClose"
        width="550px"
      >
        <el-form ref="revokeForm" :model="revokeForm" label-width="90px">
          <el-form-item
            label="撤单原因:"
            prop="auditOpinion"
            :rules="{
              required: true,
              message: '请填写撤单原因',
            }"
          >
            <el-input
              type="textarea"
              :rows="2"
              placeholder="请填写撤单原因"
              v-model="revokeForm.auditOpinion"
              style="width: 300px"
              show-word-limit
              maxlength="255"
              @keyup.native="descTip(255, 'auditOpinion', 'showTip')"
            >
            </el-input>
            <div class="el-form-item__error" v-if="showTip">
              已超过填写字数上限
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="handleRevokeSubmit('revokeForm')"
            v-loading.fullscreen.lock="revokeSubmitLoading"
            >提 交</el-button
          >
          <el-button @click="onResetRevoke">重 置</el-button>
        </div>
      </el-dialog>

      <!--是否上站-->
      <el-dialog
        title="是否上站"
        :visible.sync="dialogSZVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogSZClose"
        width="420px"
      >
        <el-form ref="szForm" :model="szForm" label-width="90px">
          <el-form-item
            label="是否上站:"
            prop="isSZ"
            :rules="{
              required: true,
              message: '请选择',
            }"
          >
            <el-radio-group
              v-model="szForm.isSZ"
              @input="szChange"
              style="width: 260px"
            >
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
            <div class="el-form-item__error" v-if="showSZTip">
              *上站选"是"则必须进行现场打点
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="handleSZSubmit('szForm')"
            v-loading.fullscreen.lock="szSubmitLoading"
            >提 交</el-button
          >
          <el-button @click="onResetSZ">重 置</el-button>
        </div>
      </el-dialog>

      <!--追单-->
      <el-dialog
        title="追单"
        :visible.sync="dialogAfterSingleVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogAfterSingleClose"
        width="600px"
      >
        <after-single
          :common="common"
          actionName="追单"
          @closeAfterSingleDialog="dialogAfterSingleSubmitClose"
        ></after-single>
      </el-dialog>
      <!--  挂起0 -->
      <el-dialog
        title="挂起"
        :visible.sync="dialogHangUpVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogHangUpClose"
        width="540px"
      >
        <hang-up
          :common="common"
          :opType="1"
          actionName="挂起"
          @closeDialogHangUp="dialogHangUpSubmitClose"
        ></hang-up>
      </el-dialog>
      <!--  解挂0  -->
      <el-dialog
        title="解挂"
        :visible.sync="dialogSolutionToHangVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogSolutionToHangClose"
        width="540px"
      >
        <hang-up
          :common="common"
          :opType="2"
          actionName="解挂"
          @closeDialogHangUp="dialogSolutionToHangSubmitClose"
        ></hang-up>
      </el-dialog>

      <!--  挂起审核  -->
      <el-dialog
        title="挂起审核"
        :visible.sync="dialogPendingReviewVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogPendingReviewClose"
        width="450px"
      >
        <audit
          :common="common"
          :opContent="opContent"
          actionName="挂起审核"
          @closeDialogPendingReview="dialogPendingReviewSubmitClose"
        ></audit>
      </el-dialog>

      <!--  解挂审核  -->
      <el-dialog
        title="解挂审核"
        :visible.sync="dialogSolutionToHangAuditVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogSolutionToHangAuditClose"
        width="450px"
      >
        <audit
          :common="common"
          :opContent="opContent"
          actionName="解挂审核"
          @closeDialogSolutionToHangAudit="dialogSolutionToHangAuditSubmitClose"
        ></audit>
      </el-dialog>

      <!--异常终止-->
      <el-dialog
        title="异常终止"
        :visible.sync="dialogAbendVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogAbendClose"
        width="450px"
      >
        <abend
          :common="common"
          @closeDialogAbend="dialogAbendSubmitClose"
        ></abend>
      </el-dialog>

      <!--异常终止审核-->
      <el-dialog
        title="异常终止审核"
        :visible.sync="dialogAbendAuditVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogAbendAuditClose"
        width="450px"
      >
        <abend-audit
          :common="common"
          @closeDialogAbendAudit="dialogAbendAuditSubmitClose"
        ></abend-audit>
      </el-dialog>

      <!--    转派-->
      <el-dialog
        title="转派"
        :visible.sync="dialogTurnToSendVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogTurnToSendClose"
        width="480px"
      >
        <turn-to-send
          @closeDialogTurnToSend="dialogTurnToSendSubmitClose"
          :common="common"
          :type="type"
        ></turn-to-send>
      </el-dialog>
      <!--    转办0-->
      <el-dialog
        width="480px"
        title="转办"
        :visible.sync="transferTodoDialogVisible"
        :close-on-click-modal="false"
        :destroy-on-close="true"
        @close="dialogtransferTodoClose"
      >
        <transfer-todo
          @closeDialogTransfer="dialogTransferClose"
          :common="common"
          :type="type"
          actionName="批量转办"
        ></transfer-todo>
      </el-dialog>
      <!--    上传故障报告0-->
      <el-dialog
        title="上传故障报告"
        :visible.sync="dialogUpFFVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogUpFFClose"
        width="600px"
      >
        <upload-fault-file-handle
          :common="common"
          actionName="上传故障报告"
          @closeUpFileDialog="dialogUpFFSubmitClose"
        ></upload-fault-file-handle>
      </el-dialog>
      <!--    故障处理0-->
      <el-dialog
        title="故障处理"
        :visible.sync="dialogFaultHandleVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogFaultHandleClose"
        :fullscreen="false"
        width="83%"
        top="5vh"
      >
        <fault-handle
          :common="common"
          :order-info="basicWorkOrderData"
          @closeFaultHandleDialog="dialogFaultHandleSubmitClose"
        ></fault-handle>
      </el-dialog>

      <!--    联通确认0-->
      <el-dialog
        title="联通确认"
        :visible.sync="dialogLTConfirmVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogLTConfirmClose"
        :fullscreen="false"
        width="83%"
        top="5vh"
      >
        <unicom-confirm
          :common="common"
          :order-info="basicWorkOrderData"
          @closeLTConfirmDialog="dialogLTConfirmSubmitClose"
        ></unicom-confirm>
      </el-dialog>

      <!--处理人列表-->
      <el-dialog
        title="处理人"
        :visible.sync="dialogPeerProcessorVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="PeerProcessorClose"
        :fullscreen="false"
        width="70%"
        top="5vh"
      >
        <PeerProcessorDetail
          :common="common"
          :persons="basicWorkOrderData.operatePersonId"
        ></PeerProcessorDetail>
      </el-dialog>

      <!--    一键催办-->
      <one-key-ivr
        :dialogOneKeyIvrNotice.sync="dialogOneKeyVisible"
        :common="common"
        :sheetId="headInfo.sheetId && headInfo.sheetId"
        :sheetCreateTime="headInfo.buildSingleTime && headInfo.buildSingleTime"
      />

      <!-- 弹出框信息 -->
      <el-dialog
        title="消息"
        :visible.sync="chatopsVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleCloseMessage"
        width="1000px"
      >
        <iframe
          :src="rsaEncryptUrl"
          frameborder="0"
          width="100%"
          height="500px"
          scrolling="auto"
          style="border: 1px solid #eee; margin-top: -20px"
        ></iframe>
      </el-dialog>

      <!--现场打点-->
      <!--    <el-dialog-->
      <!--      title="PC现场打点"-->
      <!--      :visible.sync="dialogLocationVisible"-->
      <!--      width="480px"-->
      <!--    >-->
      <!--      <el-form-->
      <!--        ref="locationForm"-->
      <!--        :model="locationForm"-->
      <!--        :rules="locationRules"-->
      <!--        label-width="80px"-->
      <!--      >-->
      <!--        <el-form-item label="经度">-->
      <!--          <el-input v-model="locationForm.longitude"></el-input>-->
      <!--        </el-form-item>-->
      <!--        <el-form-item label="纬度">-->
      <!--          <el-input v-model="locationForm.latitude"></el-input>-->
      <!--        </el-form-item>-->
      <!--      </el-form>-->
      <!--      <span slot="footer" class="dialog-footer">-->
      <!--        <el-button @click="dialogLocationVisible = false">取 消</el-button>-->
      <!--        <el-button type="primary" @click="submitLocationForm('locationForm')"-->
      <!--        >确 定</el-button-->
      <!--        >-->
      <!--      </span>-->
      <!--    </el-dialog>-->
    </head-fixed-layout>
    <!-- 悬浮 外链接按钮 -->
    <div class="outer-link">
      <span class="num">{{ messageNum }}</span>
      <span class="icon" @click="handleOuterLink"></span>
      <span class="name" @click="handleOuterLink">chatops</span>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import oneKeyIvr from "../../commCloud/components/CommCloudoneKeyIVR.vue";
import TransferTodo from "../../workOrder/workOrderWaitDetail/components/TransferTodo.vue";
import HeadFixedLayout from "../../workOrder/components/HeadFixedLayout.vue";
import BaseInfo from "./components/BaseInfo.vue";
import ShareHandleDetail from "./components/ShareHandleDetail.vue";
import UploadFaultFile from "./components/UploadFaultFile.vue";
import UploadFaultFileHandle from "./components/UploadFaultFileHandle.vue";
import AlarmDetail from "./components/AlarmDetail.vue";
import RelationDiagnosis from "./components/RelationDiagnosis.vue";
import FeedbackSheet from "./components/FeedbackSheet.vue";
import DealDetails from "./components/DealDetails.vue";
import ProcessLog from "./components/ProcessLog.vue";
import FlowChart from "./components/FlowChart.vue";
import StageFeedback from "./components/StageFeedback.vue";
import HangUp from "./components/HangUp.vue";
import Audit from "./components/Audit.vue";
import Abend from "./components/Abend.vue";
import AbendAudit from "./components/AbendAudit";
import AfterSingle from "./components/AfterSingle";
import TurnToSend from "./components/TurnToSend.vue";
import QualitativeAudit from "./components/QualitativeAudit.vue";
import OrderEvaluate from "./components/OrderEvaluate.vue";
import EliminateFaults from "./components/EliminateFaults.vue";
import Qualitative from "./components/Qualitative.vue";
import BackSingle from "./components/BackSingle.vue";
import FaultHandle from "./components/FaultHandle.vue";
import UnicomConfirm from "./components/UnicomConfirm.vue";
import PeerProcessorDetail from "./components/PeerProcessorDetail";
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";
import TextCollapseTitle from "@plugin/backbone/components/TextCollapseTitle.vue";

import ResourceCorrigendum from "./components/ResourceCorrigendum";

import {
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiAccept,
  apiRevoke,
  apiModifyTitle,
  apiHaveRead,
  apiActionPublic,
  getCurrentTime,
  apiSetRead,
  apiGetRsaEncrypt,
  apiMessageRsaEncrypt,
  apiGetMeassgeNum,
  apiLocation,
  apiGetWoid,
  apiqueryFeedback,
  apiGetShareInfo,
  apiQueryUploadFileDetail,
  apiGetRelationDiagnosis,
  apiBackSingleInit,
  apiSyncClear,
  apiQueryAlarmDetail,
  apiGetPeerProcessorList,
  apiGetProcessNode,
} from "./api/CommonApi";
import {
  checkAiAgentAvailability,
  getAiAgentConfig,
  getAiAgentUrl,
  mockApiCall,
} from "@/api/aiAgent";

export default {
  name: "WorkOrderWaitDetail",
  components: {
    PeerProcessorDetail,
    oneKeyIvr,
    FaultHandle,
    UnicomConfirm,
    TransferTodo,
    HeadFixedLayout,
    BaseInfo,
    AlarmDetail,
    ShareHandleDetail,
    UploadFaultFileHandle,
    UploadFaultFile,
    RelationDiagnosis,
    FeedbackSheet,
    DealDetails,
    ProcessLog,
    FlowChart,
    StageFeedback,
    HangUp,
    Audit,
    Abend,
    AfterSingle,
    AbendAudit,
    TurnToSend,
    QualitativeAudit,
    OrderEvaluate,
    EliminateFaults,
    Qualitative,
    BackSingle,
    TextCollapse,
    ResourceCorrigendum,
    TextCollapseTitle,
  },
  data() {
    return {
      fbTipVisible: false,
      fbTipMsg: "",
      showgdzt: false,
      showTip: false,
      showTipTime: 5000,
      // reportId:'',//上传故障报告初始化需要，如果返回的流程id和本工单流程id一样
      // shareOrderInfo:{},//共建共享信息
      headInfo: {
        title: "",
        sheetId: "",
        currentHandler: "",
        currentLink: "",
        sheetStatus: null,
        acceptTimeLimit: "",
        processingTimeLimit: "",
        processTimeLimit: "",
        totalWorkingTime: "",
        buildSingleMan: "",
        buildSingleDept: "",
        buildSingleTime: "",
        sourceInfo: "",
        occurrenceTime: "",
        emergencyDegree: "",
      },
      headMoreDrops: [],
      processButtonArr: [],
      basicWorkOrderData: {},
      LogsData: [],
      dealData: [],
      alarmsData: [],
      messageNum: "", // chatops 未读条数
      isTitleCollapse: false,
      common: {
        woId: "", //工单ID
        workItemId: "", //工作项ID
        processInstId: null, //流程实例ID
        processDefId: null, //流程定义ID
        failureTime: "", //故障发生时间
        failureInformTime: "", //故障通知时间
        actionName: "", //操作按钮名称
        processNode: "", //环节名称
        sheetNo: "", //工单编号
        isSender: null, //是否为建单人
        agentManId: "", //主送人Id
        agentMan: "", //主送人
        copyManId: "", //抄送人Id
        copyMan: "", //抄送人
        sheetStatus: "", //工单状态
        agentDeptCode: "",
        agentDeptName: "",
        copyDeptCode: "",
        copyDeptName: "",
        professionalType: null,
        networkType: null,
        hangOver: null, //挂起历时
        faultCauseDescription: null, //故障原因描述
        networkTypeName: null, //网络类型中文名
        professionalTypeName: null, //专业类型中文名
      },
      timing: {
        hangTime: "", //挂起时间
      },
      showFkdxq: false, //反馈单详情
      showClxq: false, //处理详情
      showLcrz: false, //流程日志
      showLct: false, //流程图
      showRelation: false, //关联诊断
      showAlarm: false, //告警详情
      showUpInfo: false, //上传故障报告详情
      showShareInfo: false, //共建共享详情

      processFullscreenLoading: false,
      isShowQualitativeAuditButton: false,
      //阶段反馈
      dialogStageFeedbackVisible: false,

      //返单
      dialogBackSingleVisible: false,
      //撤单
      dialogRevokeVisible: false,
      revokeSubmitLoading: false,
      revokeForm: {
        auditOpinion: null,
      },
      //修改主题
      showModifyBtn: false,
      dialogModifyTitleVisiable: false,
      modifySubmitLoading: false,
      modifyForm: {
        title: null,
      },
      //是否上站
      showSZTip: false,
      dialogSZVisible: false,
      szSubmitLoading: false,
      szForm: {
        isSZ: null,
      },
      //资源勘误
      dialogZYKWVisible: false,
      //追单
      dialogAfterSingleVisible: false,
      //挂起
      dialogHangUpVisible: false,
      //解挂
      dialogSolutionToHangVisible: false,
      //挂起审核
      // auditTitle: null,
      opContent: null,
      dialogPendingReviewVisible: false,
      //解挂审核
      dialogSolutionToHangAuditVisible: false,
      //异常终止
      dialogAbendVisible: false,
      //异常终止审核
      dialogAbendAuditVisible: false,
      //一键催办
      dialogOneKeyVisible: false,
      //转派
      dialogTurnToSendVisible: false,
      //转办
      transferTodoDialogVisible: false,
      //上传故障报告
      dialogUpFFVisible: false,
      //故障报告审核是否显示
      dialogUpFFAuditVisible: false,
      //告警清除申请按钮是否显示
      alarmClearApplyBtnShow: false,
      //人工确认清除按钮是否显示
      manualAlarmClearBtnShow: false,
      //定性审核
      dialogQualitativeAuditVisible: false,
      // PC现场打点
      dialogLocationVisible: false,
      //类型
      type: "single",
      // 故障处理
      dialogFaultHandleVisible: false,
      //联通确认
      dialogLTConfirmVisible: false,
      //处理人
      dialogPeerProcessorVisible: false,

      route: null,
      //从哪个页面进入的，不同页面进入，显示的按钮会不同，如：待阅工单只展示已阅、传阅按钮
      fromPage: null,
      pageLoading: true,
      networkType: null,
      // turnFlag: null, // 转派了，就不显示转派按钮。'Y'不显示
      // regularFdbkFlag: null, // 操作了，阶段反馈，才能处理完成，'Y'显示
      // locationFlag: null, //现场打点完了，才显示，'Y'显示
      rsaEncrypt: "", // chatops 未读加密
      rsaEncryptUrl: "", // chatops iframe 路径
      chatopsVisible: false, // chatops 弹出框
      // locationForm: {}, // pc现场定位form
      // locationRules: {
      //   longitude: [{ require: true, message: "现场打点经度必填" }],
      //   latitude: [{ require: true, message: "现场打点纬度必填" }],
      // },

      // 智能体相关数据
      isMobile: false,
      workOrderDataLoaded: false, // 工单数据是否已加载完成

      // 智能体消息监听器引用
      aiAgentMessageHandler: null,

      // ========== 新增：页面活跃状态管理 ==========
      // 当前页面是否为活跃tab
      isCurrentPageActive: false,
      // 页面实例ID（用于区分同一工单的多个页面实例）
      pageInstanceId: null,
      lastReciveNum: 1,
      reciveNum: 1,
    };
  },
  computed: {
    ...mapGetters([
      "userInfo",
      "token",
      "sidebar",
      // 全局智能体状态
      "showAiAgent",
      "isAiAgentAvailable",
      "aiAgentLoading",
      "aiAgentUrl",
      "aiAgentConfig",
    ]),

    // 智能体面板的最小宽度（用于CSS变量）
    aiAgentMinWidth() {
      return this.isMobile ? 0 : 300;
    },

    // 智能体面板的最大宽度（用于CSS变量）
    aiAgentMaxWidth() {
      return this.isMobile ? 0 : 600;
    },
    // mainProfessionalType() {
    //   return this.$route.query.mainProfessionalType;
    // },
    setAcceptTimeLimit() {
      let time = this.headInfo.acceptTimeLimit;
      if (time == "-") {
        return "-";
      } else if (time <= 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `已超时${Math.abs(time) % 60}分钟`;
        } else {
          return `已超时${Math.floor(Math.abs(time) / 60)}小时${
            Math.abs(time) % 60
          }分钟`;
        }
      } else if (time > 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `${time % 60}分钟`;
        } else {
          return `${Math.floor(time / 60)}小时${time % 60}分钟`;
        }
      } else {
        return "";
      }
    },
    setHandleTimeLimit() {
      let time = this.headInfo.processTimeLimit;
      if (time == "-") {
        return "-";
      } else if (time <= 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `已超时${Math.abs(time) % 60}分钟`;
        } else {
          return `已超时${Math.floor(Math.abs(time) / 60)}小时${
            Math.abs(time) % 60
          }分钟`;
        }
      } else if (time > 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `${time % 60}分钟`;
        } else {
          return `${Math.floor(time / 60)}小时${time % 60}分钟`;
        }
      } else {
        return "";
      }
    },
    // 容器样式（使用CSS变量传递动态值）
    wrapperStyle() {
      return {
        padding: "0",
        "--ai-agent-min-width": `${this.aiAgentMinWidth}px`,
        "--ai-agent-max-width": `${this.aiAgentMaxWidth}px`,
      };
    },
  },
  watch: {
    // 监听全局智能体显示状态变化
    showAiAgent: {
      handler(newVal, oldVal) {
        console.log("🤖 省二全局智能体状态变化:", { oldVal, newVal });

        // 当智能体展开时，检查专业类型为 （7无线、4动环、3传输）工单并发送初始化数据
        if (newVal && this.workOrderDataLoaded) {
          const isAvailable =
            this.common.professionalType == "3" ||
            this.common.professionalType == "4" ||
            this.common.professionalType == "7";

          if (isAvailable) {
            // 延迟发送，确保iframe已加载
            this.$nextTick(() => {
              setTimeout(() => {
                // 确认当前页面是激活状态（防止多页面冲突）
                const isCurrentPageActive = this.checkIfCurrentPageIsActive();
                if (isCurrentPageActive) {
                  this.sendInitDataToGlobalAgent();
                  // this.sendWoMessageToAiAgent();
                  console.log(
                    "🤖 智能体展开：省二工单且页面激活，发送智能体数据"
                  );
                } else {
                  console.log(
                    "🤖 智能体展开：当前页面不是激活状态，跳过发送智能体数据"
                  );
                }
              }, 1000);
            });
          } else {
            console.log("🤖 智能体展开：非省二工单，跳过发送智能体数据");
          }
        }
      },
      immediate: false,
    },
  },
  created() {
    this.route = this.$route;
    this.common.workItemId = this.$route.query.workItemId;
    this.common.processInstId = this.$route.query.processInstId;
    this.common.woId = this.$route.query.woId;
    this.common.processDefId = this.$route.query.processDefId;
    this.common.processNode = this.$route.query.processNode;
    this.networkType = this.$route.query.networkType;
    this.professionalType = this.$route.query.professionalType;

    //外部链接调用
    if (this.$route.query.outSystem) {
      if (this.$route.query.woId) {
        this.common.woId = this.$route.query.woId;
        this.common.processInstId = this.$route.query.processInstId;
      } else {
        let param = {
          woCode: this.$route.query.sheetNo,
        };
        apiGetWoid(param).then(res => {
          this.common.woId = res.data.woId;
          this.common.processInstId = res.data.processInstId;
        });
      }
    }
    // tag右击刷新页面的时候，获取当前环节。
    // 场景：智能体处理完受理之后，当前环节是处理，但是url上还是受理，导致当前环节还是受理，需要获取当前环节
    if (
      this.$route.query.processNode == "受理" &&
      this.$route.query.fromPage == "待办工单"
    ) {
      this.getProcessNode();
    }
  },
  mounted() {
    // if (this.networkType == "一干") {
    //
    // } else if (this.networkType == "国际") {
    //   this.headMoreDrops = [{ command: "lct", title: "流程图" }];
    // }
    this.headMoreDrops = [
      { command: "fkdxq", title: "反馈单详情" },
      { command: "gjgx", title: "共建共享处理信息" },
      { command: "scbg", title: "上传故障报告信息" },
      { command: "clxq", title: "处理详情" },
      { command: "lcrz", title: "流程日志" },
      { command: "lct", title: "流程图" },
    ];

    this.fromPage = this.$route.query.fromPage;

    if (this.fromPage == "待阅工单") {
      this.setReadStatus();
    }
    this.getWorkOrderInfo();
    this.getCurrentHandler();
    this.syncClearAlarm();
    // 初始化智能体功能

    // 生成页面实例ID（用于区分同一工单的多个页面实例）
    this.pageInstanceId =
      Date.now() + "_" + Math.random().toString(36).substr(2, 9);

    // 设置页面为活跃状态
    this.isCurrentPageActive = true;

    // 设置当前活跃工单信息（用于消息过滤）
    this.setCurrentActiveWorkOrder();

    // 初始化全局智能体（但不检查可用性，避免覆盖我们的网络类型检查）
    this.initGlobalAiAgentWithoutAvailabilityCheck();

    // 初始检查智能体可用性（此时网络类型可能还未设置，会隐藏按钮）
    this.checkAiAgentAvailabilityByNetworkType();
    this.checkMobile();
    window.addEventListener("resize", this.handleResize);

    // 添加智能体 postMessage 监听器
    this.setupAiAgentMessageListener();

    // 加载工单数据，完成后再初始化智能体
    // this.getShowButton().then(() => {
    //   this.getWorkOrderInfo();
    // });
  },
  activated() {
    // 当tab切换回来时，设置页面为活跃状态并发送初始化数据给智能体
    console.log("🤖 页面激活，设置为活跃状态并发送智能体数据");

    // 设置页面为活跃状态
    this.isCurrentPageActive = true;

    // 重新设置当前活跃工单
    this.setCurrentActiveWorkOrder();

    // ========== 页面激活智能体状态管理 ==========
    // 恢复智能体按钮显示（根据网络类型检查可用性）
    this.checkAiAgentAvailabilityByNetworkType();

    // ========== 智能体数据更新 ==========
    // 页面激活时，如果智能体已展开且是工单，则发送最新的工单数据
    // 这样用户在多个工单间切换时，智能体能获取到正确的工单信息
    this.$nextTick(() => {
      setTimeout(() => {
        // 只有当专业类型为 （7无线、4动环、3传输） 时才显示智能体按钮
        const isAvailable =
          this.common.professionalType == "3" ||
          this.common.professionalType == "4" ||
          this.common.professionalType == "7";
        const isAiAgentShown = this.showAiAgent;

        // 只有当智能体已展开且是工单且当前页面激活时才发送
        if (isAvailable && isAiAgentShown && this.workOrderDataLoaded) {
          // 再次确认当前页面是激活状态（防止多页面冲突）
          const isCurrentPageActive = this.checkIfCurrentPageIsActive();
          if (isCurrentPageActive) {
            this.sendInitDataToGlobalAgent();
            console.log(
              "🤖 页面激活：省二工单且智能体已展开且页面激活，发送智能体数据"
            );
          } else {
            console.log(
              "🤖 页面激活：当前页面不是激活状态，跳过发送智能体数据"
            );
          }
        } else {
          console.log(
            "🤖 页面激活：智能体未展开或省二工单，跳过发送智能体数据"
          );
        }
      }, 500);
    });
  },
  deactivated() {
    // 当tab切换离开时，设置页面为非活跃状态
    console.log("🤖 页面失活，设置为非活跃状态");
    this.isCurrentPageActive = false;
    // ========== 页面失活智能体状态管理 ==========
    // 处理页面失活时的智能体状态（根据用户偏好自动隐藏）
    this.$store.dispatch("aiAgent/handlePageDeactivated");

    // 隐藏智能体按钮（设置为不可用状态）
    this.$store.commit("aiAgent/SET_AI_AGENT_AVAILABLE", false);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    // 移除智能体消息监听器
    if (this.aiAgentMessageHandler) {
      window.removeEventListener("message", this.aiAgentMessageHandler);
    }
    // 清除当前活跃工单信息
    this.$store.dispatch("aiAgent/clearCurrentActiveWorkOrder");
  },
  methods: {
    descTipZT(count, name, showName) {
      if (this.modifyForm[name].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTipTime);
      } else {
        this[showName] = false;
      }
    },

    descTip(count, name, showName) {
      if (this.revokeForm[name].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTipTime);
      } else {
        this[showName] = false;
      }
    },
    getShowButton() {
      return new Promise(resolve => {
        let param = {
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          processInstId: this.common.processInstId,
          processNode: this.common.processNode,
          fromPage: this.fromPage,
        };
        if (sessionStorage.getItem(this.common.woId) == "受理") {
          param.actionName = "受理";
        } else {
          param.actionName = "";
        }
        apiGetShowButton(param)
          .then(res => {
            if (res.status == "0") {
              let self = this;
              self.processButtonArr = res?.data ?? [];
              self.processButtonArr.forEach(item => {
                if (item == "告警清除申请") {
                  this.alarmClearApplyBtnShow = true;
                }
                if (item == "人工确认清除") {
                  this.manualAlarmClearBtnShow = true;
                }
                if (item == "定性审核") {
                  this.isShowQualitativeAuditButton = true;
                }
                if (item == "故障报告审核") {
                  this.dialogUpFFAuditVisible = true;
                }
              });
            }
            resolve("success");
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    getWorkOrderInfo() {
      this.pageLoading = true;
      // 第三方打开时， url 中 获取 sheetNo 参数
      const url = window.location.href;
      const queryStri = url.substring(url.indexOf("?") + 1).split("&");
      let sheetNoUrl = "";
      queryStri.forEach(item => {
        if (item.indexOf("sheetNo") >= 0) {
          sheetNoUrl = decodeURI(item.split("=")[1]);
        }
      });

      let param = {
        woId: this.common.woId,
        processInstId: this.common.processInstId,
        workItemId: this.common.workItemId,
        woCode: sheetNoUrl,
      };

      apiGetWorkOrderInfo(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            this.getFeedbackData();
            this.getShareData();
            this.getUpFileData();

            self.basicWorkOrderData = res?.data ?? {};
            // self.basicWorkOrderData = res?.data?.mainForm ?? {};
            // self.dealData = res?.data?.handleDetails ?? {};
            // self.alarmsData = res?.data?.alarms ?? {};
            self.headInfo.title = self.basicWorkOrderData.sheetTitle;
            self.modifyForm.title = self.basicWorkOrderData.sheetTitle;
            //只有工单归属福建才能修改标题
            self.showModifyBtn =
              self.basicWorkOrderData.provinceCode == 213 ? true : false;
            if (sessionStorage.hasOwnProperty(self.basicWorkOrderData.woId)) {
              this.common.actionName = sessionStorage.getItem(
                self.basicWorkOrderData.woId
              );
            }

            self.headInfo.sheetId = self.basicWorkOrderData.sheetNo;
            // self.headInfo.currentHandler =
            //   self.basicWorkOrderData.operatePerson; // 当前处理人 改成从获取处理人列表接口获取
            self.headInfo.acceptTimeLimit =
              self.basicWorkOrderData.remainAcceptTime;
            self.headInfo.processTimeLimit =
              self.basicWorkOrderData.remainHandleTime;
            self.headInfo.sheetStatus = self.basicWorkOrderData.sheetStatusName;
            self.headInfo.buildSingleMan = self.basicWorkOrderData.senderName;
            self.headInfo.buildSingleDept =
              self.basicWorkOrderData.senderDeptName;
            self.headInfo.buildSingleTime =
              self.basicWorkOrderData.sheetCreateTime;
            this.getWorkOrderSpentTime(self.basicWorkOrderData); //工单总耗时
            self.headInfo.sourceInfo = self.basicWorkOrderData.createTypeName;
            self.headInfo.occurrenceTime =
              self.basicWorkOrderData.alarmCreateTime;
            self.headInfo.emergencyDegree =
              self.basicWorkOrderData.emergencyLevelName;
            // self.headInfo.faultRegion = self.basicWorkOrderData.faultRegion;

            self.common.failureTime = self.basicWorkOrderData.alarmCreateTime;
            self.common.failureInformTime =
              self.basicWorkOrderData.sheetCreateTime;
            //self.common.processNode = self.basicWorkOrderData.processNode;

            self.common.sheetNo = self.basicWorkOrderData.sheetNo;
            self.common.isSender =
              self.basicWorkOrderData.sender == self.userInfo.userName ? 1 : 0;
            self.common.agentManId = self.basicWorkOrderData.agentManId;
            self.common.agentMan = self.basicWorkOrderData.agentMan;
            self.common.agentDeptCode = self.basicWorkOrderData.agentDeptCode;
            self.common.agentDeptName = self.basicWorkOrderData.agentDeptName;
            self.common.copyDeptCode = self.basicWorkOrderData.copyDeptCode;
            self.common.copyDeptName = self.basicWorkOrderData.copyDeptName;

            self.common.copyManId = self.basicWorkOrderData.copyManId;
            self.common.copyMan = self.basicWorkOrderData.copyMan;
            self.common.sheetStatus = self.headInfo.sheetStatus;
            self.common.sheetCreateTime =
              self.basicWorkOrderData.sheetCreateTime;
            self.timing.hangTime = self.basicWorkOrderData.suspendDuration;
            // 转化枚举值
            self.common.professionalType =
              self.basicWorkOrderData.professionalType;
            self.common.professionalTypeName =
              self.basicWorkOrderData.professionalTypeName;
            self.common.networkType = self.basicWorkOrderData.networkTypeId;
            self.common.networkTypeName = self.basicWorkOrderData.networkType;
            self.common.hangOver = self.basicWorkOrderData.suspendDuration;
            self.common.faultCauseDescription =
              self.basicWorkOrderData.ppResult;

            // 根据网络类型立即检查智能体可用性
            self.checkAiAgentAvailabilityByNetworkType();

            self.handleData();

            // if (
            //   this.headInfo.sheetStatus == "待定性审核" ||
            //   this.headInfo.sheetStatus == "已归档" ||
            //   this.headInfo.sheetStatus == "待评价"
            // ) {
            //   // this.showFkdxq = false;
            //   this.$nextTick(() => {
            //     this.showFkdxq = true;
            //   });
            // } else {
            //   this.showFkdxq = false;
            // }
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.pageLoading = false;
          // 标记工单数据已加载完成
          this.workOrderDataLoaded = true;

          // 工单数据加载完成后，初始化智能体功能
          this.initAiAgentAfterDataLoaded();

          // 页面刷新完成后，发送工单信息变更消息给智能体
          this.$nextTick(() => {
            setTimeout(() => {
              this.sendWoMessageToAiAgent();
            }, 500); // 延迟500ms确保智能体已准备好接收消息
          });
        });
    },
    // 获取最新处理环节
    getProcessNode() {
      let param = {
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processNode: this.common.processNode,
        fromPage: this.$route.query.fromPage,
        actionName: this.common.actionName,
      };

      apiGetProcessNode(param).then(res => {
        if (res.data && res.data == "处理") {
          this.common.processNode = res.data;
        }
      });
    },
    handleData() {
      if (
        this.fromPage == "待办工单" ||
        this.fromPage == "在办工单" ||
        this.fromPage == "办结工单" ||
        this.fromPage == "工单查询"
      ) {
        this.getShowButton();
      }

      this.showClxq = false;
      this.showLcrz = false;
      this.showLct = false;
      this.showRelation = false;
      this.showAlarm = false;
      // this.showUpInfo = false;
      // this.showShareInfo = false;
      this.$nextTick(() => {
        if (this.basicWorkOrderData.createType == 2) {
          //手工派单工单来源为电子运维新建，没有关联诊断,告警详情根据接口判断
          this.getAlarmsData();
        } else {
          this.showAlarm = true;
        }
        if (
          this.basicWorkOrderData.professionalType == 7 ||
          this.basicWorkOrderData.professionalType == 3
        ) {
          this.showRelation = true;
        } else {
          if (this.basicWorkOrderData.createType == 2) {
            //手工派单工单来源为电子运维新建，没有关联诊断,告警详情根据接口判断
            this.showRelation = false;
          } else {
            this.getRelationDiagnosis();
          }
        }

        this.showClxq = true;
        this.showLcrz = true;
        this.showLct = true;
      });
      this.getMessageNum();
    },

    //获取处当前理人
    getCurrentHandler() {
      let param = {
        pageIndex: 1,
        pageSize: 10,
        // userIds: this.persons,
        woId: this.common.woId,
      };
      let self = this;
      apiGetPeerProcessorList(param)
        .then(res => {
          if (res.status == "200") {
            self.headInfo.currentHandler = res?.data?.operatePerson ?? "";
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //电子运维新建有的省分会生成虚拟告警列表，接口有数据就展示告警详情
    getAlarmsData() {
      let param = {
        pageIndex: 1,
        pageSize: 10,
        woId: this.common.woId,
      };
      apiQueryAlarmDetail(param)
        .then(res => {
          if (res.status == "0") {
            let total = res?.data?.total ?? 0;
            if (total != 0) {
              this.$nextTick(() => {
                this.showAlarm = true;
              });
            } else {
              this.showAlarm = false;
            }
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    //同步告警清除接口
    syncClearAlarm() {
      // this.processFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiSyncClear(param)
        .then(res => {
          // if (res.status == "0") {
          //   this.$message.success(res.msg);
          // } else {
          //   this.$message.error(res.msg);
          // }
          // this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          // this.$message.error(error.msg);
          // this.processFullscreenLoading = false;
        });
    },

    //获取反馈单数据，有数据展示无数据不展示该模块--
    getFeedbackData() {
      let param = {
        woId: this.common.woId,
        isEdit: 0, //isEdit    0:查看 1:编辑
      };
      apiqueryFeedback(param)
        .then(res => {
          console.log("============反馈单");
          console.log(res);

          if (res.status == 0) {
            let data = res?.data ?? [];
            if (data.length > 0) {
              this.$nextTick(() => {
                this.showFkdxq = true;
              });
            } else {
              this.showFkdxq = false;
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },

    //获取共建共享数据，有数据展示无数据不展示该模块--
    getShareData() {
      let realParam = {
        woId: this.common.woId,
      };
      apiGetShareInfo(realParam)
        .then(res => {
          if (res.status == "0") {
            let dealArr = res?.data?.dealOrderInfo ?? [];
            let confirmArr = res?.data?.confirmOrderInfo ?? [];
            // let tmp = res?.data ?? [];
            if (dealArr.length > 0 || confirmArr.length > 0) {
              this.$nextTick(() => {
                this.showShareInfo = true;
              });
            } else {
              this.showShareInfo = false;
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //共建共享展示审核
    showShareAudit(val) {
      this.opContent = val;
      if (val == 1) {
        this.dialogPendingReviewVisible = true;
      } else if (val == 2) {
        this.dialogSolutionToHangAuditVisible = true;
      }
    },

    //获取上传故障报告数据，有数据展示无数据不展示该模块--
    getUpFileData() {
      let param = {
        woId: this.common.woId,
      };
      apiQueryUploadFileDetail(param)
        .then(res => {
          if (res.status == "0") {
            let tmp = res?.data ?? [];
            if (tmp.length > 0) {
              this.$nextTick(() => {
                this.showUpInfo = true;
              });
            } else {
              this.showUpInfo = false;
            }
            // console.log(this.common.processInstId);
            // tmp.forEach(item => {
            //   if (item.processinstid == this.common.processInstId){
            //     this.reportId = item.linkid;
            //   }
            // });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //查询关联诊断是否显示--
    getRelationDiagnosis() {
      let param = {
        woId: this.common.woId,
      };

      apiGetRelationDiagnosis(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            if (res.data.length > 0) {
              let tmp = res.data[0];
              //显示
              //当 isPPShow为true 时显示关联诊断，
              // 当 isPPShow为false，isAIShow 为 true时显示上海ai
              // 都为false 则不呈现关联诊断版块；
              if (tmp.isPPShow || tmp.isRoomShow || tmp.isAIShow) {
                this.$nextTick(() => {
                  this.showRelation = true;
                });
              } else {
                self.showRelation = false;
              }
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //工单总耗时--
    getWorkOrderSpentTime(basicData) {
      if (
        basicData.sheetStatusName == "异常归档" ||
        basicData.sheetStatusName == "已归档" ||
        basicData.sheetStatusName == "作废"
      ) {
        let days = moment(
          basicData.sheetFilingTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      } else {
        let days = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      }
    },

    //查询是否可以返单操作
    canBack() {
      let param = {
        woId: this.common.woId,
        processInstId: this.common.processInstId,
      };
      apiBackSingleInit(param)
        .then(res => {
          if (res.status == "0") {
            // this.dialogBackSingleVisible = res?.data?.ifClearAlarm;
            this.fbTipVisible = false;

            if (!res?.data?.ifStation) {
              this.fbTipVisible = true;
              this.fbTipMsg = "请先进行【是否上站】的选择！";
              setTimeout(() => {
                this.fbTipVisible = false;
              }, 5000);
            } else if (!res?.data?.ifSign) {
              this.fbTipVisible = true;
              this.fbTipMsg = "上站处理必须进行【现场打点】操作！";
              setTimeout(() => {
                this.fbTipVisible = false;
              }, 5000);
            } else if (!res?.data?.ifClearAlarm) {
              this.$message.error(
                "主告警或追加告警还未清除不能返单！请先清除告警！"
              );
            } else {
              this.dialogBackSingleVisible = true;
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    buttonClick(name) {
      switch (name) {
        case "受理": //已调试
          this.accept(name);
          break;
        case "阶段反馈": //界面已调试，接口调试
          this.dialogStageFeedbackVisible = true;
          break;
        case "追单": //界面已调试，接口调试
          this.dialogAfterSingleVisible = true;
          break;
        case "返单": //界面已调试，接口调试
          this.canBack();
          break;
        case "资源勘误": //界面已调试，接口调试
          this.dialogZYKWVisible = true;
          break;
        case "是否上站": //界面已调试，接口调试
          this.dialogSZVisible = true;
          break;
        case "转派": //界面已调试，接口调试
          this.dialogTurnToSendVisible = true;
          break;
        case "异常终止": //界面已调试，接口待调试
          this.dialogAbendVisible = true;
          break;
        case "撤单": //界面已调试，接口待调试
          this.dialogRevokeVisible = true;
          break;
        case "挂起审核": //界面已调试，接口待调试
          this.opContent = 1;
          this.dialogPendingReviewVisible = true;
          break;
        case "解挂审核": //界面已调试，接口待调试
          this.opContent = 2;
          this.dialogSolutionToHangAuditVisible = true;
          break;
        case "异常终止审核": //界面已调试，接口待调试
          this.dialogAbendAuditVisible = true;
          break;
        case "一键催办":
          this.dialogOneKeyVisible = true;
          break;
        case "挂起": //界面已调试，接口待调试
          // this.dialogUpFFVisible = true;
          this.dialogHangUpVisible = true;
          break;
        case "解挂": //界面已调试，接口待调试
          this.dialogSolutionToHangVisible = true;
          break;
        case "转办": //界面已调试，接口待调试
          this.transferTodoDialogVisible = true;
          break;
        case "上传故障报告":
          this.dialogUpFFVisible = true;
          break;
        case "故障处理":
          this.dialogFaultHandleVisible = true;
          break;
        case "联通确认":
          this.dialogLTConfirmVisible = true;
          break;
        case "同步清除告警":
          this.syncClearAlarm();
          break;
      }
    },
    //受理操作-已调试--
    accept(buttonName) {
      this.processFullscreenLoading = true;
      sessionStorage.setItem(this.common.woId, "受理");
      let param = {
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        actionName: buttonName,
        processInstId: this.common.processInstId,
      };
      apiAccept(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success(res.msg);
            this.common.workItemId = res.data.workItemId;
            this.common.processDefId = res.data.processDefId;
            this.common.processInstId = res.data.processInstId;
            this.common.processNode = res.data.processNode;

            //根据新生成的workItemId再次调用显示按钮的接口
            this.getShowButton();
            this.getWorkOrderInfo();
            // 受理成功后，发送工单信息变更消息给智能体
            this.$nextTick(() => {
              setTimeout(() => {
                this.sendWoMessageToAiAgent();
              }, 2000); // 延迟1秒确保工单数据已更新且智能体已准备好接收消息
            });
          } else {
            this.$message.error(res.msg);
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
          this.processFullscreenLoading = false;
        });
    },

    openPeerProcessor() {
      this.dialogPeerProcessorVisible = true;
    },

    PeerProcessorClose() {
      this.dialogPeerProcessorVisible = false;
    },

    //故障报告审核提交完成--
    //审核成功
    dialogReportAuditSubmitClose() {
      this.closeAndTurnAround();
    },
    //追单关闭--
    dialogAfterSingleClose() {
      this.dialogAfterSingleVisible = false;
    },
    //追单提交--
    dialogAfterSingleSubmitClose(val) {
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogAfterSingleVisible = false;
      if (val == 1) {
        this.closeAndTurnAround();
      }
    },
    //返单关闭--
    dialogBackSingleClose() {
      this.dialogBackSingleVisible = false;
    },
    //返单提交--
    dialogBackSingleSubmitClose(data) {
      // this.common.workItemId = data.workItemId;
      // this.common.processInstId = data.processInstId;
      // this.common.processDefId = data.processDefId;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      sessionStorage.removeItem(this.common.woId);
      this.dialogBackSingleVisible = false;
      this.closeAndTurnAround();
    },
    //上传故障报告关闭--
    dialogUpFFClose() {
      this.dialogUpFFVisible = false;
    },
    //上传故障报告提交--
    dialogUpFFSubmitClose(val) {
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      this.dialogUpFFVisible = false;
      if (val == 1) {
        this.closeAndTurnAround();
      }
    },
    //定性审核提交--
    qualitativeReviewSubmit(data) {
      // this.common.workItemId = data.workItemId;
      // this.common.processInstId = data.processInstId;
      // this.common.processDefId = data.processDefId;
      // this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    //转办关闭
    dialogtransferTodoClose() {
      this.transferTodoDialogVisible = false;
    },
    //转办提交关闭--
    dialogTransferClose(val) {
      if (val == "1") {
        this.closeAndTurnAround();
        this.transferTodoDialogVisible = false;
      } else {
        this.transferTodoDialogVisible = false;
      }
    },
    //转派关闭--
    dialogTurnToSendClose() {
      this.dialogTurnToSendVisible = false;
    },
    //转派提交--
    dialogTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogTurnToSendVisible = false;
        sessionStorage.removeItem(this.common.woId);
        this.closeAndTurnAround();
      } else {
        this.dialogTurnToSendVisible = false;
      }
    },
    // 阶段反馈关闭--
    dialogStageFeedbackClose() {
      this.dialogStageFeedbackVisible = false;
    },
    // 阶段反馈提交--
    stageBackDialogCommitClose() {
      this.getWorkOrderInfo();
      this.dialogStageFeedbackVisible = false;
      // 智能体刷新工单信息
      this.sendWoMessageToAiAgent();
    },
    //撤单提交--
    handleRevokeSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.revokeSubmitLoading = true;
          let param = {
            woId: this.common.woId,
            workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            processNode: this.common.processNode,
            actionName: "撤单",
            sheetNo: this.common.sheetNo,
            reason: this.revokeForm.auditOpinion, //撤单原因
          };
          apiRevoke(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success(res.msg);
                this.dialogRevokeClose();
                this.closeAndTurnAround();
              } else {
                this.$message.error(res.msg);
              }
              this.revokeSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error(error.msg);
              this.revokeSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    //撤单重置--
    onResetRevoke() {
      this.revokeForm = {
        ...this.$options.data,
      };
    },
    //撤单关闭--
    dialogRevokeClose() {
      this.onResetRevoke();
    },
    //资源勘误关闭--
    dialogZYKWClose() {
      this.dialogZYKWVisible = false;
    },
    //资源勘误提交--
    dialogZYKWSubmitClose() {
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogZYKWVisible = false;
      // if (val == 1) {
      //   this.closeAndTurnAround();
      // }
    },

    //是否上站提交--
    handleSZSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.szSubmitLoading = true;
          let param = {
            woId: this.common.woId,
            // workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            // processNode: this.common.processNode,
            actionName: "是否上站",
            station: this.szForm.isSZ,
          };

          apiAccept(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success(res.msg);
                this.dialogSZClose();
                this.dialogSZVisible = false;
                //留在详情页 不跳转待办
                this.getWorkOrderInfo();
                // this.closeAndTurnAround();
              } else {
                this.$message.error(res.msg);
              }
              this.szSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error(error.msg);
              this.szSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    //是否上站重置--
    onResetSZ() {
      this.szForm = {
        isSZ: null,
      };
      this.showSZTip = false;
    },
    //是否上站关闭--
    dialogSZClose() {
      this.dialogSZVisible = false;
      this.onResetSZ();
    },
    szChange(val) {
      // console.log('上站变化'+val);
      if (val == "1") {
        this.showSZTip = true;
      } else {
        this.showSZTip = false;
      }
    },

    //修改主题提交
    handleModifySubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.modifySubmitLoading = true;
          let param = {
            woId: this.common.woId,
            title: this.modifyForm.title,
            // workItemId: this.common.workItemId,
            // processInstId: this.common.processInstId,
            // processNode: this.common.processNode,
            // actionName: "撤单",
            // sheetNo: this.common.sheetNo,
            // reason: this.revokeForm.auditOpinion, //撤单原因
          };
          apiModifyTitle(param)
            .then(res => {
              if (res.status == "200") {
                this.$message.success(res.msg);
                this.dialogModifyTitleClose();
                this.dialogModifyTitleVisiable = false;
                this.getWorkOrderInfo();
                // this.closeAndTurnAround();
              } else {
                this.$message.error(res.msg);
              }
              this.modifySubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error(error.msg);
              this.modifySubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    //修改主题重置
    onResetModify() {
      this.modifyForm = {
        title: "",
      };
    },
    //修改主题关闭--
    dialogModifyTitleClose() {
      this.onResetModify();
    },
    //挂起关闭--
    dialogHangUpClose() {
      this.dialogHangUpVisible = false;
    },
    //挂起提交--
    dialogHangUpSubmitClose() {
      this.dialogHangUpVisible = false;
      // this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //解挂关闭--
    dialogSolutionToHangClose() {
      this.dialogSolutionToHangVisible = false;
    },
    //解挂提交--
    dialogSolutionToHangSubmitClose() {
      this.dialogSolutionToHangVisible = false;
      this.closeAndTurnAround();
    },
    //挂起审核关闭--
    dialogPendingReviewClose() {
      this.dialogPendingReviewVisible = false;
    },
    //挂起审核提交--
    dialogPendingReviewSubmitClose() {
      this.dialogPendingReviewVisible = false;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      // this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    //解挂审核关闭--
    dialogSolutionToHangAuditClose() {
      this.dialogSolutionToHangAuditVisible = false;
    },
    //解挂审核提交--
    dialogSolutionToHangAuditSubmitClose() {
      this.dialogSolutionToHangAuditVisible = false;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      // this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    //异常终止关闭--
    dialogAbendClose() {
      this.dialogAbendVisible = false;
    },
    //异常终止提交--
    dialogAbendSubmitClose() {
      sessionStorage.removeItem(this.common.woId);
      this.dialogAbendVisible = false;
      // this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //异常终止审核关闭--
    dialogAbendAuditClose() {
      this.dialogAbendAuditVisible = false;
    },
    //异常终止审核提交--
    dialogAbendAuditSubmitClose() {
      this.processButtonArr = [];
      this.dialogAbendAuditVisible = false;
      // this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //故障处理关闭--
    dialogFaultHandleClose() {
      this.dialogFaultHandleVisible = false;
    },
    //故障处理提交关闭--
    dialogFaultHandleSubmitClose() {
      this.dialogFaultHandleVisible = false;
      this.closeAndTurnAround();
    },
    //联通确认关闭--
    dialogLTConfirmClose() {
      this.dialogLTConfirmVisible = false;
    },
    //联通确认提交关闭--
    dialogLTConfirmSubmitClose() {
      this.dialogLTConfirmVisible = false;
      this.closeAndTurnAround();
    },

    // 现场打点
    // submitLocationForm(formName) {
    //   this.$refs[formName].validate(valid => {
    //     if (valid) {
    //       const params = {
    //         woId: this.common.woId,
    //         processInstId: this.common.processInstId,
    //         actionName: "现场打点",
    //         longitude: this.locationForm.longitude,
    //         latitude: this.locationForm.latitude,
    //         manageuser: this.userInfo.userName,
    //         actualSceneSign: "否",
    //       };
    //       apiLocation(params)
    //         .then(res => {
    //           this.dialogLocationVisible = false;
    //           this.$message.success({
    //             message: "PC现场打点成功！",
    //           });
    //         })
    //         .catch(error => {
    //           this.$message.error(error.msg);
    //         });
    //     } else {
    //       console.log("error submit!!");
    //       return false;
    //     }
    //   });
    // },
    //已阅
    // haveRead() {
    //   this.processFullscreenLoading = true;
    //   let param = {
    //     woId: this.common.woId,
    //   };
    //   apiHaveRead(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.$message.success("已阅");
    //         this.closeAndTurnAroundRead();
    //       } else {
    //         this.$message.error("已阅失败");
    //       }
    //       this.processFullscreenLoading = false;
    //     })
    //     .catch(error => {
    //       console.log(error);
    //       this.$message.error("已阅失败");
    //       this.processFullscreenLoading = false;
    //     });
    // },

    //暂时没用
    getStatusStyle() {
      // if (
      //   this.common.sheetStatus == "待定性" ||
      //   this.common.sheetStatus == "待定性审核"
      // ) {
      //   return "color:#b50b14;border-color:#e9b6b9;background-color:#f8e7e8";
      // }
    },

    //修改主题
    modifyTitle() {
      this.dialogModifyTitleVisiable = true;
    },

    onHeadHandleClick(command) {
      this.$refs[command]?.$el?.scrollIntoView?.({ behavior: "smooth" });
    },
    showTime(val) {
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分";
      }
      return time;
    },
    //页面跳转
    closeAndTurnAround() {
      let self = this;

      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    closeAndTurnAroundRead() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toReadRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    //待阅进来变已阅--已调试--
    setReadStatus() {
      let params = {
        woId: this.common.woId,
      };
      apiSetRead(params)
        .then(res => {
          console.log(res.msg);
        })
        .catch(error => {});
    },

    // 外连接
    handleOuterLink() {
      let params = {
        userId: this.$store.getters.userInfo.userName,
        sheetNo: this.basicWorkOrderData.sheetNo,
        title: "",
      };

      apiGetRsaEncrypt(params)
        .then(res => {
          if (res.data != "") {
            this.rsaEncrypt = res.data;
            // this.rsaEncryptUrl = "/nfm3/chatOpsWeb/talk-view?" + res.data;
            this.rsaEncryptUrl =
              "http://10.245.0.121:5412/chatOpsWeb/talk-view?appKey=EOMS&params=" +
              res.data;
            this.chatopsVisible = true;
          } else {
            this.$message.error("获取rsa明文加密结果失败");
          }
        })
        .catch(error => {
          this.$message.error(error.msg);
        });
    },
    // chatops 未读条数
    getMessageNum() {
      let groupIds = [];
      groupIds.push(this.headInfo.sheetId);
      let paramsRsa = {
        userId: this.$store.getters.userInfo.userName,
        groupIds: groupIds,
      };
      apiMessageRsaEncrypt(paramsRsa)
        .then(res => {
          if (res.data != "") {
            let paramsChatops = {
              appKey: "EOMS",
              params: res.data,
            };
            apiGetMeassgeNum(paramsChatops)
              .then(resNum => {
                if (resNum.data != "") {
                  this.messageNum = resNum.data[this.headInfo.sheetId];
                  console.log(resNum.data);
                }
              })
              .catch(error => {
                console.log(error.msg);
              });
          } else {
            this.$message.error("获取rsa明文加密结果失败");
          }
        })
        .catch(error => {
          this.$message.error(error.msg);
        });
    },

    // 消息弹出框关闭之后，未读条数清零
    handleCloseMessage() {
      this.chatopsVisible = false;
      this.messageNum = 0;
    },

    // ==================== 全局智能体相关方法 ====================

    // 初始化全局智能体（不检查可用性，避免覆盖网络类型检查）
    initGlobalAiAgentWithoutAvailabilityCheck() {
      try {
        // 只设置路由信息，不检查可用性
        this.$store.commit("aiAgent/SET_CURRENT_ROUTE", this.$route);

        // 恢复用户偏好设置
        const savedPreferences = localStorage.getItem("aiAgent_preferences");
        if (savedPreferences) {
          try {
            const preferences = JSON.parse(savedPreferences);
            this.$store.commit("aiAgent/UPDATE_USER_PREFERENCES", preferences);
          } catch (error) {
            console.warn("Failed to parse AI agent preferences:", error);
          }
        }

        console.log("🤖 全局智能体已初始化（跳过可用性检查）");
      } catch (error) {
        console.error("Failed to initialize global AI agent:", error);
      }
    },
    // 设置智能体 postMessage 监听器
    setupAiAgentMessageListener() {
      // 直接使用箭头函数避免this绑定问题
      this.aiAgentMessageHandler = event => {
        this.handleAiAgentMessage(event);
      };
      window.addEventListener("message", this.aiAgentMessageHandler);
      console.log("🤖 智能体消息监听器已启动");
    },

    aiCommandQuery(command) {
      this.$nextTick(() => {
        this.$refs["glzd"].$refs["commandQuery"]?.$el?.scrollIntoView?.({
          behavior: "smooth",
        });
        let commandBtns = this.$refs["glzd"].$refs["commandQuery"].commandBtns;
        let selectedCommand = this.$refs["glzd"].$refs["commandQuery"]
          .selectedCommand;
        let handleCommand = commandBtns.filter(item => {
          return item.name == command;
        });

        if (handleCommand) {
          this.$refs["glzd"].$refs["commandQuery"].selectedCommand =
            handleCommand[0];
        }
      });
    },
    // 处理智能体发送的消息
    async handleAiAgentMessage(event) {
      debugger;
      // 过滤掉开发工具消息
      if (event.data && event.data.source === "react-devtools-content-script")
        return;
      if (event.data && event.data.source === "react-devtools-bridge") return;
      if (event.data && event.data.source === "react-devtools-detector") return;

      // 检查是否是智能体消息格式
      if (event.data && typeof event.data === "object") {
        // 检查是否包含智能体消息的特征字段
        if (
          event.data.action_name ||
          event.data.messageId !== undefined ||
          event.data.timestamp
        ) {
          console.group("🤖 智能体消息");
          console.log("消息来源:", event.origin);
          console.log("消息数据:", event.data);
          console.log("动作名称:", event.data.action_name || "未知");
          console.log("消息ID:", event.data.messageId || "无");
          console.log("消息载荷:", event.data.action_data);
          console.log(
            "时间戳:",
            event.data.timestamp
              ? new Date(event.data.timestamp).toLocaleString()
              : "无"
          );
          console.log("完整事件:", event);
          console.groupEnd();

          // ========== 新增：消息目标工单检查 ==========
          // 检查消息是否针对当前工单（除了initComplete消息，它总是被处理）
          if (event.data.action_name !== "initComplete") {
            const isTargetMessage = this.checkAiAgentMessageTarget(event.data);
            if (!isTargetMessage) {
              console.log("🤖 消息不针对当前工单，忽略处理");
              return;
            }
          }

          // 处理智能体动作
          switch (event.data.action_name) {
            case "CloseIframe":
              // 关闭全局智能体
              this.$store.dispatch("aiAgent/hideAiAgent");
              break;

            case "工单受理":
              // 工单受理 - 使用智能体返回的参数直接更新
              this.$message.success("工单受理成功");
              this.handleAiAgentAcceptSuccess(event.data.action_data);
              break;
            case "上站":
              // 刷新页面
              this.refreshPage();
              this.$message.success("是否上站处理成功！");
              break;
            case "现场打点":
              //  刷新页面
              this.refreshPage();
              this.$message.success("现场打点成功");
              break;

            case "阶段反馈":
              // 刷新页面
              // this.refreshPage();
              this.getWorkOrderInfo();
              this.$message.success("阶段反馈成功");
              break;

            case "资源勘误":
              // 刷新页面
              this.refreshPage();
              this.$message.success("资源勘误功能暂未上线。");
              break;

            case "二次诊断":
              // 页面滚动到“二次诊断”锚点
              this.refreshPage();
              this.onHeadHandleClick("glzd");
              break;

            case "转派":
              // 转派 - 使用现有的关闭方法
              this.closeAndTurnAround();
              this.$message.success("转派成功");
              // 关闭全局智能体
              this.$store.dispatch("aiAgent/hideAiAgent");
              break;

            case "异常终止":
              // 异常终止 - 使用现有的关闭方法
              this.closeAndTurnAround();
              this.$message.success("异常终止成功");
              // 关闭全局智能体
              this.$store.dispatch("aiAgent/hideAiAgent");
              break;

            case "挂起申请":
              // 挂起申请 - 使用现有的关闭方法
              this.closeAndTurnAround();
              this.$message.success("挂起申请成功");
              // 关闭全局智能体
              this.$store.dispatch("aiAgent/hideAiAgent");
              break;

            case "挂起":
              // 挂起申请 - 使用现有的关闭方法
              this.closeAndTurnAround();
              this.$message.success("挂起申请成功");
              // 关闭全局智能体
              this.$store.dispatch("aiAgent/hideAiAgent");
              break;

            case "解挂申请":
              // 解挂申请 - 使用现有的关闭方法
              this.closeAndTurnAround();
              this.$message.success("解挂申请成功");
              // 关闭全局智能体
              this.$store.dispatch("aiAgent/hideAiAgent");
              break;

            case "解挂":
              // 解挂申请 - 使用现有的关闭方法
              this.closeAndTurnAround();
              this.$message.success("解挂申请成功");
              // 关闭全局智能体
              this.$store.dispatch("aiAgent/hideAiAgent");
              break;

            case "返单":
              // 返单 - 使用现有的关闭方法
              this.closeAndTurnAround();
              this.$message.success("返单成功");
              // 关闭全局智能体
              this.$store.dispatch("aiAgent/hideAiAgent");
              break;
            case "流程诊断图":
              console.log("流程诊断图", this.$refs.lct);
              this.$refs.lct.$el.scrollIntoView({ behavior: "smooth" });
              break;

            case "woNewTab":
              // 打开新的工单Tab页
              if (event.data.action_data && event.data.action_data.sheet_no) {
                this.openNewWorkOrderTab(event.data.action_data.sheet_no);
              } else {
                // this.$message.error("缺少工单编号参数");
              }
              break;

            case "fetchLoaction":
              // 获取位置 - 发送当前位置信息给智能体
              this.sendLocationToAgent();
              break;

            case "initComplete":
              // 智能体初始化完成 - 发送工单参数数据给全局智能体
              this.sendInitDataToGlobalAgent();
              break;

            // 查看信息类型
            case "一次诊断推送":
              this.onHeadHandleClick("jcxx");
              break;
            case "查看故障的预处理信息":
              this.$refs["glzd"].$refs["zdfx"]?.$el?.scrollIntoView?.({
                behavior: "smooth",
              });
              break;

            case "查看诊断流程图":
              this.reciveNum = event.data?.action_data?.reciveNum;
              this.lastReciveNum++;
              this.$refs["glzd"].$refs["gzdw"]?.$el?.scrollIntoView?.({
                behavior: "smooth",
              });

              break;

            case "二次诊断":
              this.onHeadHandleClick("glzd");

              break;
            case "查活动告警":
              this.onHeadHandleClick("gjxq");
              break;

            // 8个状态指令，1、滚动到状态查询； 2、切换到对应的标签下
            case "查驻波比":
              this.aiCommandQuery("驻波比");
              break;
            case "查光功率":
              this.aiCommandQuery("光模块功率");
              break;
            case "查小区状态":
              this.aiCommandQuery("小区状态");
              break;
            case "查网元或设备历史告警":
              this.aiCommandQuery("历史告警");
              break;
            case "查网元或设备活动告警":
              this.aiCommandQuery("活动告警");
              break;
            case "查单板状态":
              this.aiCommandQuery("单板状态");
              break;
            case "查风扇转速温度":
              this.aiCommandQuery("风扇转速温度");
              break;
            case "查GPS状态":
              this.aiCommandQuery("GPS状态");
              break;

            default:
              console.log("未处理的智能体动作:", event.data.action_name);
              break;
          }
        }
      }
    },
    // 初始化全局智能体
    initGlobalAiAgent() {
      try {
        // 初始化全局智能体状态
        this.$store.dispatch("aiAgent/initAiAgent", this.$route);
        console.log("🤖 全局智能体已初始化");
      } catch (error) {
        console.error("Failed to initialize global AI agent:", error);
      }
    },

    // 工单数据加载完成后的智能体初始化
    async initAiAgentAfterDataLoaded() {
      try {
        // 更新全局智能体的工单数据
        await this.updateGlobalAiAgentData();

        console.log("🤖 全局智能体工单数据已更新:", {
          basicWorkOrderData: this.basicWorkOrderData,
          headInfo: this.headInfo,
          common: this.common,
        });

        // 如果智能体已经显示，立即发送初始化数据
        if (this.showAiAgent) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.sendInitDataToGlobalAgent();
              console.log("🤖 页面刷新后发送智能体初始化数据");
            }, 1000); // 延迟1秒确保智能体iframe已加载
          });
        }
      } catch (error) {
        console.error("Failed to update global AI agent data:", error);
      }
    },

    // 根据网络类型检查智能体可用性
    checkAiAgentAvailabilityByNetworkType() {
      // 如果专业类型还没有设置，隐藏智能体按钮
      if (!this.common.professionalType) {
        console.log("🤖 专业类型未设置，隐藏智能体按钮");
        this.$store.commit("aiAgent/SET_AI_AGENT_AVAILABLE", false);
        return;
      }

      // 只有当专业类型为 （7无线、4动环、3传输） 时才显示智能体按钮

      const isAvailable =
        this.common.professionalType == "3" ||
        this.common.professionalType == "4" ||
        this.common.professionalType == "7";

      // 直接设置智能体可用性状态
      this.$store.commit("aiAgent/SET_AI_AGENT_AVAILABLE", isAvailable);
    },
    // 获取智能体URL参数（通过URL query传递的基础参数）
    getAiAgentUrlParams() {
      // 获取主告警数据
      const majorAlarm = this.getMajorAlarmData();

      // 构建工单标题
      const workOrderTitle = `工单编号：${this.headInfo.sheetId || ""}`;

      // 构建工单描述
      const workOrderDescription = `[${
        this.basicWorkOrderData?.professionalType || "0000"
      }故障工单]${this.headInfo.title || ""}`;

      // 获取真实的网元名称（优先从告警数据获取）
      const networkElementName = this.getRealNetworkElementName();

      // 获取真实的厂商信息（优先从告警数据获取）
      const vendorName = this.getRealVendorName();

      // 获取真实的省份信息（优先从告警数据获取）
      const provinceName = this.getRealProvinceName();

      // 获取真实的经纬度信息（优先从告警数据获取）
      const coordinates = this.getRealCoordinates();

      const userInfoObj = JSON.parse(this.userInfo.attr2);

      let focusStatus = "";
      if (this.basicWorkOrderData.hasOwnProperty("focusStatus")) {
        focusStatus = this.basicWorkOrderData.focusStatus;
      }

      return {
        // 智能体场景标识（无线网专业）
        scene: "MBBFaultAgent",
        // 用户名称
        username: this.userInfo.realName || "",
        // 工单标题
        title: workOrderTitle,
        // 工单描述
        describe: workOrderDescription,
        // 组织ID
        orgId: this.userInfo.orgId || "",
        // 工单接口token
        ai_wo_token: this.token || "",
        // 工单号
        ai_wo_id: this.common.woId || "",
        // 工作项ID（步骤ID）
        ai_wo_step_id: this.common.workItemId || "",
        // 流程实例ID
        ai_wo_process_id: this.common.processInstId || "",
        // 流程模版ID
        ai_wo_prodef_id: this.common.processDefId || "",
        // 工单状态
        ai_wo_status: this.headInfo.sheetStatus || "",
        // 工单领域（专业中文）
        ai_wo_domain: this.basicWorkOrderData?.professionalTypeName || "无线网",
        // 客户端类型枚举：PC，MOBILE
        ai_client_type: "PC",
        // 告警唯一标识
        ai_alarm_uniqueid: majorAlarm?.alarmStaId || "",
        // 告警发生时间：2024-09-18 00:57:56
        ai_alarm_event_time: majorAlarm?.alarmCreateTime || "",
        // 网元名称
        ai_ne_name: networkElementName,
        // 厂商
        ai_vendor: vendorName,
        // 省份
        ai_province: provinceName,
        ai_province_id: "",
        // 用户实时经度
        ai_user_longitude: coordinates.longitude,
        // 用户实时纬度
        ai_user_latitude: coordinates.latitude,

        // ========== 新增参数 ==========
        // 对话显示的中文名称
        ai_user_name: this.userInfo.userName || "",
        ai_true_name: this.userInfo.realName || "",
        ai_user_id: this.userInfo.userId || userInfoObj.userId || "",

        ai_user_phone: userInfoObj.mobilePhone || "",
        // 强制上站勘误的状态标识，默认0。0是不强制，1是 强制
        ai_focus_status: focusStatus,
        // 工单大标题（工单标题）
        ai_wo_title: this.headInfo.title || "",
        // 工单小标题（工单编号）
        ai_wo_sheet_no: this.headInfo.sheetId || "",
        // 组织ID（与orgId保持一致）
        ai_org_id: userInfoObj.orgInfo.orgId || "",
        ai_wo_process_type:
          this.$route.query.fromPage == "待办工单" ? "待办" : "非待办",
        ai_wo_process_node: this.common.processNode,

        ai_org_name: userInfoObj.orgInfo.orgName || "",
      };
    },

    // 获取主告警数据
    getMajorAlarmData() {
      // 通过AlarmDetail组件获取告警数据
      const alarmDetailComponent = this.$refs.gjxq;
      if (alarmDetailComponent && alarmDetailComponent.tableData) {
        // 从告警列表中找到isMajorAlarm=是的主告警
        const majorAlarm = alarmDetailComponent.tableData.find(
          alarm => alarm.isMajorAlarm === "是"
        );
        return majorAlarm;
      }

      // 如果AlarmDetail组件还没有加载数据，返回null
      console.log("AlarmDetail component not ready or no alarm data available");
      return null;
    },

    // 获取真实的网元名称（优先从告警数据获取）
    getRealNetworkElementName() {
      // 1. 优先从主告警数据获取
      const majorAlarm = this.getMajorAlarmData();
      if (majorAlarm) {
        // 核心网专业优先使用neName，其次locateNeName
        if (majorAlarm.neName) {
          return majorAlarm.neName;
        }
        if (majorAlarm.locateNeName) {
          return majorAlarm.locateNeName;
        }
      }

      // 2. 从工单基础数据获取
      if (this.basicWorkOrderData) {
        if (this.basicWorkOrderData.neName) {
          return this.basicWorkOrderData.neName;
        }
        if (this.basicWorkOrderData.locateNeName) {
          return this.basicWorkOrderData.locateNeName;
        }
        if (this.basicWorkOrderData.deviceName) {
          return this.basicWorkOrderData.deviceName;
        }
      }

      return "";
    },

    // 获取真实的厂商信息（优先从告警数据获取）
    getRealVendorName() {
      // 1. 优先从主告警数据获取
      const majorAlarm = this.getMajorAlarmData();
      if (majorAlarm && majorAlarm.alarmVendor) {
        return majorAlarm.alarmVendor;
      }

      // 2. 从工单基础数据获取
      if (this.basicWorkOrderData) {
        if (this.basicWorkOrderData.commandVendor) {
          return this.basicWorkOrderData.commandVendor;
        }
        if (this.basicWorkOrderData.vendor) {
          return this.basicWorkOrderData.vendor;
        }
        if (this.basicWorkOrderData.deviceVendor) {
          return this.basicWorkOrderData.deviceVendor;
        }
      }

      return "";
    },

    // 获取真实的省份信息（优先从告警数据获取）
    getRealProvinceName() {
      // 1. 优先从主告警数据获取
      const majorAlarm = this.getMajorAlarmData();
      if (majorAlarm && majorAlarm.alarmProvince) {
        return majorAlarm.alarmProvince;
      }

      // 2. 从工单基础数据获取
      if (this.basicWorkOrderData) {
        if (this.basicWorkOrderData.province) {
          return this.basicWorkOrderData.province;
        }
        if (this.basicWorkOrderData.provinceName) {
          return this.basicWorkOrderData.provinceName;
        }
      }

      // 3. 从用户信息获取（如果有组织省份信息）
      if (this.userInfo && this.userInfo.provinceName) {
        return this.userInfo.provinceName;
      }

      return "";
    },

    // 获取真实的经纬度信息（优先从告警数据获取）
    getRealCoordinates() {
      // 1. 优先从主告警数据获取
      const majorAlarm = this.getMajorAlarmData();
      if (majorAlarm) {
        const longitude = majorAlarm.longitude || "";
        const latitude = majorAlarm.latitude || "";
        if (longitude && latitude) {
          return { longitude, latitude };
        }
      }

      // 2. 从工单基础数据获取
      if (this.basicWorkOrderData) {
        const longitude = this.basicWorkOrderData.longitude || "";
        const latitude = this.basicWorkOrderData.latitude || "";
        if (longitude && latitude) {
          return { longitude, latitude };
        }
      }

      return { longitude: "", latitude: "" };
    },

    // 根据不同专业获取网元名称（保留原方法以兼容）
    getNetworkElementName() {
      return this.getRealNetworkElementName();
    },

    // 调试方法：打印所有可用的数据源
    debugDataSources() {
      console.log("=== AI智能体参数数据源调试 ===");

      console.log(
        "1. 工单基础数据 (basicWorkOrderData):",
        this.basicWorkOrderData
      );
      console.log("2. 工单通用数据 (common):", this.common);
      console.log("3. 头部信息 (headInfo):", this.headInfo);
      console.log("4. 用户信息 (userInfo):", this.userInfo);

      // 告警数据
      const alarmDetailComponent = this.$refs.gjxq;
      if (alarmDetailComponent) {
        console.log("5. 告警组件数据 (AlarmDetail):", {
          tableData: alarmDetailComponent.tableData,
          alarmStaIdArr: alarmDetailComponent.alarmStaIdArr,
          alarmCreateTime: alarmDetailComponent.alarmCreateTime,
        });

        const majorAlarm = this.getMajorAlarmData();
        console.log("6. 主告警数据:", majorAlarm);
      } else {
        console.log("5. 告警组件未找到");
      }

      // 最终参数
      const finalParams = this.getAiAgentUrlParams();
      console.log("7. 最终AI智能体参数:", finalParams);

      console.log("=== 调试结束 ===");

      return finalParams;
    },

    // 检查是否为移动端
    checkMobile() {
      this.isMobile = window.innerWidth <= 768;
    },

    // 处理窗口大小变化
    handleResize() {
      this.checkMobile();
    },

    // 智能体动作处理方法 - 刷新页面数据
    refreshPage() {
      // 重新加载工单详情数据和按钮权限
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
        // 页面刷新完成后，发送工单信息变更消息给智能体
        this.$nextTick(() => {
          setTimeout(() => {
            this.sendWoMessageToAiAgent();
          }, 500); // 延迟500ms确保智能体已准备好接收消息
        });
      });
    },
    // 处理智能体返回的受理成功操作
    handleAiAgentAcceptSuccess(actionData) {
      try {
        console.log(
          "🤖 处理智能体受理成功操作，使用返回的参数直接更新:",
          actionData
        );

        // 直接使用智能体返回的参数更新 common 对象
        if (actionData) {
          if (actionData.workItemId) {
            this.common.workItemId = actionData.workItemId;
            console.log("🤖 更新 workItemId:", actionData.workItemId);
          }
          if (actionData.processDefId) {
            this.common.processDefId = actionData.processDefId;
            console.log("🤖 更新 processDefId:", actionData.processDefId);
          }
          if (actionData.processInsId) {
            this.common.processInstId = actionData.processInsId;
            console.log("🤖 更新 processInstId:", actionData.processInsId);
          }
          if (actionData.woId) {
            this.common.woId = actionData.woId;
            console.log("🤖 更新 woId:", actionData.woId);
          }
          if (actionData.networkType) {
            this.common.networkType = actionData.networkType;
            console.log("🤖 更新 networkType:", actionData.networkType);
          }
          this.common.processNode = "处理";

          // 根据新生成的workItemId再次调用显示按钮的接口
          this.getShowButton().then(() => {
            // 再次获取工单详情信息（确保所有数据都是最新的）
            this.getWorkOrderInfo();

            // 受理成功后，发送工单信息变更消息给智能体
            this.$nextTick(() => {
              setTimeout(() => {
                this.sendWoMessageToAiAgent();
              }, 1000); // 延迟1秒确保工单数据已更新且智能体已准备好接收消息
            });
          });
        } else {
          console.warn("🤖 智能体未返回有效的参数数据，使用常规刷新方式");
          this.refreshPage();
        }
      } catch (error) {
        console.error("🤖 处理智能体受理成功操作失败:", error);
        this.refreshPage();
      }
    },

    openNewWorkOrderTab(sheetNo) {
      // 打开新的工单Tab页
      if (!sheetNo) {
        this.$message.error("工单编号不能为空");
        return;
      }

      // 构建新工单的路由
      const route = {
        name: "backbone_commonWoDetail",
        params: {
          sheetId: sheetNo,
        },
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          ...this.$route.query,
        },
      };

      // 在新标签页中打开
      const routeData = this.$router.resolve(route);
      window.open(routeData.href, "_blank");
    },

    sendLocationToAgent() {
      // 发送当前位置信息给全局智能体
      try {
        const locationData = {
          action: "locationResponse",
          data: {
            // 这里可以添加具体的位置信息
            currentPage: "workOrderDetail",
            sheetId: this.headInfo.sheetId,
            timestamp: Date.now(),
          },
        };

        // 使用全局智能体通信服务
        if (this.$globalAiAgent && this.$globalAiAgent.isAvailable()) {
          const success = this.$globalAiAgent.sendMessage(locationData);
          if (success) {
            console.log("🤖 通过全局智能体服务发送位置信息:", locationData);
            return;
          }
        }

        console.warn("全局智能体服务不可用，无法发送位置信息");
      } catch (error) {
        console.error("发送位置信息失败:", error);
      }
    },
    // 更新全局智能体数据
    async updateGlobalAiAgentData() {
      try {
        // 直接更新智能体数据，不调用checkAiAgentAvailability（避免覆盖网络类型检查）
        await this.updateGlobalAiAgentDataWithoutAvailabilityCheck({
          route: this.$route,
        });

        console.log("🤖 全局智能体数据已更新（跳过可用性检查）");
      } catch (error) {
        console.error("Failed to update global AI agent data:", error);
      }
    },
    // 更新全局智能体数据（不检查可用性，避免覆盖网络类型检查）
    async updateGlobalAiAgentDataWithoutAvailabilityCheck({ route }) {
      try {
        // 更新当前路由
        this.$store.commit("aiAgent/SET_CURRENT_ROUTE", route);

        // 只有在智能体可用时才更新URL和配置
        if (this.isAiAgentAvailable) {
          // 使用配置文件中的智能体URL（支持环境自动切换）
          const aiAgentUrl =
            this.$store.getters["aiAgent/aiAgentUrl"] ||
            require("@/config/aiAgent").getAiAgentUrl();

          // 更新智能体URL和配置
          this.$store.commit("aiAgent/SET_AI_AGENT_URL", aiAgentUrl);
          this.$store.commit(
            "aiAgent/SET_AI_AGENT_CONFIG",
            require("@/config/aiAgent").AI_AGENT_CONFIG
          );

          console.log("🤖 全局智能体数据已更新（不检查可用性）:", {
            url: aiAgentUrl,
            environment: process.env.NODE_ENV,
            note: "跳过可用性检查，保持网络类型检查结果",
          });
        }
      } catch (error) {
        console.error(
          "Failed to update AI agent data without availability check:",
          error
        );
        this.$store.commit("aiAgent/SET_AI_AGENT_ERROR", true);
      }
    },

    // 发送初始化数据给全局智能体
    sendInitDataToGlobalAgent() {
      try {
        // ========== 关键检查：只有当前激活的页面才能发送消息 ==========
        const isCurrentPageActive = this.checkIfCurrentPageIsActive();
        if (!isCurrentPageActive) {
          console.log("🤖 当前页面不是激活状态，跳过发送智能体数据", {
            pageInstanceId: this.pageInstanceId,
            woId: this.common.woId,
            isCurrentPageActive: this.isCurrentPageActive,
          });
          return;
        }

        // 获取智能体URL参数数据
        const initData = this.getAiAgentUrlParams();

        const responseMessage = {
          action_name: "initComplete",
          action_data: initData,
        };

        console.log("🤖 当前页面是激活状态，准备发送智能体数据:", {
          pageInstanceId: this.pageInstanceId,
          woId: this.common.woId,
          sheetNo: this.headInfo.sheetId,
          message: responseMessage,
        });

        // 使用全局智能体通信服务
        if (this.$globalAiAgent && this.$globalAiAgent.isAvailable()) {
          const success = this.$globalAiAgent.sendMessage(responseMessage);
          if (success) {
            console.log(
              "🤖 通过全局智能体服务发送初始化数据成功:",
              responseMessage
            );
            return;
          }
        }

        console.warn("全局智能体服务不可用，无法发送初始化数据");
      } catch (error) {
        console.error("发送初始化数据失败:", error);
      }
    },

    // 发送工单信息变更消息给智能体
    sendWoMessageToAiAgent() {
      try {
        // ========== 关键检查：只有当前激活的页面才能发送消息 ==========
        const isCurrentPageActive = this.checkIfCurrentPageIsActive();
        if (!isCurrentPageActive) {
          console.log("🤖 当前页面不是激活状态，跳过发送工单变更消息", {
            pageInstanceId: this.pageInstanceId,
            woId: this.common.woId,
            isCurrentPageActive: this.isCurrentPageActive,
          });
          return;
        }
        // 页面刷新：需要更新的参数，不能来自于url
        // ai_wo_process_id、ai_wo_step_id、ai_wo_status、ai_wo_token、ai_wo_process_node、ai_wo_process_type参数
        // 构建工单变更消息
        const responseMessage = {
          action_name: "sendWoMessage",
          action_data: {
            ai_wo_process_id: this.common.processInstId, // url中获取 processInstId
            ai_wo_step_id: this.common.workItemId, // url中获取workltemld
            ai_wo_status: this.headInfo.sheetStatus,
            ai_wo_token: this.token,
            ai_wo_process_node: this.common.processNode,
            ai_wo_process_type:
              this.$route.query.fromPage == "待办工单" ? "待办" : "非待办",
            // // 包含TOKEN和其他参数，与initComplete保持一致
            // ...this.getAiAgentUrlParams(),
          },
        };

        console.log("🤖 准备发送工单变更消息给智能体:", {
          pageInstanceId: this.pageInstanceId,
          woId: this.common.woId,
          sheetNo: this.headInfo.sheetId,
          sheetStatus: this.headInfo.sheetStatus,
          message: responseMessage,
        });

        // 使用全局智能体通信服务
        if (this.$globalAiAgent && this.$globalAiAgent.isAvailable()) {
          const success = this.$globalAiAgent.sendMessage(responseMessage);
          if (success) {
            console.log(
              "🤖 通过全局智能体服务发送工单变更消息成功:",
              responseMessage
            );
            return;
          }
        }

        console.warn("全局智能体服务不可用，无法发送工单变更消息");
      } catch (error) {
        console.error("发送工单变更消息失败:", error);
      }
    },
    // ========== 新增：多工单消息过滤相关方法 ==========

    // 设置当前活跃工单信息
    setCurrentActiveWorkOrder() {
      const workOrderInfo = {
        woId: this.common.woId,
        sheetNo: this.headInfo.sheetId || this.common.sheetNo,
        pageInstanceId: this.pageInstanceId,
      };

      this.$store.dispatch("aiAgent/setCurrentActiveWorkOrder", workOrderInfo);
    },

    // 检查智能体消息是否针对当前工单
    checkAiAgentMessageTarget(messageData) {
      // 从消息中提取工单信息
      const messageWorkOrder = {
        woId: messageData.action_data?.ai_wo_id || messageData.woId,
        sheetNo: messageData.action_data?.ai_sheet_no || messageData.sheetNo,
      };

      // ========== 关键修复：检查当前页面是否为活跃tab ==========
      const isCurrentPageActive = this.checkIfCurrentPageIsActive();

      console.log("🤖 页面活跃状态检查:", {
        isCurrentPageActive,
        currentWoId: this.common.woId,
        currentSheetNo: this.headInfo.sheetId,
        messageWorkOrder: messageWorkOrder,
        pageInstanceId: this.pageInstanceId,
      });

      // 如果当前页面不是活跃tab，直接忽略所有消息（除了initComplete）
      if (!isCurrentPageActive) {
        console.log("🤖 当前页面不是活跃tab，忽略消息处理");
        return false;
      }

      // 如果消息没有指定目标工单，且当前页面是活跃的，则处理消息（向后兼容）
      if (!messageWorkOrder.woId && !messageWorkOrder.sheetNo) {
        console.log("🤖 消息未指定目标工单，当前页面是活跃tab，处理消息");
        return true;
      }

      // 检查工单ID或工单编号是否匹配
      const woIdMatch =
        this.common.woId &&
        messageWorkOrder.woId &&
        this.common.woId === messageWorkOrder.woId;
      const sheetNoMatch =
        (this.headInfo.sheetId || this.common.sheetNo) &&
        messageWorkOrder.sheetNo &&
        (this.headInfo.sheetId === messageWorkOrder.sheetNo ||
          this.common.sheetNo === messageWorkOrder.sheetNo);

      const isMatch = woIdMatch || sheetNoMatch;

      console.log("🤖 消息目标工单检查:", {
        currentWoId: this.common.woId,
        currentSheetNo: this.headInfo.sheetId || this.common.sheetNo,
        messageWorkOrder: messageWorkOrder,
        isMatch,
        woIdMatch,
        sheetNoMatch,
        isCurrentPageActive,
      });

      return isMatch;
    },

    // 检查当前页面是否为活跃的tab页面
    checkIfCurrentPageIsActive() {
      try {
        // 主要依赖我们在生命周期中设置的状态
        const isActiveByLifecycle = this.isCurrentPageActive;

        // 辅助检查：当前路由是否匹配
        const currentRoute = this.$route;
        let routeMatches = false;
        if (currentRoute && currentRoute.query) {
          const routeWoId = currentRoute.query.woId;
          routeMatches = routeWoId === this.common.woId;
        }

        // ========== 新增：检查当前页面是否为Vuex中的活跃工单 ==========
        const currentActiveWorkOrder = this.$store.getters[
          "aiAgent/currentActiveWorkOrder"
        ];
        const isActiveInStore =
          currentActiveWorkOrder &&
          currentActiveWorkOrder.woId === this.common.woId &&
          currentActiveWorkOrder.pageInstanceId === this.pageInstanceId;

        // 综合判断：生命周期状态 + 路由匹配 + Vuex状态匹配
        const isActive = isActiveByLifecycle && routeMatches && isActiveInStore;

        console.log("🤖 页面活跃状态检查:", {
          isActiveByLifecycle,
          routeMatches,
          isActiveInStore,
          isActive,
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          pageInstanceId: this.pageInstanceId,
          currentActiveWorkOrder,
        });

        return isActive;
      } catch (error) {
        console.error("检查页面活跃状态失败:", error);
        // 出错时返回生命周期状态
        return this.isCurrentPageActive;
      }
    },
  },
};
</script>
<style lang="scss">
.szTipCla {
  background-color: #fef0f0 !important;
  color: #f56c6c !important;
  padding: 10px 17px !important;
  border: none !important;

  i {
    margin-right: 3px;
  }
  .popper__arrow::after {
    border-bottom-color: #fef0f0 !important;
  }
}
</style>
<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    position: relative;
    font-size: 18px;
    line-height: 28px;
    font-weight: 700;
    display: flex;

    .text-overflow {
      max-width: calc(100% - 30px) !important;
      width: auto;
    }
  }

  .modifyT {
    width: 30px;
    height: 28px;
    border: 0px !important;
    background: url("../../../assets/xiugaiziliao.png") center no-repeat;
    background-size: 70% 70%;
  }

  .head-handle-wrap {
    text-align: right;
    .more-dropdown {
      padding: 0;
      @include themify() {
        border: none;
        border-left: 1px solid themed("$--button-default-border-color");
      }
    }
    .btnleft__group {
      margin-left: 5px;
      margin-bottom: 0px;
      margin-top: 8px;
    }
  }

  .divider {
    margin: 10px 0 16px;
  }
  .head-info2 {
    p {
      margin: 0 auto 5px;
    }
  }
  .head-sheetId {
    font-size: 18px;
    line-height: 28px;
  }
  .head-up-down {
    text-align: center;
    padding-left: 0px !important;
    padding-right: 0px !important;
    margin-left: 0px !important;
    margin-right: 0px !important;
    & > div:first-child {
      line-height: 20px;
      @include themify() {
        color: themed("$--color-text-regular");
      }
    }
    & > div:last-child {
      font-weight: 400;
      font-size: 15px;
      line-height: 24px;
    }
  }
  .card-title {
    font-weight: 400;
    font-size: 18px;
    box-shadow: 0 0 5px #aaa;
    padding-left: 10px;
  }
}
::v-deep .detail-dialog .el-dialog__body {
  padding: 20px;
}

.title-hd {
  font-size: 24px;
  line-height: 28px;
  font-weight: 700;
  overflow: hidden !important;
}

.expande {
  overflow: hidden !important;
  height: auto;
  // display: inline;
}

.close-title {
  overflow: hidden;
  height: 56px;
}

.expande-button-wrap {
  position: absolute;
  bottom: 0;
  right: -12px;
  height: 28px;
  background: white;
}

.expande-button {
  text-align: right;
  vertical-align: middle;
  line-height: 28px;
  padding-left: 3px;
}
.expande-button i {
  vertical-align: bottom;
  color: #b50b14;
}

/* 全局智能体激活时的页面样式调整 */
.common-wo-detail-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;

  // 页面内容正常显示，宽度控制由全局Layout层处理
  .draft-eidt-page {
    width: 100%;
    transition: all 0.3s ease;
  }

  // 移动端处理
  &.mobile {
    .draft-eidt-page {
      overflow-x: auto;
    }
  }
}
.outer-link {
  position: absolute;
  top: 55%;
  right: 17px;
  z-index: 99;
  padding: 3px 0px 0px 0px;
  border-radius: 999px;
  box-shadow: 0 0 8px #999;
  background: rgba(255, 255, 255, 0.7);
  width: 56px;
  height: 56px;

  .icon {
    display: block;
    width: 36px;
    height: 36px;
    cursor: pointer;
    background: url("../../../assets/icon_chatops.png") center no-repeat;
    background-size: 68% 60%;
    margin-left: 10px;
  }
  .num {
    position: absolute;
    top: -10px;
    right: -5px;
    padding: 2px 8px;
    color: #fff;
    background: #b50b14;
    border-radius: 999px;
  }
  .name {
    color: #b50b14;
    font-size: 11px;
    position: absolute;
    margin-top: -4px;
    font-weight: 500;
    text-align: center;
    width: 56px;
  }
}
</style>
