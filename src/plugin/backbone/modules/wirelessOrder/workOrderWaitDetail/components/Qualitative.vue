<template>
  <div class="qualitative">
    <el-form
      ref="qualitativeForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeForm"
      label-width="130px"
      :rules="qualitativeFormRule"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障所属专业:"
              prop="professionalType"
              :rules="{
                required: true,
                message: '请选择故障所属专业',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.professionalType"
                :dictId="10002"
                style="width: 100%"
                :notSelect="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障等级:"
              prop="faultLevel"
              :rules="{
                required: true,
                message: '请选择故障等级',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.faultLevel"
                :dictId="70002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8"></el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ qualitativeForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ qualitativeForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障恢复时间:" prop="busRecoverTime">
              <el-date-picker
                v-model="qualitativeForm.busRecoverTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                @change="computerBusRecoverDuration"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.qualitativeForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" required>
              {{ second2Time(this.qualitativeForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:" required>
              {{ second2Time(this.qualitativeForm.processDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障恢复历时:" required>
              {{ second2Time(qualitativeForm.busRecoverDuration) }}
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="是否超时:" prop="isOverTime">
              <el-radio-group
                disabled
                v-model="qualitativeForm.isOverTime"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="故障处理部门:" prop="dept" required>
              {{ qualitativeForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ qualitativeForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人电话:" prop="personPhone" required>
              {{ qualitativeForm.personPhone }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否影响业务:" prop="isEffectBusiness">
              <el-radio-group
                v-model="qualitativeForm.isEffectBusiness"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否铁塔原因:" prop="isTowerFault">
              <el-radio-group
                v-model="qualitativeForm.isTowerFault"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="是否基站退服:"
              prop="isSiteOffline"
              :rules="{
                required: true,
                message: '请选择是否基站退服',
              }"
            >
              <el-radio-group
                v-model="qualitativeForm.isSiteOffline"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="退服原因:"
              v-if="qualitativeForm.isSiteOffline == '1'"
              prop="siteOfflineReason"
              :rules="{
                required: qualitativeForm.isSiteOffline == '1' ? true : false,
                message: '请选择退服原因',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.siteOfflineReason"
                :dictId="10016"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障处理方式:"
              prop="faultHandleType"
              :rules="{
                required: true,
                message: '请选择故障处理方式',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.faultHandleType"
                :dictId="70004"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="同行处理人:" prop="withinPerson">
              <el-input v-model="qualitativeForm.withinPerson">
                <template v-for="(tag, index) in organizeForm.withinPersonList">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('withPerson', tag)"
                    v-show="index < 1"
                    v-if="tag.type == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.withinPersonList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.withinPersonName"
                    placeholder="请输入同行处理人姓名"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('withinPerson')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('withinPerson')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.withinPersonListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('withPerson', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('withPerson')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 3px">
                    +{{ organizeForm.withinPersonList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('withinPerson')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否需要定性审核:"
              prop="needCheck"
              style="white-space: nowrap"
            >
              <el-radio-group
                v-model="qualitativeForm.needCheck"
                style="width: 100%"
              >
                <el-radio :disabled="this.isCheckDisable" label="0"
                  >否</el-radio
                >
                <el-radio :disabled="this.isCheckDisable" label="1"
                  >是</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              :rules="{
                required: true,
                message: '请选择故障分类',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.faultCate"
                :dictId="70005"
                style="width: 100%"
                placeholder="请选择内容"
                @change="changeFaultCate"
              />
            </el-form-item>
          </el-col>
          <!-- 故障厂家字段 - 仅在故障分类为"基站设备"或"传输系统"时显示 -->
          <el-col :span="8" v-if="showVendorField">
            <el-form-item
              label="故障厂家:"
              prop="faultVendor"
            >
              <el-select
                v-model="qualitativeForm.faultVendor"
                placeholder="请选择故障厂家"
                style="width: 100%"
              >
                <el-option
                  v-for="item in vendorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障原因:"
              prop="faultReason"
              :rules="{
                required: true,
                message: '请选择故障原因',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.faultReason"
                :dictId="dictTypeCode"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
          <!-- 设备类型字段 -->
          <el-col :span="8">
            <el-form-item
              label="设备类型:"
              prop="deviceType"
              :rules="{
                required: isUnicomToUnicom,
                message: '请选择设备类型',
              }"
            >
              <el-select
                v-model="qualitativeForm.deviceType"
                placeholder="请选择设备类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in deviceTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 设备名称字段 -->
          <el-col :span="8">
            <el-form-item
              label="设备名称:"
              prop="deviceName"
              :rules="{
                required: isUnicomToUnicom,
                message: '请输入设备名称',
              }"
            >
              <el-input
                v-model="qualitativeForm.deviceName"
                placeholder="请输入设备名称"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="附件:" prop="attachmentName">
              <el-tag
                class="fileName_style_download"
                closable
                v-for="(item, index) in fddxFileArr"
                :key="index"
                @close="closeAndDeleteFile(item)"
                @click="downloadAppendixFile(item)"
                :title="item.name"
              >
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-tag
                class="fileName_style"
                closable
                v-for="(item, index) in importForm.relatedFilesFileList"
                :key="index"
                @close="closeFile(item)"
                :title="item.name"
              >
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                >+上传附件</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障原因描述:" prop="faultReasonDesc">
              <el-input
                maxlength="500"
                show-word-limit
                type="textarea"
                :rows="2"
                v-model="qualitativeForm.faultReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注:" prop="faultComment">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeForm.faultComment"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button type="primary" v-if="sumBtnShow" @click="nextStepEvaluation()"
        >下一步</el-button
      >
      <el-button type="primary" v-else @click="handleSubmit('qualitativeForm')"
        >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>

    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="relatedFilesDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <el-dialog
      width="620px"
      title="满意度评价"
      :visible.sync="evaluationDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
    >
      <el-form ref="evaluation" :model="evaluation" label-width="120px">
        <el-form-item label="工单准确性:">
          <el-rate
            v-model="evaluation.orderAccuracy"
            :colors="colors"
            style="display: inline-block; vertical-align: text-bottom"
          >
          </el-rate>
        </el-form-item>
        <el-form-item label="诊断准确性:">
          <el-rate
            v-model="evaluation.diagnosticrAccuracy"
            :colors="colors"
            style="display: inline-block; vertical-align: text-bottom"
          >
          </el-rate>
        </el-form-item>
        <el-form-item label="中台评价:">
          <el-rate
            v-model="evaluation.middleGroundRate"
            :colors="colors"
            style="display: inline-block; vertical-align: text-bottom"
          >
          </el-rate>
        </el-form-item>
        <el-form-item label="派单及时性:">
          <el-rate
            v-model="evaluation.sendTimely"
            :colors="colors"
            style="display: inline-block; vertical-align: text-bottom"
          >
          </el-rate>
        </el-form-item>

        <el-form-item
          v-if="
            evaluation.orderAccuracy < 3 ||
            evaluation.diagnosticrAccuracy < 3 ||
            evaluation.sendTimely < 3 ||
            evaluation.middleGroundRate < 3
          "
          label="反馈问题:"
          prop="feedbackProblemCheckList"
          :rules="[
            {
              required:
                evaluation.orderAccuracy >= 3 &&
                evaluation.diagnosticrAccuracy >= 3 &&
                evaluation.sendTimely >= 3 &&
                evaluation.middleGroundRate >= 3
                  ? false
                  : true,
              message: '请选择要反馈的问题',
              trigger: 'blur',
            },
          ]"
        >
          <el-checkbox-group
            v-model="evaluation.feedbackProblemCheckList"
            @change="feedbackChange"
          >
            <el-checkbox label="工单基础信息错误"></el-checkbox>
            <el-checkbox label="诊断信息不完整"></el-checkbox>
            <el-checkbox label="诊断信息错误"></el-checkbox>
            <el-checkbox label="其他"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          v-if="
            viewsOnContentShow &&
            (evaluation.orderAccuracy <= 3 ||
              evaluation.diagnosticrAccuracy <= 3 ||
              evaluation.sendTimely <= 3 ||
              evaluation.middleGroundRate <= 3)
          "
          prop="viewsOnContent"
          :rules="{
            required: true,
            message: '请填写内容',
          }"
        >
          <el-input
            type="textarea"
            :rows="3"
            placeholder="为了下次给您更好的服务，请留下宝贵意见，对您和我们都很重要"
            v-model="evaluation.viewsOnContent"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button
          type="primary"
          @click="handleSubmit('evaluation')"
          v-loading.fullscreen.lock="qualitativeFullscreenLoading"
          >提 交</el-button
        >
        <el-button @click="nextStepEvaluation()">上一步</el-button>
      </span>
    </el-dialog>

    <dia-tissue-tree
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      :showContactUserTab="diaPeople.showContactUserTab"
      :multiple-select-enable="diaPeople.multipleSelectEnable"
      :single-select-tip="diaPeople.singleSelectTip"
      :professionalType="mainProfessionalType"
      :checkUserLimit="checkUserLimit"
      @on-save="onSavePeople"
      :appendToBody="true"
      :userAttribution="userAttribution"
    />
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import DictSelect from "../../../workOrder/components/DictSelect.vue";
import {
  apiWirelessQualitative,
  apiGetEvaluation,
  apiqueryFeedback,
  apiDownloadAppendixFile,
  apiDeleteFdFile,
} from "../api/CommonApi";
import FileUpload from "../../../workOrder/components/FileUpload.vue";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";
import { mixin } from "../../../../../../mixins";
export default {
  name: "BackSingle",
  props: {
    common: Object,
    timing: Object,
    // 接收父组件传递的告警设备信息
    alarmDeviceVendor: String, // 告警设备厂家
    alarmDeviceType: String, // 告警设备类型
    alarmDeviceName: String, // 告警设备名称
  },
  components: { DictSelect, FileUpload, DiaTissueTree },
  computed: {
    ...mapGetters(["userInfo"]),
    // 是否为联通派联通模式
    isUnicomToUnicom() {
      return this.testDispatchMode === 'unicom_to_unicom';
    },
    // 是否显示故障厂家字段
    showVendorField() {
      // 仅在故障分类为"基站设备"或"传输系统"时显示
      const faultCateValue = this.qualitativeForm.faultCate;
      console.log('faultCateValue:', faultCateValue);
      return faultCateValue === '基站设备' || faultCateValue === '传输系统' || faultCateValue === '1';
    },
    // 根据故障分类获取对应的厂家选项
    vendorOptions() {
      const faultCateValue = this.qualitativeForm.faultCate;
      if (faultCateValue === '基站设备' || faultCateValue === '1') {
        return this.baseStationVendorOptions;
      } else if (faultCateValue === '传输系统') {
        return this.transmissionVendorOptions;
      }
      return [];
    },
  },
  mixins: [mixin],
  data() {
    var validBusRecoverTime = (rule, value, callback) => {
      if (this.qualitativeForm.busRecoverTime) {
        let seconds3 = moment(
          this.qualitativeForm.busRecoverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        let seconds4 = moment(
          this.qualitativeForm.busRecoverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds3 > 0 || seconds4 <= 0) {
          callback(
            new Error(
              "当前时间>=故障恢复时间>故障发生时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      } else {
        callback(new Error("请选择故障恢复时间"));
      }
    };
    var validAttachment = (rule, value, callback) => {
      if (this.common.isUploadReport == "1") {
        if (
          this.importForm.relatedFilesFileList.length > 0 ||
          this.fddxFileArr.length > 0
        ) {
          this.qualitativeForm.attachmentName = "xlFile";
          callback();
        } else {
          this.qualitativeForm.attachmentName = null;
          callback(new Error("请添加相关附件"));
        }
      } else {
        callback();
      }
    };
    return {
      userAttribution: "withPerson",
      mainProfessionalType: "7",
      sumBtnShow: true,
      qualitativeForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        busRecoverTime: null, //故障恢复时间
        busRecoverDuration: 0, //业务恢复历时
        faultDuration: 0, //故障处理历时
        isOverTime: null,
        dept: null,
        person: null,
        personPhone: null,
        suspendDuration: 0, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        isEffectBusiness: null,
        isTowerFault: null,
        isSiteOffline: null,
        siteOfflineReason: null,
        faultHandleType: null,
        withinPerson: null,
        //故障专业信息
        faultCate: null,
        faultReason: null,
        relatedFiles: null,
        faultReasonDesc: this.common.faultCauseDescription || "",
        faultComment: null,
        actionName: "",
        linkId: null,
        appendix: null,
        needCheck: "0",
        faultLevel: null,
        // 新增字段
        faultVendor: null, // 故障厂家
        deviceType: null, // 设备类型
        deviceName: null, // 设备名称
      },
      dictTypeCode: null,
      isCheckDisable: false,
      qualitativeFullscreenLoading: false,
      // 测试用：派单方式
      testDispatchMode: 'unicom_to_unicom', // unicom_to_unicom, unicom_to_telecom, telecom_to_unicom
      // 故障厂家选项
      baseStationVendorOptions: [
        { label: '华为', value: 'huawei' },
        { label: '中兴', value: 'zte' },
        { label: '爱立信', value: 'ericsson' },
        { label: '诺基亚', value: 'nokia' },
        { label: '中信科', value: 'cict' },
        { label: '其他', value: 'other' }
      ],
      transmissionVendorOptions: [
        { label: '华为', value: 'huawei' },
        { label: '中兴', value: 'zte' },
        { label: '烽火', value: 'fiberhome' },
        { label: '上海贝尔', value: 'alcatel' },
        { label: '其它', value: 'other' }
      ],
      // 设备类型选项
      deviceTypeOptions: [
        { label: 'BSC', value: 'BSC' },
        { label: 'BTS', value: 'BTS' },
        { label: 'RNC', value: 'RNC' },
        { label: 'NodeB', value: 'NodeB' },
        { label: 'eNodeB', value: 'eNodeB' },
        { label: '小区(2/3/4G)', value: 'cell_234g' }
      ],
      //关联附件
      relatedFilesDialogVisible: false,
      importForm: {
        relatedFilesFileList: [],
      },
      evaluationDialogVisible: false,
      evaluation: {
        sendTimely: 5,
        orderAccuracy: 5,
        diagnosticrAccuracy: 5,
        middleGroundRate: 5,
        viewsOnContent: null,
        feedbackProblemCheckList: [],
      },
      colors: ["#99A9BF", "#F7BA2A", "#FF9900"], // 等同于 { 2: '#99A9BF', 4: { value: '#F7BA2A', excluded: true }, 5: '#FF9900' }
      //当前人的部门
      userData: null,

      qualitativeFormRule: {
        busRecoverTime: [{ validator: validBusRecoverTime, required: true }],
        attachmentName: [
          {
            validator: validAttachment,
            required: this.common.isUploadReport == "1" ? true : false,
          },
        ],

        isEffectBusiness: [
          { required: true, message: "请选择是否影响业务", trigger: "change" },
        ],
        isOverTime: [
          { required: true, message: "请选择是否超时", trigger: "change" },
        ],
        isTowerFault: [
          { required: true, message: "请选择是否铁塔原因", trigger: "change" },
        ],
        needCheck: [
          {
            required: true,
            message: "请选择是否需要定性审核",
            trigger: "change",
          },
        ],
        faultReasonDesc: [
          {
            required: true,
            message: "请填写故障原因描述",
          },
          {
            validator: this.checkLength,
            max: 500,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        faultComment: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      viewsOnContentShow: false,
      faultReasonOption: [],
      fddxFileArr: [],
      pickerOptions: {
        disabledDate: this.disabledDate,
        selectableRange: this.timeRange,
      },
      timeRange: "00:00:00 - 23:59:59",
      createTimeDate: "2022-10-20",
      organizeForm: {
        withinPersonList: [],
        withinPersonListCopy: [],
        withinPersonName: null,
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          withinPerson: "同行处理人",
        },
        multipleSelectEnable: true,
        singleSelectTip: "",
        showOrgsTree: true,
        showOrgsTreeMap: {
          recipientDetermine: false,
        },
        showContactUserTab: true,
        showContactUserTabMap: {
          recipientDetermine: true,
        },
      },
      multipleSelection: [],
      checkUserLimit: 10,
    };
  },
  watch: {
    "qualitativeForm.busRecoverTime": {
      handler(val) {
        if (val) {
          let valDate = val.split(" ")[0];
          let originDate = this.common.failureTime.split(" ")[0];
          let now = new Date();
          now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
          valDate = valDate.replace(/\-/g, "/");
          originDate = originDate.replace(/\-/g, "/");
          let year = now.getFullYear();
          let month = now.getMonth() + 1;
          let date = now.getDate();
          let nowDate =
            year + "/" + this.addZero(month) + "/" + this.addZero(date);
          let valDateUnix = Date.parse(valDate);
          let originDateUnix = Date.parse(originDate);
          let nowDateUnix = Date.parse(nowDate);

          let array = this.qualitativeForm.alarmCreateTime.split(" ");
          let createTime = array[1];
          let hour = now.getHours();
          let minute = now.getMinutes();
          let second = now.getSeconds();
          let nowTime =
            this.addZero(hour) +
            ":" +
            this.addZero(minute) +
            ":" +
            this.addZero(second);
          if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
            this.timeRange = createTime + " - " + nowTime;
            this.pickerOptions.selectableRange = this.timeRange;
          } else if (
            valDateUnix == originDateUnix &&
            originDateUnix < nowDateUnix
          ) {
            this.timeRange = createTime + " - 23:59:59";
            this.pickerOptions.selectableRange = this.timeRange;
          } else if (
            valDateUnix > originDateUnix &&
            valDateUnix < nowDateUnix
          ) {
            this.timeRange = "00:00:00 - 23:59:59";
            this.pickerOptions.selectableRange = this.timeRange;
          } else if (
            valDateUnix > originDateUnix &&
            valDateUnix == nowDateUnix
          ) {
            this.timeRange = "00:00:00 - " + nowTime;
            this.pickerOptions.selectableRange = this.timeRange;
          }
        }
      },
      deep: true,
    },
  },
  created() {
    this.getEvaluation();
  },
  mounted() {
    this.qualitativeForm.alarmCreateTime = this.common.failureTime;
    this.qualitativeForm.faultLevel = this.common.faultLevel;
    this.qualitativeForm.sheetCreateTime = this.common.failureInformTime;
    this.qualitativeForm.person = this.userInfo.realName;
    this.qualitativeForm.workItemId = this.common.workItemId;
    this.qualitativeForm.woId = this.common.woId;
    this.qualitativeForm.processInstId = this.common.processInstId;
    this.qualitativeForm.processDefId = this.common.processDefId;
    this.qualitativeForm.actionName = this.common.actionName;
    this.qualitativeForm.professionalType = this.common.professionalType + "";
    this.qualitativeForm.suspendDuration = this.common.hangOver;
    this.qualitativeForm.busRecoverTime = this.common.alarmClearTime;
    this.computerBusRecoverDuration();
    this.userData = JSON.parse(this.userInfo.attr2);
    this.qualitativeForm.dept = this.userData.orgInfo.fullOrgName;
    this.qualitativeForm.personPhone = this.userData.mobilePhone;
    if (this.common.auditResult == "N") {
      this.qualitativeDetail();
    }
    let array = this.qualitativeForm.alarmCreateTime.split(" ");
    this.createTimeDate = array[0];
    if (
      (this.common.acceptMan != null && this.common.acceptMan != "") ||
      (this.common.acceptDeptName != null &&
        this.common.acceptDeptName != "") ||
      (this.common.agentMan && this.common.agentMan.includes(",")) ||
      (this.common.agentDeptName && this.common.agentDeptName.includes(","))
    ) {
      this.qualitativeForm.needCheck = "1";
      this.isCheckDisable = true;
    } else {
      this.qualitativeForm.needCheck = "0";
      this.isCheckDisable = false;
    }
    this.computerBusRecoverDuration();
    // 初始化测试数据
    this.initTestData();
  },
  methods: {
    //时间对比
    disabledDate(time) {
      let now = moment(this.createTimeDate);
      let a = moment(now).subtract(1, "days");
      let today = moment(a).valueOf();
      return time.getTime() <= today || time.getTime() - 8.64e6 >= Date.now();
    },
    addZero(s) {
      return s < 10 ? "0" + s : s;
    },
    //查询是否已评价
    getEvaluation() {
      let param = {
        woId: this.common.woId,
      };
      apiGetEvaluation(param)
        .then(res => {
          if (res.status == "0") {
            let show = res?.data?.rows[0].isHaveEvaluation;

            if (show == "0") {
              this.sumBtnShow = true;
            } else {
              this.sumBtnShow = false;
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    qualitativeDetail() {
      let param = {
        woId: this.common.woId,
        isShowWu: "0",
      };
      apiqueryFeedback(param)
        .then(res => {
          if (res.status == 0) {
            let self = this;
            let qualitativeData = res?.data?.rows ?? [];
            if (qualitativeData.length > 0) {
              self.qualitativeForm = qualitativeData[0];
              self.$set(
                self.qualitativeForm,
                "isOverTime",
                self.qualitativeForm.isOverTime + ""
              );
              self.$set(
                self.qualitativeForm,
                "isEffectBusiness",
                self.qualitativeForm.isEffectBusiness + ""
              );
              self.$set(
                self.qualitativeForm,
                "isSiteOffline",
                self.qualitativeForm.isSiteOffline + ""
              );
              self.$set(
                self.qualitativeForm,
                "isTowerFault",
                self.qualitativeForm.isTowerFault + ""
              );
              self.$set(
                self.qualitativeForm,
                "needCheck",
                self.qualitativeForm.needCheck + ""
              );
              self.$set(
                self.qualitativeForm,
                "faultHandleType",
                self.qualitativeForm.faultHandleType + ""
              );
              self.changeFaultCate("init");
              if (qualitativeData[0].appendix) {
                this.fddxFileArr = JSON.parse(qualitativeData[0].appendix);
              }
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.qualitativeForm.relatedFiles = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    computerBusRecoverDuration() {
      if (this.qualitativeForm.busRecoverTime) {
        let days = moment(
          this.qualitativeForm.busRecoverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.qualitativeForm.busRecoverDuration = days;
      } else {
        this.qualitativeForm.busRecoverDuration = 0;
      }

      if (this.qualitativeForm.busRecoverTime) {
        let days = moment(
          this.qualitativeForm.busRecoverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.qualitativeForm.faultDuration = days;
        //故障处理净历时  （故障结束时间-故障发生时间）-挂起历时(秒) 换算成-天-小时-分钟-秒
        if (this.qualitativeForm.suspendDuration == 0) {
          this.qualitativeForm.processDuration = this.qualitativeForm.faultDuration;
        } else {
          let seconds = moment(
            this.qualitativeForm.busRecoverTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          this.qualitativeForm.processDuration =
            seconds - this.qualitativeForm.suspendDuration;
        }
      }
      if (this.qualitativeForm.processDuration != 0) {
        //故障处理净历时<预估处理时限  不超时
        if (
          this.qualitativeForm.processDuration <
          this.common.processTimeLimit * 3600
        ) {
          this.qualitativeForm.isOverTime = "0";
        } else {
          this.qualitativeForm.isOverTime = "1";
        }
      }
    },
    second2Time(days) {
      // return this.showTime(Math.abs(days));
      return this.showTime(days);
    },
    //挂起历时
    computerSuspendDuration() {
      if (this.timing.hangTime != "" && null != this.timing.hangTime) {
        if (
          this.timing.liftHangTime != "" &&
          null != this.timing.liftHangTime
        ) {
          let seconds = moment(
            this.timing.liftHangTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.timing.hangTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          this.qualitativeForm.suspendDuration = seconds;
        } else {
          this.qualitativeForm.suspendDuration = 0;
        }
      } else {
        this.qualitativeForm.suspendDuration = 0;
      }
    },
    changeFaultCate(type) {
      if (type != "init") {
        this.qualitativeForm.faultReason = "";
      }
      if (this.qualitativeForm.faultCate == "1") {
        this.dictTypeCode = "70006";
      } else if (this.qualitativeForm.faultCate == "2") {
        this.dictTypeCode = "70007";
      } else if (this.qualitativeForm.faultCate == "3") {
        this.dictTypeCode = "70008";
      } else if (this.qualitativeForm.faultCate == "4") {
        this.dictTypeCode = "70009";
      } else if (this.qualitativeForm.faultCate == "5") {
        this.dictTypeCode = "70010";
      } else if (this.qualitativeForm.faultCate == "6") {
        this.dictTypeCode = "70011";
      } else if (this.qualitativeForm.faultCate == "7") {
        this.dictTypeCode = "70012";
      } else if (this.qualitativeForm.faultCate == "8") {
        this.dictTypeCode = "70013";
      } else if (this.qualitativeForm.faultCate == "9") {
        this.dictTypeCode = "70014";
      }
    },
    //下一步评价
    nextStepEvaluation() {
      this.$refs.qualitativeForm.validate((valid, a) => {
        if (this.uncheck(a)) {
          this.evaluationDialogVisible = !this.evaluationDialogVisible;
        } else {
          return false;
        }
      });
    },
    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          let self = this;
          this.qualitativeFullscreenLoading = true;
          self.entering();
          let formData = new FormData();
          if (self.importForm.relatedFilesFileList.length > 0) {
            for (let item of self.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          self.$set(self.qualitativeForm, "workItemId", self.common.workItemId);
          console.log(self.organizeForm.withinPersonList);
          formData.append("jsonParam", JSON.stringify(self.qualitativeForm));
          let evaluateParam = {
            woId: self.common.woId,
            sheetCreateTime: self.qualitativeForm.sheetCreateTime,
            sendAccuracy: self.evaluation.orderAccuracy,
            sendTimely: self.evaluation.sendTimely,
            middleGroundRate: self.evaluation.middleGroundRate,
            diagnoseAccuracy: self.evaluation.diagnosticrAccuracy,
            evaluateContent: self.evaluation.viewsOnContent,
            problemClass:
              self.evaluation.feedbackProblemCheckList.length > 0
                ? self.evaluation.feedbackProblemCheckList.join(",")
                : "",
            remainAcceptTime: self.common.remainAcceptTime,
          };
          formData.append("evaluateParam", JSON.stringify(evaluateParam));

          formData.append(
            "processingType",
            self.common.auditResult == "N" ? "update" : "add"
          );
          apiWirelessQualitative(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("提交定性成功");
                this.evaluationDialogVisible = false;
                this.$emit("qualitativeSubmit", res.data);
              } else {
                this.$message.error("提交定性失败");
              }
              this.qualitativeFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("提交定性失败");
              this.qualitativeFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    close(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
    },
    onReset() {
      this.qualitativeForm = {
        ...this.$options.data,
        alarmCreateTime: this.common.failureTime,
        sheetCreateTime: this.common.failureInformTime,
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        person: this.userInfo.realName,
        dept: this.userData.orgInfo.fullOrgName,
        actionName: this.common.actionName,
        professionalType: "22",
        faultDuration: 0, //故障处理净历时
        suspendDuration: this.common.hangOver, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        faultReason: null,
        faultReasonDesc: this.common.faultCauseDescription || "",
        faultComment: null,
        faultCate: null,
        busRecoverTime: this.common.alarmClearTime, //业务恢复时间
        busRecoverDuration: 0,
        isTowerFault: null,
        isEffectBusiness: null,
        isSiteOffline: null,
        siteOfflineReason: null,
        faultHandleType: null,
        withinPerson: null,
        isOverTime: null,
        faultLevel: this.common.faultLevel,
        personPhone: this.userData.mobilePhone,
      };
      this.importForm.relatedFilesFileList = [];
      this.computerBusRecoverDuration();
      if (
        (this.common.acceptMan != null && this.common.acceptMan != "") ||
        (this.common.acceptDeptName != null &&
          this.common.acceptDeptName != "") ||
        (this.common.agentMan && this.common.agentMan.includes(",")) ||
        (this.common.agentDeptName && this.common.agentDeptName.includes(","))
      ) {
        this.qualitativeForm.needCheck = "1";
        this.isCheckDisable = true;
      } else {
        this.qualitativeForm.needCheck = "0";
        this.isCheckDisable = false;
      }
    },
    showTime(val) {
      let isPositiveNumber = true;
      let valStr = val + "";
      if (valStr.indexOf("-") != -1) {
        //负数
        val = Math.abs(val);
        isPositiveNumber = false;
      }
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }
        if (!isPositiveNumber) {
          time = "-" + time;
        }
        return time;
      } else {
        return "0秒";
      }
    },
    //设备名称选择
    deviceSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#device").value = jsonStr;
        document.querySelector("#sub__device").submit();
      });
    },

    feedbackChange(val) {
      if (val.length > 0) {
        if (val.join(",").indexOf("其他") != -1) {
          this.viewsOnContentShow = true;
        } else {
          this.viewsOnContentShow = false;
        }
      }
    },
    closeAndDeleteFile(tag) {
      this.deleteFile(tag);
    },
    deleteFile(tag) {
      let param = {
        attId: tag.id,
        linkId: this.qualitativeForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            this.fddxFileArr.splice(this.fdFileArr.indexOf(tag), 1);
            this.qualitativeForm.appendix = JSON.stringify(this.fddxFileArr);
            this.$message.success("附件删除成功");
          } else {
            this.$$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    closeFile(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
      if (
        this.importForm.relatedFilesFileList.length == 0 &&
        this.fddxFileArr.length == 0
      ) {
        this.qualitativeReviewForm.attachmentName = null;
      }
    },
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        });
    },
    onOpenPeopleDialog(diaSaveName) {
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree = false;
      this.diaPeople.showContactUserTab = true;
      this.diaPeople.visible = true;
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //同行处理人
    withinPerson({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let withPerson = this.organizeForm.withinPersonList.findIndex(val => {
            return val.id === item.userName;
          });
          if (withPerson > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.withinPersonList.push({
              type: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let withPerson = this.organizeForm.withinPersonList.findIndex(val => {
            return val.id === item.userName;
          });
          if (withPerson > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.withinPersonList.push({
              type: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let withPerson = this.organizeForm.withinPersonList.findIndex(val => {
            return val.id === item.id;
          });
          if (withPerson > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.withinPersonList.push({
              type: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.organizeForm.withinPersonListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.withinPersonList)
      );
    },
    //搜索
    search(val) {
      if (val == "withinPerson" && this.organizeForm.withinPersonList != null) {
        this.organizeForm.withinPersonListCopy = [];
        this.organizeForm.withinPersonList.forEach(row => {
          if (
            row.name == this.organizeForm.withinPersonName ||
            row.orgName == this.organizeForm.withinPersonName
          ) {
            this.organizeForm.withinPersonListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "withinPerson") {
        this.organizeForm.withinPersonName = "";
        this.organizeForm.withinPersonListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.withinPersonList)
        );
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "withPerson") {
        this.organizeForm.withinPersonList.splice(
          this.arrayIndex(this.organizeForm.withinPersonList, tag),
          1
        );
        this.organizeForm.withinPersonListCopy.splice(
          this.arrayIndex(this.organizeForm.withinPersonListCopy, tag),
          1
        );
      }
    },
    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection.length == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    entering() {
      if (
        this.organizeForm.withinPersonList &&
        this.organizeForm.withinPersonList.length > 0
      ) {
        let userList = this.organizeForm.withinPersonList.filter(
          (item, index) => {
            return item.type == "user";
          }
        );

        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.qualitativeForm.withinPerson = usersCheckedName.join(",");
        }
      }
    },
    // 初始化测试数据
    initTestData() {
      // 自动回填告警设备信息（模拟数据）
      if (this.alarmDeviceVendor) {
        this.qualitativeForm.faultVendor = this.alarmDeviceVendor;
      } else {
        // 测试数据
        this.qualitativeForm.faultVendor = 'huawei';
      }

      if (this.alarmDeviceType) {
        this.qualitativeForm.deviceType = this.alarmDeviceType;
      } else {
        // 测试数据
        this.qualitativeForm.deviceType = 'eNodeB';
      }

      if (this.alarmDeviceName) {
        this.qualitativeForm.deviceName = this.alarmDeviceName;
      } else {
        // 测试数据
        this.qualitativeForm.deviceName = '北京朝阳CBD基站设备';
      }

      // 测试数据：设置故障分类为基站设备，以便显示故障厂家字段
      if (!this.qualitativeForm.faultCate) {
        this.qualitativeForm.faultCate = '基站设备';
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;

  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;

    @include themify {
      background-color: themed("$--background-color-base");
    }
  }

  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }

  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
