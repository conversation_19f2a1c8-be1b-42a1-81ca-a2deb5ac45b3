const getters = {
  token: state => state.user.token,
  userInfo: state => state.user.userInfo,
  permission_routes: state => state.permission.routes,
  addRoutes: state => state.permission.addRoutes,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  isleave: state => state.tagsView.isleave,
  layoutMode: state => state.settings.layout,
  singleLayout: state => state.settings.singleLayout,
  sidebar: state => state.settings.sidebar,
  fixedHeader: state => state.settings.fixedHeader,
  showSettings: state => state.settings.showSettings,
  fullScreen: state => state.settings.fullScreen,
  topShowLogo: state => state.settings.topShowLogo,
  topShowBreadcrumb: state => state.settings.topShowBreadcrumb,
  personalPanelShowText: state => state.settings.personalPanelShowText,
  hideTabView: state => state.settings.hideTabView,
  openOnceFullscreen: state => state.settings.openOnceFullscreen,
  keepAliveInstance: state => state.keepAlive.instance,
  themeName: state => state.settings.themeName,
  themeVars: state => state.settings.themeVars, // 当前主题的scss变量 js中当变量使用
  echartsThemeName: state => state.settings.echartsThemeName, // 当前echarts主题名称 主题设置用
  echartsName: state => state.settings.echartsName, // 当前echarts主题名称 页面echarts用
  frameStyle: state => state.settings.frameStyle, // 框架的共享样式
  authorityList: state => state.authority.list,

  // 智能体相关状态
  isAiAgentAvailable: state => state.aiAgent.isAiAgentAvailable,
  showAiAgent: state => state.aiAgent.showAiAgent,
  aiAgentLoading: state => state.aiAgent.aiAgentLoading,
  aiAgentError: state => state.aiAgent.aiAgentError,
  aiAgentUrl: state => state.aiAgent.aiAgentUrl,
  aiAgentConfig: state => state.aiAgent.aiAgentConfig,
  aiAgentUserPreferences: state => state.aiAgent.userPreferences,
};

export default getters;
