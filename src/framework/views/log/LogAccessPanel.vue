<template>
  <div>
    <el-header style="height: 50px; width: 100%">
      <el-card class="box-card">
        <el-form :model="queryParams" :inline="true">
          <el-form-item label="用户名">
            <el-input v-model="queryParams.userName"></el-input>
          </el-form-item>
          <el-form-item label="菜单名称">
            <el-input v-model="queryParams.resourceName"></el-input>
          </el-form-item>
          <el-form-item label="开始时间">
            <el-date-picker
              v-model="queryParams.startLoadTime"
              type="datetime"
              placeholder="选择日期时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间">
            <el-date-picker
              v-model="queryParams.endLoadTime"
              type="datetime"
              placeholder="选择日期时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="doCheck" class="el-icon-search"
              >查询</el-button
            >
            <el-button
              type="primary"
              @click="exportLogAccessFn"
              class="export el-icon-download"
              >导出</el-button
            >
          </el-form-item>
        </el-form>
      </el-card>
    </el-header>
    <el-main style="width: 100%">
      <el-card
        :style="{
          height: rightBodyHeight,
          width: '100%',
        }"
      >
        <el-table
          :style="{ width: '100%' }"
          :height="tableHeight"
          :data="tableData"
          border
          stripe
        >
          <el-table-column align="center" prop="" label="序号" width="60">
            <template slot-scope="scope">
              <span>{{
                scope.$index +
                1 +
                (pageConfig.currentPage - 1) * pageConfig.pageSize
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="userName"
            width="120"
            label="用户名"
          ></el-table-column>
          <!-- <el-table-column
            align="left"
            width="160px"
            prop="realName"
            label="姓名"
          ></el-table-column> -->
          <el-table-column
            align="left"
            width="160px"
            prop="zoneName"
            label="所属区域"
          ></el-table-column>
          <!-- <el-table-column
            align="left"
            width="160px"
            prop="deptName"
            label="所属组织"
          ></el-table-column> -->
          <el-table-column
            align="center"
            prop="resourceName"
            label="菜单名称"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="clientIp"
            width="120"
            label="IP地址"
          ></el-table-column>
          <el-table-column
            align="center"
            width="100px"
            prop="clientOs"
            label="操作系统"
          ></el-table-column>
          <el-table-column
            align="center"
            width="120"
            prop="browserName"
            label="浏览器"
          ></el-table-column>
          <el-table-column
            align="center"
            width="120"
            prop="browserVersion"
            label="浏览器版本"
          ></el-table-column>
          <el-table-column
            align="center"
            width="170"
            prop="loadTime"
            :formatter="dateFormater"
            label="访问时间"
          ></el-table-column>
        </el-table>
        <div align="right">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageConfig.currentPage"
            :page-sizes="[10, 15, 20, 30, 50]"
            :page-size="pageConfig.pageSize"
            layout="->, sizes, prev, pager, next, jumper, slot, total"
            :total="pageConfig.total"
            class="pagination"
          ></el-pagination>
        </div>
      </el-card>
    </el-main>
  </div>
</template>

<script>
import moment from "moment";
import { getLogAccess, exportLogAccess } from "./api/log";

export default {
  name: "LogAccessPanel",
  props: ["pid", "tableHeight", "rightBodyHeight"],
  watch: {
    pid: function () {
      this.queryLogAccessFn();
    },
  },
  data() {
    return {
      tableData: [],
      queryParams: {
        userName: "",
        resourceName: "",
        startLoadTime: moment().add(-1, "d").toDate(),
        endLoadTime: moment().toDate(),
      },
      pageConfig: {
        pageSizes: [10, 20, 30, 40, 50, 60, 70, 80, 100],
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
    };
  },
  methods: {
    dateFormater: function (row) {
      if (row.loadTime) {
        return moment(row.loadTime).format("YYYY-MM-DD HH:mm:ss");
      }
      return "";
    },

    exportLogAccessFn() {
      let that = this;
      that.$message.info("正在导出文件");
      exportLogAccess({
        resourceName: this.queryParams.resourceName,
        userName: this.queryParams.userName,
        startLoadTime: this.queryParams.startLoadTime,
        endLoadTime: this.queryParams.endLoadTime,
      }).catch(err => {
        if (err.response.data && err.response.data.type == "application/json") {
          let reader = new FileReader();
          reader.readAsText(err.response.data, "utf-8");
          reader.onload = function (result) {
            let target = result.target;
            let res = JSON.parse(target.result);
            that.$message.warning(res.msg);
          };
        } else if (err.status) {
          this.$message.error(err.message);
        }
      });
    },

    //执行查询
    doCheck() {
      this.resetData();
      this.queryLogAccessFn();
    },

    queryLogAccessFn: function () {
      getLogAccess({
        resourceName: this.queryParams.resourceName,
        userName: this.queryParams.userName,
        startLoadTime: this.queryParams.startLoadTime,
        endLoadTime: this.queryParams.endLoadTime,
        pageNum: this.pageConfig.currentPage,
        pageSize: this.pageConfig.pageSize,
      })
        .then(res => {
          console.info(res);
          this.tableData = res.data.list;
          this.pageConfig.total = res.data.total;
        })
        .catch(err => {
          if (err.status) {
            this.$message.error(err.message);
          }
        });
    },

    //重置数据
    resetData() {
      this.pageConfig.currentPage = 1;
      this.tableData = [];
    },

    handleSizeChange: function (pageSize) {
      this.pageConfig.pageSize = pageSize;
      this.resetData();
      this.queryLogAccessFn();
    },

    handleCurrentChange: function (pageNum) {
      this.pageConfig.currentPage = pageNum;
      this.tableData = [];
      this.queryLogAccessFn();
    },
  },
  mounted() {
    this.queryLogAccessFn();
  },
};
</script>
<style lang="scss" scoped>
.pagination {
  margin-top: 8px;
}
</style>
