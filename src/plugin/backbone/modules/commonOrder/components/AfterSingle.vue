<template>
  <div class="afterSingle">
    <el-form
      ref="afterSingleForm"
      :model="afterSingleForm"
      :rules="afterSingleFormRules"
      label-width="150px"
    >
      <el-form-item label="追单内容" prop="content">
        <el-input
          type="textarea"
          :rows="2"
          v-model="afterSingleForm.content"
          style="width: 390px"
          show-word-limit
          maxlength="255"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="系统名称" prop="systemName" v-if="common.isObject == 1">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="多个系统以逗号分隔"
          v-model="afterSingleForm.systemName"
          style="width: 390px"
          show-word-limit
          maxlength="255"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="附件:">
        <div style="width: 400px">
          <el-tag
            class="fileName_style"
            closable
            v-for="(item, index) in importForm.attachmentFileList"
            :key="index"
            @close="close(item)"
            :title="item.name"
            ><div class="text-truncate">{{ item.name }}</div></el-tag
          >
          <el-button size="mini" type="primary" @click="attachmentBrowse"
            >+上传附件</el-button
          >
        </div>
      </el-form-item>
      <el-form-item
        label="是否流转省份:"
        v-if="
          common.isObject == 1 &&
          common.sheetStatus != '待定性审核' &&
          common.sheetStatus != '挂起'
        "
        prop="transferStatusRadio"
        :rules="{
          required: true,
          message: '请选择是否流转省份',
        }"
      >
        <el-radio-group v-model="afterSingleForm.transferStatusRadio">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        label="是否备份中心管辖:"
        v-if="
          common.isObject == 1 &&
          common.sheetStatus != '待定性审核' &&
          common.sheetStatus != '挂起'
        "
        :rules="{
          required: true,
          message: '请选择是否备份中心管辖',
        }"
        prop="backCenterControlled"
      >
        <el-radio-group v-model="afterSingleForm.backCenterControlled">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="主送:"
        prop="mainSending"
        v-if="afterSingleForm.transferStatusRadio == 1"
        :rules="{
          required: true,
          message: '请选择主送人',
        }"
      >
        <el-input
          v-model="afterSingleForm.mainSending"
          placeholder="添加人员"
          style="width: 390px"
          readonly
        >
          <template v-for="(tag, index) in organizeForm.builderZsList">
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable="true"
              @close="handleClose('builderZs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'user'"
            >
              {{ tag.name }}
            </el-tag>
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable="true"
              @close="handleClose('builderZs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'org'"
            >
              {{ tag.orgName }}
            </el-tag>
          </template>
          <el-popover
            slot="prefix"
            v-if="organizeForm.builderZsList.length >= 2"
            width="500"
            trigger="click"
          >
            <el-input
              v-model="organizeForm.builderZsName"
              placeholder="请输入主送人员姓名/组织名称"
            >
              <el-button
                type="info"
                slot="append"
                icon="el-icon-search"
                @click="search('builderZs')"
              >
              </el-button>
              <el-button
                type="info"
                slot="append"
                icon="el-icon-close"
                @click="clear('builderZs')"
              >
              </el-button>
            </el-input>
            <el-table
              ref="multipleTable"
              tooltip-effect="dark"
              @selection-change="handleSelectionChange"
              :data="organizeForm.builderZsListCopy"
              max-height="240"
            >
              <el-table-column width="30" type="selection"> </el-table-column>
              <el-table-column min-width="70" property="name" label="姓名">
              </el-table-column>
              <el-table-column min-width="180" property="orgName" label="组织">
              </el-table-column>
              <el-table-column
                min-width="120"
                property="mobilePhone"
                label="电话"
              >
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="50">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click.native.prevent="handleClose('builderZs', scope.row)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              size="small"
              type="text"
              @click="toggleSelection('builderZs')"
              >批量移除
            </el-button>
            <el-tag slot="reference" style="margin-top: 3px">
              +{{ organizeForm.builderZsList.length - 1 }}
            </el-tag>
          </el-popover>
          <el-button
            type="info"
            slot="append"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('lordSentDetermine')"
          ></el-button>
        </el-input>
      </el-form-item>
      <el-form-item
        label="抄送:"
        prop="cc"
        v-if="afterSingleForm.transferStatusRadio == 1"
      >
        <el-input
          v-model="afterSingleForm.cc"
          placeholder="添加人员"
          style="width: 390px"
          readonly
        >
          <template v-for="(tag, index) in organizeForm.builderCsList">
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable="true"
              @close="handleClose('builderCs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'user'"
            >
              {{ tag.name }}
            </el-tag>
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable="true"
              @close="handleClose('builderCs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'org'"
            >
              {{ tag.orgName }}
            </el-tag>
          </template>
          <el-popover
            slot="prefix"
            v-if="organizeForm.builderCsList.length >= 2"
            width="500"
            trigger="click"
          >
            <el-input
              v-model="organizeForm.builderCsName"
              placeholder="请输入抄送人员姓名/组织名称"
            >
              <el-button
                type="info"
                slot="append"
                icon="el-icon-search"
                @click="search('builderCs')"
              >
              </el-button>
              <el-button
                type="info"
                slot="append"
                icon="el-icon-close"
                @click="clear('builderCs')"
              >
              </el-button>
            </el-input>
            <el-table
              ref="multipleTable"
              tooltip-effect="dark"
              @selection-change="handleSelectionChange"
              :data="organizeForm.builderCsListCopy"
              max-height="240"
            >
              <el-table-column width="30" type="selection"> </el-table-column>
              <el-table-column min-width="70" property="name" label="姓名">
              </el-table-column>
              <el-table-column min-width="180" property="orgName" label="组织">
              </el-table-column>
              <el-table-column
                min-width="120"
                property="mobilePhone"
                label="电话"
              >
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="50">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click.native.prevent="handleClose('builderCs', scope.row)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              size="small"
              type="text"
              @click="toggleSelection('builderCs')"
              >批量移除
            </el-button>
            <el-tag slot="reference" style="margin-top: 5px">
              +{{ organizeForm.builderCsList.length - 1 }}
            </el-tag>
          </el-popover>
          <el-button
            type="info"
            slot="append"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('ccDetermine')"
          >
          </el-button>
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleSubmit('afterSingleForm')"
        v-loading.fullscreen.lock="afterSingleFullscreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAfterSingleForm">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <!-- <dia-orgs-user-tree
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      @on-save="onSavePeople"
      :appendToBody="true"
      :orgDefaultChecked="rawAgenManDeptCodeArr"
      :userDefaultChecked="rawAgentManIdArr"
    /> -->
    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      :showContactUserTab="diaPeople.showContactUserTab"
      :showContactOrgTab="diaPeople.showContactOrgTab"
      @on-save="onSavePeople"
      :orgDefaultChecked="rawAgenManDeptCodeArr"
      :userDefaultChecked="rawAgentManIdArr"
      :appendToBody="true"
    />
  </div>
</template>
<script>
import { apiAfterSingle } from "../api/CommonApi";
import { workflowQueryBackControlled } from "@/plugin/backbone/modules/api/generalApi";

import FileUpload from "../../workOrder/components/FileUpload.vue";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";
import {mixin} from "../../../../../mixins"

export default {
  name: "AfterSingle",
  props: {
    common: Object,
  },
  components: { FileUpload, DiaTissueTree },
  mixins: [mixin],
  data() {
    return {
      afterSingleForm: {
        content: null,
        systemName: null,
        mainSending: null,
        lordSentUserName: null,
        lordSentOrgName: null,
        lordSentUserId: null,
        lordSentOrgId: null,
        cc: null,
        transferStatusRadio: "0",
        backCenterControlled: "0",
        ccUserName: null,
        ccOrgName: null,
        ccUserId: null,
        ccOrgId: null,
        agentManDetail: null,
        copyManDetail: null,
      },
      afterSingleFormRules: {
        content: [
          {
            required: true,
            message: "请填写追单内容",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "afterSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        systemName: [
          {
            validator: this.checkLength,
            max: 255,
            form: "afterSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      organizeForm: {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",
        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",
      },
      multipleSelection: [],
      afterSingleFullscreenLoading: false,
      attachmentDialogVisible: false,
      importForm: {
        //附件
        attachmentFileList: [],
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "主送",
          ccDetermine: "建单人抄送",
        },
        showOrgsTree: true,
        showContactUserTab: false,
        showContactOrgTab: false,
        showContactUserTabMap: {
          lordSentDetermine: true,
          ccDetermine: true,
        },
        showContactOrgTabMap: {
          lordSentDetermine: true,
          ccDetermine: true,
        },
      },
      isDiaOrgsUserTree: false,
      rawAgentManIdArr: [],
      rawAgenManDeptCodeArr: [],
    };
  },
  mounted() {
    // if (this.common.isSender == 1) {
    //   this.afterSingleForm.mainSending = this.stitchingAlgorithm(
    //     this.common.agentDeptName,
    //     this.common.agentMan
    //   );
    //   this.afterSingleForm.lordSentUserName = this.common.agentMan;
    //   this.afterSingleForm.lordSentOrgName = this.common.agentDeptName;
    //   if (this.common.agentManId) {
    //     this.afterSingleForm.lordSentUserId = this.common.agentManId;
    //     this.rawAgentManIdArr = this.common.agentManId.split(",");
    //   }
    //   if (this.common.agentDeptCode) {
    //     this.afterSingleForm.lordSentOrgId = this.common.agentDeptCode;
    //     this.rawAgenManDeptCodeArr = this.common.agentDeptCode.split(",");
    //   }
    // }

    // 只有在展示"是否备份中心管辖"字段时才调用回填接口
    if (this.common.isObject == 1 && this.common.sheetStatus != '待定性审核' && this.common.sheetStatus != '挂起') {
      workflowQueryBackControlled({ woId: this.common.woId })
        .then(backRes => {
          if (backRes.status == "0" && backRes.data) {
            this.afterSingleForm.backCenterControlled = backRes.data;
          }
        })
        .catch(backError => {
          console.log("回填是否备份中心管辖字段失败:", backError);
        });
    }
  },
  watch: {
    "organizeForm.builderZsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.afterSingleForm.mainSending = "已选";
        } else {
          this.afterSingleForm.mainSending = "";
        }
      },
      deep: true,
    },
    "organizeForm.builderCsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.afterSingleForm.cc = "已选";
        } else {
          this.afterSingleForm.cc = "";
        }
      },
      deep: true,
    },
  },
  methods: {
    handleSubmit(formName) {
      this.entering();
      this.$refs[formName].validate((valid,a) => {
        if (this.uncheck(a)) {
          this.afterSingleFullscreenLoading = true;
          let formData = new FormData();
          if (this.importForm.attachmentFileList.length > 0) {
            for (let item of this.importForm.attachmentFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          formData.append("isSender", this.common.isObject);
          let param = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            processDefId: this.common.processDefId,
            workItemId: null, //this.common.workItemId,
            processNode: this.common.processNode,
            appendContent: this.afterSingleForm.content,
            sysName: this.afterSingleForm.systemName,
            isSendtoProvince: this.afterSingleForm.transferStatusRadio || "0",
            agentManId:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.agentManId
                : this.afterSingleForm.lordSentUserId,
            agentMan:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.agentMan
                : this.afterSingleForm.lordSentUserName,
            agentDeptCode:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.agentDeptCode
                : this.afterSingleForm.lordSentOrgId,
            agentDeptName:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.agentDeptName
                : this.afterSingleForm.lordSentOrgName,
            copyManId:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.copyManId
                : this.afterSingleForm.ccUserId,
            copyMan:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.copyMan
                : this.afterSingleForm.ccUserName,
            copyDeptCode:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.copyDeptCode
                : this.afterSingleForm.ccOrgId,
            copyDeptName:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.copyDeptName
                : this.afterSingleForm.ccOrgName,
          };

          // 只有在字段展示时才传递backCenterControlled字段
          if (this.common.isObject == 1 &&
              this.common.sheetStatus != '待定性审核' &&
              this.common.sheetStatus != '挂起') {
            param.backCenterControlled = this.afterSingleForm.backCenterControlled || "0";
          }
          formData.append("jsonParam", JSON.stringify(param));
          apiAfterSingle(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("追单提交成功");
                this.$emit("closeAfterSingleDialog", this.common.isObject);
              } else {
                this.$message.error("追单提交失败");
              }
              this.afterSingleFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.afterSingleFullscreenLoading = false;
              this.$message.error("追单提交失败");
            });
        } else {
          return false;
        }
      });
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    onResetAfterSingleForm() {
      this.afterSingleForm = {
        content: null,
        systemName: null,
        mainSending: null,
        lordSentUserName: null,
        lordSentOrgName: null,
        lordSentUserId: null,
        lordSentOrgId: null,
        cc: null,
        transferStatusRadio: "0",
        backCenterControlled: "0",
        ccUserName: null,
        ccOrgName: null,
        ccUserId: null,
        ccOrgId: null,
      };
      (this.organizeForm = {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",
        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",
      }),
        (this.importForm.attachmentFileList = []);
    },
    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree = true;
      this.diaPeople.showContactUserTab =
        this.diaPeople.showContactUserTabMap[diaSaveName] ?? false;
      this.diaPeople.showContactOrgTab =
        this.diaPeople.showContactOrgTabMap[diaSaveName] ?? false;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //主送确定
    lordSentDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      this.organizeForm.builderZsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderZsList)
      );
    },
    //抄送
    ccDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (csOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (csOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          }
          {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }
      this.organizeForm.builderCsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderCsList)
      );
    },
    close(tag) {
      this.importForm.attachmentFileList.splice(
        this.importForm.attachmentFileList.indexOf(tag),
        1
      );
    },
    stitchingAlgorithm(orgName, userName) {
      if (
        null != orgName &&
        orgName.length !== 0 &&
        null != userName &&
        userName.length !== 0
      ) {
        return orgName + "," + userName;
      } else {
        if (null != orgName && orgName.length !== 0) {
          return orgName;
        } else if (null != userName && userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },

    stitchingAlgorithmArr(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName.join(",") + "," + userName.join(",");
      } else {
        if (orgName.length !== 0) {
          return orgName.join(",");
        } else if (userName.length !== 0) {
          return userName.join(",");
        } else {
          return "";
        }
      }
    },
    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      this.multipleSelection;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "builderZs") {
        this.organizeForm.builderZsList.splice(
          this.arrayIndex(this.organizeForm.builderZsList, tag),
          1
        );
        this.organizeForm.builderZsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderZsListCopy, tag),
          1
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsList.splice(
          this.arrayIndex(this.organizeForm.builderCsList, tag),
          1
        );
        this.organizeForm.builderCsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderCsListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "builderZs" && this.organizeForm.builderZsList != null) {
        this.organizeForm.builderZsListCopy = [];
        this.organizeForm.builderZsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderZsName ||
            row.orgName == this.organizeForm.builderZsName
          ) {
            this.organizeForm.builderZsListCopy.push(row);
          }
        });
      }
      if (val == "builderCs" && this.organizeForm.builderCsList != null) {
        this.organizeForm.builderCsListCopy = [];
        this.organizeForm.builderCsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderCsName ||
            row.orgName == this.organizeForm.builderCsName
          ) {
            this.organizeForm.builderCsListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "builderZs") {
        this.organizeForm.builderZsName = "";
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsName = "";
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
    },
    entering() {
      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length > 0
      ) {
        this.afterSingleForm.mainSending = "1";
        let userList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.afterSingleForm.lordSentUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.afterSingleForm.lordSentUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.afterSingleForm.agentManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.afterSingleForm.lordSentOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.afterSingleForm.lordSentOrgId = orgsCheckedId.join(",");
        }
      }
      if (
        this.organizeForm.builderCsList &&
        this.organizeForm.builderCsList.length > 0
      ) {
        let userList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.afterSingleForm.ccUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.afterSingleForm.ccUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.afterSingleForm.copyManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.afterSingleForm.ccOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.afterSingleForm.ccOrgId = orgsCheckedId.join(",");
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 15px;
  padding-left: 10px;
  // 不可全选样式
  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }
    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}
.afterSingle {
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 360px;
      vertical-align: top;
    }
  }
}
</style>
