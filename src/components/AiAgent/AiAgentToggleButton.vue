<template>
  <div class="ai-agent-toggle-button" :class="{ 'button-active': show }">
    <div
      @click="handleToggle"
      class="toggle-btn"
      :class="{ 'btn-show': show, 'btn-hide': !show, 'is-loading': loading }"
    >
      <img
        src="./assets/new-ai-button.png"
        alt="智能助手"
        class="ai-icon"
        :class="{ 'icon-active': show }"
      />

      <!-- 状态指示器 -->
      <!-- <div class="status-indicator" :class="{ 'active': show }"></div> -->

      <!-- 光泽动画 -->
      <div class="btn-shine"></div>

      <!-- 加载状态遮罩 -->
      <!-- <div v-if="loading" class="loading-overlay">
        <i class="el-icon-loading"></i>
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "AiAgentToggleButton",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleToggle() {
      if (!this.loading) {
        this.$emit("toggle");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ai-agent-toggle-button {
  position: absolute;
  bottom: 80px;
  right: 7px;
  z-index: 2000; // 确保按钮在智能体面板之上

  .toggle-btn {
    position: relative;
    // width: 60px;
    // height: 60px;是
    border-radius: 50%;
    border: none;
    overflow: hidden;
    transition: all 0.3s ease;

    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
    }

    // 隐藏状态样式（蓝绿渐变背景）
    &.btn-hide {
      // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

      &:hover {
        // background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      }
    }

    // 显示状态样式（红橙渐变背景）
    &.btn-show {
      // background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);

      &:hover {
        // background: linear-gradient(135deg, #ff5252 0%, #ff9800 100%);
      }
    }

    // AI图标样式
    .ai-icon {
      width: 70px;
      height: 70px;
      object-fit: contain;
      transition: all 0.3s ease;
      // filter: brightness(0) invert(1); // 将图标变为白色

      &.icon-active {
        transform: scale(1.1);
      }
    }

    // 加载状态遮罩
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 3;
      border-radius: 50%;

      i {
        color: white;
        font-size: 20px;
        animation: rotating 1s linear infinite;
      }
    }

    // 状态指示器
    .status-indicator {
      position: absolute;
      top: 6px;
      right: 6px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;

      &.active {
        background-color: #4caf50;
        // box-shadow: 0 0 6px rgba(76, 175, 80, 0.6);
      }
    }

    // 光泽动画
    .btn-shine {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s ease;
    }

    &:hover .btn-shine {
      left: 100%;
    }

    // 加载状态
    &.is-loading {
      pointer-events: none;

      .ai-icon {
        opacity: 0.7;
      }
    }
  }

  // 智能体展开时的按钮位置调整
  &.ai-agent-active {
    right: 15px; // 智能体宽度400px + 20px间距

    // @media (max-width: 1400px) {
    //   right: 38%; // 智能体宽度350px + 20px间距
    // }

    // @media (max-width: 1200px) {
    //   right: 38%; // 智能体宽度320px + 20px间距
    // }

    // @media (max-width: 768px) {
    //   right: 38%; // 移动端保持原位置
    // }
  }

  // 响应式设计
  @media (max-width: 768px) {
    bottom: 60px;
    right: 16px;

    .toggle-btn {
      width: 50px;
      height: 50px;

      .ai-icon {
        width: 30px;
        height: 30px;
      }

      .loading-overlay i {
        font-size: 16px;
      }
    }
  }

  // 动画效果
  &.button-active {
    .toggle-btn {
      animation: pulse 2s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  50% {
    // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 20px rgba(255, 107, 107, 0.3);
  }
  100% {
    // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 主题适配 - 移除themify，使用CSS变量
.ai-agent-toggle-button {
  .toggle-btn {
    &.btn-hide {
      // 使用CSS变量替代themify
      // background: linear-gradient(135deg, var(--color-primary, #409eff) 0%, #764ba2 100%);
    }
  }
}
</style>
