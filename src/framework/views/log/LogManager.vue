<template>
  <div>
    <el-tabs
      ref="tabs"
      type="border-card"
      value="logAccessTab"
      @tab-click="tabClickFn"
    >
      <el-tab-pane label="访问日志" name="logAccessTab">
        <log-access-panel
          :pid="logAccessId"
          :table-height="tableHeight"
          :right-body-height="rightBodyHeight"
        ></log-access-panel>
      </el-tab-pane>
      <el-tab-pane label="登录日志" name="logLoginTab">
        <log-login-panel
          :pid="logLoginId"
          :table-height="tableHeight"
          :right-body-height="rightBodyHeight"
        ></log-login-panel>
      </el-tab-pane>
      <!-- <el-tab-pane label="操作日志" name="logOperatorTab">
        <log-operator-panel
          :pid="logOperatorId"
          :table-height="tableHeight"
          :right-body-height="rightBodyHeight"
        ></log-operator-panel>
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import LogAccessPanel from "./LogAccessPanel";
import LogLoginPanel from "./LogLoginPanel";
import LogOperatorPanel from "./LogOperatorPanel";
export default {
  name: "LogManager",
  components: {
    LogAccessPanel,
    LogLoginPanel,
    LogOperatorPanel,
  },
  created() {},
  computed: {
    ...mapGetters({
      frameStyle: "frameStyle",
    }),
    rightBodyHeight() {
      return `calc(100vh - ${this.frameStyle.headerHeight}px - 50px)`;
    },
    tableHeight() {
      return `calc(100vh - ${this.frameStyle.headerHeight}px - 186px)`;
    },
  },
  data() {
    return {
      logAccessId: "",
      logLoginId: "",
      logOperatorId: "",
    };
  },
  methods: {
    tabClickFn: function (child) {
      this.chooseTabFn(child.name);
      return true;
    },
    chooseTabFn: function (currentName) {
      let pid = new Date().getTime() + "";
      if (currentName == "logAccessTab") {
        this.logAccessId = pid;
        return true;
      }
      if (currentName == "logLoginTab") {
        this.logLoginId = pid;
        return true;
      }
      if (currentName == "logOperatorTab") {
        this.logOperatorId = pid;
        return true;
      }
    },
  },
};
</script>

<style></style>
