<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">基础信息</span>
      <div class="header-right">
        <!-- <el-button
          type="primary"
          size="mini"
          @click="exportBasicWorkOrderInfo"
          v-loading.fullscreen.lock="exportLoading"
          >导出Excel表</el-button
        > -->
      </div>
    </div>
    <div class="content">
      <el-descriptions title="工单基本信息" class="descriptions">
        <el-descriptions-item label="所属专业">{{
          basicWorkOrderData.professionalTypeName
        }}</el-descriptions-item>
        <el-descriptions-item label="网络类型">{{
          basicWorkOrderData.networkTypeName
        }}</el-descriptions-item>

        <el-descriptions-item label="所在区县">{{
          basicWorkOrderData.alarmCity
        }}</el-descriptions-item>
        <!--非动环网IDC显示-->
        <el-descriptions-item
          label="经纬度"
          v-if="
            basicWorkOrderData.professionalType != 4 &&
            basicWorkOrderData.professionalType != 29
          "
          >{{ basicWorkOrderData.stLodeAndLade }}</el-descriptions-item
        >
        <el-descriptions-item label="业务中断">
          {{ basicWorkOrderData.businessDown }}
        </el-descriptions-item>
        <!--        <el-descriptions-item label="预判故障等级">-->
        <!--          {{ basicWorkOrderData.faultLevel }}-->
        <!--        </el-descriptions-item>-->
        <el-descriptions-item label="工单优先级">
          {{ basicWorkOrderData.faultLevel }}
        </el-descriptions-item>
        <!--动环网IDC显示机房信息-->
        <el-descriptions-item
          label="机房名称"
          :span="1"
          v-if="
            (basicWorkOrderData.professionalType == 4 ||
              basicWorkOrderData.professionalType == 29) &&
            basicWorkOrderData.roomName != null &&
            basicWorkOrderData.roomName != '' &&
            basicWorkOrderData.roomName != '无'
          "
        >
          {{ basicWorkOrderData.roomName }}
        </el-descriptions-item>
        <el-descriptions-item
          label="机房ID"
          :span="1"
          v-if="
            (basicWorkOrderData.professionalType == 4 ||
              basicWorkOrderData.professionalType == 29) &&
            basicWorkOrderData.roomId != null &&
            basicWorkOrderData.roomId != '' &&
            basicWorkOrderData.roomId != '无'
          "
        >
          {{ basicWorkOrderData.roomId }}
        </el-descriptions-item>
        <el-descriptions-item
          label="机房类型"
          :span="1"
          v-if="
            (basicWorkOrderData.professionalType == 4 ||
              basicWorkOrderData.professionalType == 29) &&
            basicWorkOrderData.roomType != null &&
            basicWorkOrderData.roomType != '' &&
            basicWorkOrderData.roomType != '无'
          "
        >
          {{ basicWorkOrderData.roomType }}
        </el-descriptions-item>
        <el-descriptions-item
          label="机房等级"
          :span="1"
          v-if="
            (basicWorkOrderData.professionalType == 4 ||
              basicWorkOrderData.professionalType == 29) &&
            basicWorkOrderData.roomLevel != null &&
            basicWorkOrderData.roomLevel != '' &&
            basicWorkOrderData.roomLevel != '无'
          "
        >
          {{ basicWorkOrderData.roomLevel }}
        </el-descriptions-item>
        <el-descriptions-item label="故障现象" :span="3">
          <text-collapse
            :text="return2Br(basicWorkOrderData.faultPhenomenon)"
            :max-lines="2"
          ></text-collapse>
        </el-descriptions-item>
        <!--动环网IDC显示-->
        <el-descriptions-item
          label="经纬度"
          v-if="
            basicWorkOrderData.professionalType == 4 ||
            basicWorkOrderData.professionalType == 29
          "
          >{{ basicWorkOrderData.stLodeAndLade }}</el-descriptions-item
        >
        <!--非动环网IDC显示-->
        <el-descriptions-item label="物业联系人" :span="1">
          {{ basicWorkOrderData.propertyUser }}
        </el-descriptions-item>
        <el-descriptions-item label="物业联系电话" :span="1">
          {{ basicWorkOrderData.propertyPhone }}
        </el-descriptions-item>
        <!-- 无线 -->
        <template v-if="basicWorkOrderData.professionalType == 7">
          <el-descriptions-item label="基站名称" :span="1">{{
            basicWorkOrderData.bsName
          }}</el-descriptions-item>
          <el-descriptions-item label="基站编号" :span="1">{{
            basicWorkOrderData.bsCode
          }}</el-descriptions-item>
          <el-descriptions-item label="规模退服" :span="1">{{
            basicWorkOrderData.scaleService
          }}</el-descriptions-item>
          <el-descriptions-item label="基站地址" :span="1">{{
            basicWorkOrderData.bsAddress
          }}</el-descriptions-item>
          <el-descriptions-item label="基站级别" :span="1">{{
            basicWorkOrderData.bsLevel
          }}</el-descriptions-item>
        </template>
        <!-- 传输 -->
        <template v-if="basicWorkOrderData.professionalType == 3">
          <el-descriptions-item label="端口速率" :span="1">{{
            basicWorkOrderData.portRate
          }}</el-descriptions-item>
          <el-descriptions-item label="原因初步分析" :span="1">{{
            basicWorkOrderData.analysis
          }}</el-descriptions-item>
        </template>
        <!-- 接入网 -->
        <template v-if="basicWorkOrderData.professionalType == 10">
          <el-descriptions-item label="共性问题" :span="1">{{
            basicWorkOrderData.commonProblems
          }}</el-descriptions-item>
        </template>
        <!--        <el-descriptions-item label="工单优先级" :span="1">{{-->
        <!--          basicWorkOrderData.woPriority-->
        <!--          }}</el-descriptions-item>-->
        <!--        无线网-->
        <template v-if="basicWorkOrderData.professionalType == 7">
          <el-descriptions-item label="覆盖场景" :span="1">{{
            basicWorkOrderData.coverScene
          }}</el-descriptions-item>
        </template>
        <el-descriptions-item
          label="附件"
          :span="3"
          v-if="attachmentArr.length > 0"
        >
          <el-tag
            v-for="(item, index) in attachmentArr"
            class="fileName_style"
            :key="index"
            @click="previewAppendixFile(item)"
            v-loading.fullscreen.lock="appendixFileLoading"
            :title="item.name"
            ><div class="text-truncate">{{ item.name }}</div></el-tag
          >
        </el-descriptions-item>
        <el-descriptions-item label="附件" v-else> 无 </el-descriptions-item>
        <el-descriptions-item label="备注" :span="3">{{
          basicWorkOrderData.faultComment
        }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="工单派送信息" class="descriptions">
        <el-descriptions-item label="主送">
          {{ basicWorkOrderData.agentMan }}
        </el-descriptions-item>
        <el-descriptions-item label="次送">
          {{ basicWorkOrderData.acceptMan }}
        </el-descriptions-item>
        <el-descriptions-item label="抄送">
          {{ basicWorkOrderData.copyMan }}
        </el-descriptions-item>
        <el-descriptions-item label="是否通知他人">{{
          basicWorkOrderData.isSendSms == 1 ? "是" : "否"
        }}</el-descriptions-item>
        <el-descriptions-item
          :span="2"
          v-if="basicWorkOrderData.isSendSms == 1"
          label="接收人"
          >{{ basicWorkOrderData.smsToUsername }}</el-descriptions-item
        >
        <el-descriptions-item
          v-if="basicWorkOrderData.isSendSms == 1"
          label="发送内容"
          :span="3"
          >{{ basicWorkOrderData.sendContent }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="impactServiceVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="impactServiceVisible = false"
        :attachmentArr="impactServiceArr"
      ></file-download>
    </el-dialog>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="attachmentVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="attachmentVisible = false"
        :attachmentArr="attachmentArr"
      ></file-download>
    </el-dialog>
    <!-- 使用图片预览组件 -->
    <image-preview
      :visible.sync="imagePreviewVisible"
      :file-data="currentPreviewFile"
      preview-api="/commonDict/attach/download"
      :use-custom-download="true"
      @download="downloadCurrentFile"
      @close="imagePreviewVisible = false"
    ></image-preview>
  </el-card>
</template>

<script>
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";
import {
  apiExportWorkOrder,
  apiDownloadManualFile,
  apifluenceExcel,
  apiFileDownload,
  apiDownloadAppendixFile,
} from "../api/CommonApi";
import ImagePreview from "@plugin/backbone/components/ImagePreview.vue";

export default {
  components: {
    TextCollapse,
    ImagePreview,
  },
  props: {
    basicWorkOrderData: Object,
    woId: String,
    workItemId: [String, Number],
  },
  name: "BaseInfo",
  data() {
    return {
      builderZs: "",
      builderCs: "",
      notifier: "",
      recipient: "",
      exportLoading: false,
      impactServiceArr: [],
      impactServiceVisible: false,
      attachmentArr: [],
      attachmentVisible: false,
      manualFileName: "电路信息",
      OpticCableName: "影响系统信息",
      OpticCableLoading: false,
      manualFileFullscreenLoading: false,
      appendixFileLoading: false,
      professionalType: "",
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewFile: {},
    };
  },
  mounted() {
    if (
      this.basicWorkOrderData.appendixFileUrl &&
      this.basicWorkOrderData.appendixFileUrl != "无"
    ) {
      this.impactServiceArr = JSON.parse(
        this.basicWorkOrderData.appendixFileUrl
      );
    }
    if (
      this.basicWorkOrderData.appendix &&
      this.basicWorkOrderData.appendix != "无"
    ) {
      this.attachmentArr = JSON.parse(this.basicWorkOrderData.appendix);
    }
  },
  methods: {
    exportBasicWorkOrderInfo() {
      this.exportLoading = true;
      let param = {
        woId: this.woId,
        workItemId: this.workItemId,
      };
      apiExportWorkOrder(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("导出成功");
          } else {
            this.$message.error("导出失败");
          }
          this.exportLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("导出失败");
          this.exportLoading = false;
        });
    },
    queryImpactServiceList() {
      this.impactServiceVisible = true;
    },
    queryAttachmentList() {
      this.attachmentVisible = true;
    },
    //电缆附件下载
    downloadOpticCable() {
      this.OpticCableLoading = true;
      let param = {
        param1: JSON.stringify({
          woId: this.woId,
        }),
      };
      apifluenceExcel(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.success("文件下载失败");
          }
          this.OpticCableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.OpticCableLoading = false;
        });
    },

    // },
    downloadManualFile() {
      this.manualFileFullscreenLoading = true;
      let param = {
        param1: JSON.stringify({
          woId: this.woId,
        }),
      };
      apiDownloadManualFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.success("文件下载失败");
          }
          this.manualFileFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.manualFileFullscreenLoading = false;
        });
    },
    // 回车\r\n转为<br/>标签
    return2Br(str) {
      if (str) {
        return str.replaceAll("\\r\\n", "<br/>");
      }
    },
    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },
    // 预览附件文件
    previewAppendixFile(data) {
      // 首先检查文件名是否为图片类型
      const fileName = data.name.toLowerCase();
      const imageExtensions = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".bmp",
        ".webp",
        ".svg",
      ];

      // 检查文件扩展名是否为图片
      const isImage = imageExtensions.some(ext => fileName.endsWith(ext));

      if (!isImage) {
        // 如果不是图片，直接下载
        this.downloadAppendixFile(data);
        return;
      }

      // 如果是图片，保存当前文件信息并显示预览对话框
      this.currentPreviewFile = data;
      this.imagePreviewVisible = true;
    },

    // 下载当前预览的文件
    downloadCurrentFile() {
      this.downloadAppendixFile(this.currentPreviewFile);
    },

    // 下载附件文件
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
::v-deep .el-descriptions-item__content {
  position: relative;
  word-break: break-all;
  padding-right: 8px;
}
::v-deep .el-descriptions-item__cell {
  vertical-align: top;
}
</style>
